using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.Common.Queuing;

/// <summary>
/// An enum defining the queues that can be accessed by CalAccess
///
/// Each enum must define an ExternalId attribute containing the id of the queue in Azure Service Bus.
/// </summary>

public enum QueueName
{
    [ExternalId("efileapi-request-small")]
    EfileSmall,

    [ExternalId("efileapi-request-medium")]
    EfileMedium,

    [ExternalId("efileapi-request-large")]
    EfileLarge,

    [ExternalId("email-request")]
    EmailRequest,

    [ExternalId("sms-request")]
    SmsRequest
}
