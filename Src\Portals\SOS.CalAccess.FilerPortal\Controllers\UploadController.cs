using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Common.FileSystem;

namespace SOS.CalAccess.FilerPortal.Controllers;

[Route("[controller]")]
public class UploadController(IFileSvc blobSvc, IUploadFileSvc uploadFileSvc) : Controller
{
    private const string BlobContainer = "filerportal-uploads";
    private const int MaxFileNameLength = 100;

    /// <summary>
    /// Upload files to Azure blob storage
    /// </summary>
    /// <param name="uploadFiles"></param>
    /// <param name="relationshipId"></param>
    /// <param name="relationshipType"></param>
    /// <returns>
    /// The generated Guid name for the stored blob. The ajax function onUploadSuccess() renames the
    /// uploaded file to the Guid, which is used in the RemoveFiles() action.
    /// </returns>
    [HttpPost("UploadFiles")]
    public async Task<string> UploadFiles(IList<IFormFile> uploadFiles, [FromQuery] long? relationshipId, [FromQuery] string? relationshipType)
    {
        if (!ModelState.IsValid)
        {
            SetErrorResponse("Bad Request", "Invalid ModelState");
            return string.Empty;
        }

        //Initialize a new UploadedFile record
        UploadedFile uploadEntity = new()
        {
            Path = BlobContainer,
            RelationshipId = relationshipId,
            RelationshipType = relationshipType,
            OriginalFileName = ""
        };

        var newFileName = string.Empty;
        try
        {
            foreach (var file in uploadFiles)
            {
                uploadEntity.OriginalFileName = HtmlEncoder.Default.Encode(LimitLength(file.FileName));

                if (file.Length == 0)
                {
                    uploadEntity.UploadedFileStatusId = UploadedFileStatus.Error.Id;
                    SetErrorResponse($"{uploadEntity.OriginalFileName} has no contents", $"Empty file was submitted ({uploadEntity.OriginalFileName})");
                    continue;
                }

                using (var stream = file.OpenReadStream())
                {
                    await blobSvc.CreateFile(BlobContainer, uploadEntity.FileName.ToString(), stream);
                }

                uploadEntity.UploadedFileStatusId = UploadedFileStatus.Complete.Id;
                newFileName = uploadEntity.FileName.ToString();
            }

            return newFileName;
        }
        catch (Exception ex)
        {
            uploadEntity.UploadedFileStatusId = UploadedFileStatus.Error.Id;
            SetErrorResponse($"Failed to upload {uploadEntity.OriginalFileName}", ex.Message);
            return newFileName;
        }
        finally
        {
            //Record outcome in db
            await uploadFileSvc.CreateUploadedFile(uploadEntity);
        }
    }

    /// <summary>
    /// Remove files from Azure blob storage
    /// </summary>
    /// <param name="uploadFiles"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("RemoveFiles")]
    public async Task RemoveFiles(IList<IFormFile> uploadFiles, [FromQuery] long? relationshipId, [FromQuery] string? relationshipType)
    {
        if (!ModelState.IsValid)
        {
            SetErrorResponse("Bad Request", "Invalid ModelState");
            return;
        }

        UploadedFile? entity = null;
        try
        {
            foreach (var filename in uploadFiles.Select(upload => upload.FileName))
            {
                await blobSvc.DeleteBlobAsync(BlobContainer, filename);
                entity = await uploadFileSvc.FindUploadByFileNameGuid(filename);
                if (entity != null)
                {
                    await uploadFileSvc.DeleteUploadedFile(entity);
                }
            }
        }
        catch (Exception ex)
        {
            if (entity != null)
            {
                //Record outcome in db
                entity.UploadedFileStatusId = UploadedFileStatus.Error.Id;
                await uploadFileSvc.UpdateUploadedFile(entity);
            }
            SetErrorResponse($"Failed to remove {entity?.OriginalFileName}", ex.Message);
        }
    }

    private void SetErrorResponse(string reasonPhrase, string errorMessage)
    {
        Response.Clear();
        Response.ContentType = "application/json; charset=utf-8";
        Response.StatusCode = 300;
        Response.HttpContext.Features.Get<IHttpResponseFeature>()!.ReasonPhrase = reasonPhrase;
        Response.Headers.Append("Access-Control-Expose-Headers", "Error");
        Response.Headers.Append("Error", errorMessage);
    }

    private static string LimitLength(string s)
    {
        return new string(s.Take(MaxFileNameLength).ToArray());
    }
}
