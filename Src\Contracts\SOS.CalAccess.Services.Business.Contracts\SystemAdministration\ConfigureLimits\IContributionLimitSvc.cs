using SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits;
#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling configuration of ContributionLimits  operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the contribution data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this service is to facilitate the creation and updating of contribution limits.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA-01: Configure System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                        |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IContributionLimitRepository   | Create                         | Creates a new contribution limit.  |
/// | IContributionLimitRepository   | Update                         | Updates an existing contribution. |
/// | IContributionLimitRepository   | GetAll                          | Get All  contribution Limits. |
/// | IContributionLimitRepository   | Delete                         | Deletes a contribution Limit.      |
/// | IContributionLimitRepository   | FindContributionLimitById      | Retrieves a contribution limit  by ID. |
/// | IContributionLimitRepository   | FindContributionLimitByElectiveOffice | Retrieves a Contribution by ElectiveOffice Id. |
#endregion
public interface IContributionLimitSvc
{

    /// <summary>
    /// Create a contribution Limit
    /// </summary>
    /// <param name="contributionLimit">contribution limit dto</param>
    /// <returns> Contribution limut</returns>
    /// \msc
    /// Actor, IContribiutionLimitSvc [label="IContribiution\nLimitSvc"], IContributionLimitRepositorySvc [label="IContribution\nLimitRepositorySvc"];
    /// Actor => IContribiutionLimitSvc [label="CreateContributionLimit()"];
    /// IContribiutionLimitSvc => IContributionLimitRepositorySvc [label="Create()"];
    /// IContributionLimitRepositorySvc >> IContribiutionLimitSvc [label="\nreturn ContributionLimit"];
    /// IContribiutionLimitSvc >> Actor [label="\nreturn ContributionLimitDto"];
    /// \endmsc
    public Task<ContributionLimitDto> CreateContributionLimit(ContributionLimitDto contributionLimit);


    /// <summary>
    /// Updates an existing contribution limit.
    /// </summary>
    /// <param name="contributionLimit">The contribution limit DTO containing updated information.</param>
    /// <returns>updated contribution limit DTO.</returns>
    /// \msc
    /// Actor, IContribiutionLimitSvc [label="IContribiutionLimitSvc"], IContributionLimitRepositorySvc [label="IContributionLimitRepositorySvc"];
    /// Actor => IContribiutionLimitSvc [label="UpdateContributionLimit()"];
    /// IContribiutionLimitSvc => IContributionLimitRepositorySvc [label="Update()"];
    /// IContributionLimitRepositorySvc >> IContribiutionLimitSvc [label="\nreturn ContributionLimit"];
    /// IContribiutionLimitSvc >> Actor [label="\nreturn ContributionLimitDto"];
    /// \endmsc
    public Task<ContributionLimitDto> UpdateContributionLimit(ContributionLimitDto contributionLimit);

    /// <summary>
    /// Delete a contribution limit 
    /// </summary>
    /// <param name="contributionLimit">Contribution limit</param>
    /// <returns>bool</returns>
    /// \msc
    /// Actor, IContribiutionLimitSvc [label="IContribiutionLimitSvc"], IContributionLimitRepositorySvc [label="IContributionLimitRepositorySvc"];
    /// Actor => IContribiutionLimitSvc [label="DeleteContributionLimit()"];
    /// IContribiutionLimitSvc => IContributionLimitRepositorySvc [label="Delete()"];
    /// IContributionLimitRepositorySvc >> IContribiutionLimitSvc [label="return bool"];
    /// IContribiutionLimitSvc >> Actor [label="return bool"];
    /// \endmsc
    public Task<bool> DeleteContributionLimit(ContributionLimitDto contributionLimit);

    /// <summary>
    /// Retrieve a list of contribution limits
    /// </summary>
    /// <returns>List of Contribution Limit DTOs</returns>
    /// \msc
    /// Actor, IContributionLimitSvc [label="IContribution\nLimitSvc"], IContributionLimitRepositorySvc [label="IContribution\nLimitRepositorySvc"];
    /// Actor => IContributionLimitSvc [label="GetContributionLimits()"];
    /// IContributionLimitSvc => IContributionLimitRepositorySvc [label="GetAll()"];
    /// IContributionLimitRepositorySvc >> IContributionLimitSvc [label="\nreturn IEnumerable<ContributionLimit>"];
    /// IContributionLimitSvc >> Actor [label="\nreturn IEnumerable<ContributionLimitDto>"];
    /// \endmsc
    public Task<IEnumerable<ContributionLimitDto>> GetContributionLimits();

    /// <summary>
    /// Find a Contribution Limit by ID
    /// </summary>
    /// <param name="id">Contribution Limit Id</param>
    /// <returns>ContributionLimitDto</returns>
    /// \msc
    /// Actor, IContributionLimitSvc [label="IContribution\nLimitSvc"], IContributionLimitRepositorySvc [label="IContribution\nLimitRepositorySvc"], IDonorLimitRepository [label="IDonor\nLimitRepository"];
    /// Actor => IContributionLimitSvc [label="FindContributionLimitById(id)"];
    /// IContributionLimitSvc => IContributionLimitRepositorySvc [label="FindById(id)"];
    /// IContributionLimitRepositorySvc >> IContributionLimitSvc [label="\nreturn ContributionLimit"];
    /// IContributionLimitSvc => IDonorLimitRepository [label="FindByContributionId(id)"];
    /// IDonorLimitRepository >> IContributionLimitSvc [label="\nreturn IEnumerable<DonorLimit>"];
    /// IContributionLimitSvc >> Actor [label="\nreturn ContributionLimitDto"];
    /// \endmsc
    public Task<ContributionLimitDto> FindContributionLimitById(long id);

    /// <summary>
    /// Finds all contribution limits associated with a specific elective office.
    /// </summary>
    /// <param name="electiveOfficeId">The ID of the elective office.</param>
    /// <returns>A collection of contribution limit DTOs related to the specified office.</returns>
    /// \msc
    /// Actor, IContributionLimitSvc, IContributionLimitRepositorySvc;
    /// Actor => IContributionLimitSvc [label="FindContributionLimitByElectiveOffice()"];
    /// IContributionLimitSvc => IContributionLimitRepositorySvc [label="FindByElectiveOfficeId()"];
    /// IContributionLimitRepositorySvc >> IContributionLimitSvc [label="\nreturn IEnumerable<ContributionLimit>"];
    /// IContributionLimitSvc >> Actor [label="\nreturn IEnumerable<ContributionLimitDto>"];
    /// \endmsc
    IEnumerable<ContributionLimitDto> FindContributionLimitByElectiveOffice(long electiveOfficeId);

}
