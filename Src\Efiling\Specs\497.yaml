openapi: 3.0.3
info:
  title: Form 497 - 24-hour/10-day Contribution Report API
  description: API for submitting and managing Form 497 contribution reports.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/ContributionReport/{filerId}:
    post:
      summary: 497 Contribution Report
      description: Submit a Form 497 contribution report including details about contributions made or received.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filerInformation:
                  $ref: '#/components/schemas/FilerInformation'
                contributionsReceived:
                  type: array
                  items:
                    $ref: '#/components/schemas/ContributionReceived'
                  description: List of contributions received.
                contributionsMade:
                  type: array
                  items:
                    $ref: '#/components/schemas/ContributionMade'
                  description: List of contributions made.
                amendment:
                  $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    FilerInformation:
      type: object
      properties:
        name:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        phone:
          type: string
          description: Contact phone number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        idNumber:
          type: string
          description: Filer's identification number (if applicable).
        date:
          type: string
          format: date
          description: Date of this filing
        reportNumber:
          type: string
          description: Report number
      required:
        - name
        - address
        - date
        - reportNumber

    ContributorDetails:
      type: object
      properties:
        name:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        idNumber:
          type: string
          description: Identification number of the contributor (if applicable).
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        occupation:
          type: string
          description: Contributor's occupation (if an individual).
        employer:
          type: string
          description: Employer name (if self-employed, include business name).
        businessName:
          type: string
          description: Business name for self-employed individuals.
        contributorCode:
          type: string
          # enum:
          #   - IND
          #   - COM
          #   - OTH
          #   - PTY
          #   - SCC
          description: Contributor type. Values are IND (Individual), COM (Committee), OTH (Other), PTY (Political Party), SCC (Small Contributor Committee).
      required:
          - name
          - address
          - contributorCode

    ContributionReceived:
      type: object
      properties:
        dateReceived:
          type: string
          format: date
          description: Date the contribution was received.
        amount:
          type: number
          format: float
          description: Amount of the contribution received.
        isLoan:
          type: boolean
          description: Indicates if the contribution was a loan.
        interestRate:
          type: number
          format: float
          description: Interest rate for the loan (if applicable).
        contributorDetails:
          $ref: '#/components/schemas/ContributorDetails'
      required:
          - dateReceived
          - amount
          - contributorDetails

    ContributionMade:
      type: object
      properties:
        dateMade:
          type: string
          format: date
          description: Date the contribution was made.
        name:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        amount:
          type: number
          format: float
          description: Amount of the contribution made.
        dateOfElection:
          type: string
          format: date
          description: Date of the election associated with the contribution.
        candidateFirstName:
          type: string
          description: First name of the candidate (if applicable).
        candidateLastName:
          type: string
          description: Last name of the candidate (if applicable).
        ballotMeasure:
          type: string
          description: Name of the ballot measure (if applicable).
        officeSought:
          $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
        recipientDetails:
          $ref: '#/components/schemas/recipientDetails'
      required:
          - dateMade
          - amount
          - dateOfElection

    recipientDetails:
      type: object
      properties:
        name:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        idNumber:
          type: string
          description: Identification number of the recipient.
