using SOS.CalAccess.Models.FilerRegistration.Lobbyists;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Lobbyists;

#region Design Notes
/// <summary>
/// Interface for the Lobbyist Service.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Lobbyist Service. The service is responsible for managing lobbying-related data, including clients, employer group members, and firms.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating lobbying data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the retrieval of lobbying-related data, including clients, employer group members, and firms.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                                 | Operation            | Description                  |
/// | --------------------------------------- | -------------------- | ---------------------------- |
/// | ILobbyingClientRepository               | GetAll               | Retrieves all lobbying clients |
/// | ILobbyingEmployerGroupMemberRepository  | GetAll               | Retrieves all lobbying employer group members |
/// | ILobbyingFirmRepository                 | GetAll               | Retrieves all lobbying firms  |
/// | ILobbyingInterestRepository             | GetAll               | Retrieves all lobbying interests |
/// | IUnregisteredLobbyingFirmRepository     | GetAll               | Retrieves all unregistered lobbying firms |
#endregion

/// <summary>
///
/// </summary>
public interface ILobbyistSvc
{
    /// <summary>
    /// Gets all lobbying clients.
    /// </summary>
    /// <returns>A collection of all lobbying clients.</returns>
    ///
    /// \msc
    /// Actor, ILobbyistSvc [label="ILobbyist \n Svc"], ILobbyingClientRepository [label="ILobbying \n Client \n Repository"];
    /// Actor => ILobbyistSvc [label="GetAllLobbyingClients()"];
    /// ILobbyistSvc => ILobbyingClientRepository [label="GetAll()"];
    /// ILobbyingClientRepository >> ILobbyistSvc [label="return IEnumerable<LobbyingClient>"];
    /// ILobbyistSvc >> Actor [label="return IEnumerable<LobbyingClient>"];
    /// \endmsc
    public Task<IEnumerable<LobbyingClient>> GetAllLobbyingClients();

    /// <summary>
    /// Gets all lobbying employer group members.
    /// </summary>
    /// <returns>A collection of all lobbying employer group members.</returns>
    ///
    /// \msc
    /// Actor, ILobbyistSvc [label="ILobbyist \n Svc"], ILobbyingEmployerGroupMemberRepository [label="ILobbying \n Employer \n Group \n Member \n Repository"];
    /// Actor => ILobbyistSvc [label="GetAllLobbyingEmployerGroupMembers()"];
    /// ILobbyistSvc => ILobbyingEmployerGroupMemberRepository [label="GetAll()"];
    /// ILobbyingEmployerGroupMemberRepository >> ILobbyistSvc [label="return IEnumerable<LobbyingEmployerGroupMember>"];
    /// ILobbyistSvc >> Actor [label="return IEnumerable<LobbyingEmployerGroupMember>"];
    /// \endmsc
    public Task<IEnumerable<LobbyingEmployerGroupMember>> GetAllLobbyingEmployerGroupMembers();

    /// <summary>
    /// Gets all lobbying firms.
    /// </summary>
    /// <returns>A collection of all lobbying firms.</returns>
    ///
    /// \msc
    /// Actor, ILobbyistSvc [label="ILobbyist \n Svc"], ILobbyingFirmRepository [label="ILobbying \n Firm \n Repository"];
    /// Actor => ILobbyistSvc [label="GetAllLobbyingFirms()"];
    /// ILobbyistSvc => ILobbyingFirmRepository [label="GetAll()"];
    /// ILobbyingFirmRepository >> ILobbyistSvc [label="return IEnumerable<LobbyingFirm>"];
    /// ILobbyistSvc >> Actor [label="return IEnumerable<LobbyingFirm>"];
    /// \endmsc
    public Task<IEnumerable<LobbyingFirm>> GetAllLobbyingFirms();

    /// <summary>
    /// Gets all lobbying interests.
    /// </summary>
    /// <returns>A collection of all lobbying interests.</returns>
    ///
    /// \msc
    /// Actor, ILobbyistSvc [label="ILobbyist \n Svc"], ILobbyingInterestRepository [label="ILobbying \n Interest \n Repository"];
    /// Actor => ILobbyistSvc [label="GetAllLobbyingInterests()"];
    /// ILobbyistSvc => ILobbyingInterestRepository [label="GetAll()"];
    /// ILobbyingInterestRepository >> ILobbyistSvc [label="return IEnumerable<LobbyingInterest>"];
    /// ILobbyistSvc >> Actor [label="return IEnumerable<LobbyingInterest>"];
    /// \endmsc
    public Task<IEnumerable<LobbyingInterest>> GetAllLobbyingInterests();

    /// <summary>
    /// Gets all unregistered lobbying firms.
    /// </summary>
    /// <returns>A collection of all unregistered lobbying firms.</returns>
    ///
    /// \msc
    /// Actor, ILobbyistSvc [label="ILobbyist \n Svc"], IUnregisteredLobbyingFirmRepository [label="IUnregistered \n Lobbying \n Firm \n Repository"];
    /// Actor => ILobbyistSvc [label="GetAllUnregisteredLobbyingFirms()"];
    /// ILobbyistSvc => IUnregisteredLobbyingFirmRepository [label="GetAll()"];
    /// IUnregisteredLobbyingFirmRepository >> ILobbyistSvc [label="return IEnumerable<UnregisteredLobbyingFirm>"];
    /// ILobbyistSvc >> Actor [label="return IEnumerable<UnregisteredLobbyingFirm>"];
    /// \endmsc
    public Task<IEnumerable<UnregisteredLobbyingFirm>> GetAllUnregisteredLobbyingFirms();
}
