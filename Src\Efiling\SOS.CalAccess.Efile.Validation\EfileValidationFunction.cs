using System.Net;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Common.DataValidation;
using SOS.CalAccess.Services.Common.DataValidation.Models;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace SOS.CalAccess.Efile.Validation;

/// <h4>Design Description</h4>
/// <p>
/// HTTP triggered Azure Function to accept incoming requests from third party software vendors to file registration and disclosure forms.
/// </p>
/// <p>
/// Requests may be very large so should not be loaded into server memory in their entirety.
/// </p>
/// <p>
/// First the function will verify that the third party vendor is authorized to submit the requested type of form
/// and that they are authorized to submit filings on behalf of the specified filer.
/// Both of the those pieces of information are contained in the URL path of the request
/// and the identity of the third party vendor is obtained from the ClaimsPrincipal generated by the authentication process.
/// </p>
/// <p>
/// The function then creates an api request record to document that the request was received and generate a unique submission id.
/// </p>
/// <p>
/// Next the function uses the IFileSvc to create a new file that will contain the body of the request and setup an output file stream to write to it.
/// The file should be named using the unique submission id to prevent any possible collisions with existing files.
/// </p>
/// <p>
/// Then the function will determine the type of request from the URL parameters and load the expected schema of the request object
/// and perform a schema validation using streaming methods of the the IJsonSchemaValidationSvc and passing the file stream as the outputStream.
/// This will read a portion of the request at a time, validate that it meets the schema requirements, and then write the portion to the file.
/// </p>
/// <p>
/// If schema validation was successful the function will queue the request for asynchronous processing.
/// </p>
/// <p>
/// Finally the function will return a JSON response indicating if the request was accepted and the unique id assigned to it or a list of validation errors if it was rejected.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Allow third party software vendors to file registration and disclosure forms
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>AP-01</li>
/// <li>AP-02</li>
/// <li>AP-03</li>
/// <li>AP-04</li>
/// <li>AP-05</li>
/// <li>AP-06</li>
/// <li>AP-07</li>
/// <li>AP-08</li>
/// <li>AP-09</li>
/// <li>AP-10</li>
/// <li>AP-11</li>
/// <li>AP-12</li>
/// <li>AP-13</li>
/// <li>AP-14</li>
/// <li>AP-15</li>
/// <li>AP-16</li>
/// <li>AP-17</li>
/// <li>AP-18</li>
/// <li>AP-19</li>
/// <li>AP-20</li>
/// <li>AP-21</li>
/// <li>AP-22</li>
/// <li>AP-23</li>
/// <li>AP-24</li>
/// <li>AP-25</li>
/// <li>AP-26</li>
/// <li>AP-27</li>
/// <li>AP-28</li>
/// <li>AP-29</li>
/// <li>AP-30</li>
/// <li>AP-31</li>
/// <li>AP-32</li>
/// <li>AP-33</li>
/// <li>AP-34</li>
/// <li>AP-35</li>
/// <li>AP-36</li>
/// <li>AP-37</li>
/// <li>AP-38</li>
/// <li>AP-39</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IAuthorizationSvc              | VerifyAuthorization()          | Verify that the user is authorized to perform the requested action |
/// | IApiRequestSvc                 | CreateNewApiRequest()          | Insert a new Api Request record in the database |
/// | IFileSvc                       | CreateFileStream()             | Create a Stream to write to a new file |
/// | IJsonSchemaValidationSvc       | ValidateJsonStream()           | Validate that the input stream contains json matching a defined schema and copy to the output stream |
/// | IApiRequestSvc                 | QueueApiRequestForProcessing() | Update status and queue request for processing |
public class EfileValidationFunction
{
    #region Properties

    /// <summary>
    /// Message shown when authentication fails. Encountering this specific message in the main catch
    /// block will result in a Forbidden (HTTP 403) response. 
    /// </summary>
    private const string AuthenticationErrMsg = @"Failed to extract the EntraObjectId from the authorization header.";

    /// <summary>
    /// Reference to settings in either settings.json or local.settings.json, depending
    /// on the runtime environment.
    /// </summary>
    private readonly IConfiguration _configuration;

    /// <summary>
    /// Combines multiple dependencies for Efile Validation into a single object
    /// </summary>
    private readonly IEfileValidationDependencies _dependencies;

    /// <summary>
    /// List of errors encountered during validation
    /// </summary>
    private readonly HashSet<string> _errors = new();

    /// <summary>
    /// An API service used for validating the JSON schema
    /// </summary>
    private readonly IJsonSchemaValidationSvc _jsonValidationSvc;

    /// <summary>
    /// An API service used for validating the amendment
    /// </summary>
    private readonly IEfileValidationsSvc _efileValidationsSvc;

    /// <summary>
    /// Event/error logging utility
    /// </summary>
    private readonly ILogger<EfileValidationFunction> _logger;

    private readonly IEfileValidationFunctionCommonMethods _efileCommonMethods;

    private readonly IEnumerable<IFormValidator> _validators;

    private static readonly HashSet<string> _formsToSkipFilerValidation = new()
{
    FormNameToIdMapping.CampaignStatementCandidateIntention,
    FormNameToIdMapping.CampaignRegistrationSlateMailer,
    FormNameToIdMapping.LobbyingCertificationLobbyist,
    FormNameToIdMapping.CampaignTerminationSlateMailer
};
    #endregion

    /// <summary>
    /// Dependency Injection setup
    /// </summary>
    /// <param name="configuration">
    /// Optional. Can be loaded directly by D.I., a constructor in unit tests. Falls back
    /// to Initialize Configuration if this param is not provided.
    /// </param>
    /// <param name="logger">Used only for debugging. This logs at Information/Error levels only.</param>
    /// <param name="httpClientFactory">Used for efficient creation of HttpClient objects</param>
    /// <param name="jsonValidationSvc">Service for validating the incoming JSON schemas</param>
    /// <exception cref="ArgumentNullException">Thrown if any required dependency is null</exception>
    public EfileValidationFunction(
        IConfiguration? configuration,
        ILogger<EfileValidationFunction>? logger,
        IEfileValidationDependencies? dependencies,
        IJsonSchemaValidationSvc? jsonValidationSvc,
        IEfileValidationsSvc? efileValidationsSvc,
        IEfileValidationFunctionCommonMethods? efileCommonMethods,
        IEnumerable<IFormValidator> validators)
    {
        // Dependency setup 
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dependencies = dependencies ?? throw new ArgumentNullException(nameof(dependencies));
        _jsonValidationSvc = jsonValidationSvc ?? throw new ArgumentNullException(nameof(jsonValidationSvc));
        _efileValidationsSvc = efileValidationsSvc ?? throw new ArgumentNullException(nameof(efileValidationsSvc));
        _efileCommonMethods = efileCommonMethods ?? throw new ArgumentNullException(nameof(efileCommonMethods));
        _validators = validators ?? throw new ArgumentNullException(nameof(validators));

        // Configuration setup
        _configuration = configuration ?? _efileCommonMethods.InitializeConfiguration();
        _efileCommonMethods.ValidateSettings();

    }

    /// <summary>
    /// Accept incoming requests from third party software vendors to file registration and disclosure forms and queue for asynchronous processing
    /// </summary>
    /// <param name="req"></param>
    /// <param name="formNameAndFilerId"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, EfileValidationFunction, IApiRequestSvc, IFileSvc, IJsonSchemaValidationSvc;
    /// Actor => EfileValidationFunction [label="Run()"];
    /// EfileValidationFunction => IAuthorizationSvc [label="VerifyAuthorization()"];
    /// IAuthorizationSvc => EfileValidationFunction [label="return"];
    /// EfileValidationFunction => IApiRequestSvc [label="CreateNewApiRequest()"];
    /// IApiRequestSvc => EfileValidationFunction [label="return"];
    /// EfileValidationFunction => IFileSvc [label="CreateFileStream()"];
    /// IFileSvc => EfileValidationFunction [label="return"];
    /// EfileValidationFunction => IJsonSchemaValidationSvc [label="ValidateJsonStream()"];
    /// IJsonSchemaValidationSvc => EfileValidationFunction [label="return"];
    /// IApiRequestSvc note IJsonSchemaValidationSvc [label="ALT - If schema validation succeeded", textbgcolor="#ffb000"];
    /// EfileValidationFunction => IApiRequestSvc [label="QueueApiRequestForProcessing()"];
    /// IApiRequestSvc => EfileValidationFunction [label="return"];
    /// IApiRequestSvc note IJsonSchemaValidationSvc [label="END ALT", textbgcolor="#ffb000"];
    /// IApiRequestSvc note IJsonSchemaValidationSvc [label="ALT - If schema validation failed", textbgcolor="#ffb000"];
    /// EfileValidationFunction => IApiRequestSvc [label="InvalidateRequest()"];
    /// IApiRequestSvc => EfileValidationFunction [label="return"];
    /// IApiRequestSvc note IJsonSchemaValidationSvc [label="END ALT", textbgcolor="#ffb000"];
    /// EfileValidationFunction >> Actor [label="return"];
    /// \endmsc
    [Function("EfileValidationFunction")]
    [OpenApiOperation(operationId: "EfileValidation", tags: new[] { "Efile" }, Summary = "Validates E-File submission", Description = "Performs validation on the provided api submission.")]
    [OpenApiParameter(name: "formNameAndFilerId", In = ParameterLocation.Path, Required = true, Type = typeof(string),
        Summary = "form name and filerId", Description = "Name of the form to validate and the ID of the filter, if present")]
    [OpenApiRequestBody("application/json", typeof(string),
        Required = true, Description = "JSON payload for the request")]
    [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(JsonSchemaValidationResult), Summary = "Validation Result", Description = "Returns the result of the api request status.")]
    public async Task<IActionResult> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "EfileValidationFunction/{*formNameAndFilerId}")] HttpRequest req,
        string formNameAndFilerId)
    {
        var (formName, filerId) = _efileCommonMethods.ParseFormAndFilerId(formNameAndFilerId);

        return await HandleRequest(req, formName, filerId);
    }

    /// <summary>
    /// Same as Run() above, but more friendly for unit testing as it
    /// can be called directly without making an actual HTTP request that includes the filerID.
    /// </summary>
    /// <param name="req">The original incoming HTTP request</param>
    /// <param name="formName">Name of the form being eFiled (eg. "Form510" or "Form400")</param>
    /// <param name="filerId">Filer ID obtained from the request URL, or provided directly by unit tests</param>
    /// <returns>OkObjectResult or BadRequestObjectResult. Always includes JSON with additional information.</returns>

    public async Task<IActionResult> HandleRequest(HttpRequest req, string formName, string? filerId = null)
    {
        _logger.LogInformation("EfileValidationFunction() called");
        try
        {
            long userId = 10000;

            //validate formname
            _efileCommonMethods.ValidateFormName(formName);

            //validate filerId
            if (!_formsToSkipFilerValidation.Contains(formName))
            {
                await _efileValidationsSvc.FilerIdValidation(filerId);
            }

            //validate Json
            string requestBody = await ReadRequestBody(req);
            var validationErrors = await _jsonValidationSvc.ValidateJson(requestBody, formName);
            JObject jsonData = JObject.Parse(requestBody);

            //validate Amendment
            await _efileValidationsSvc.AmendmentValidation(jsonData);

            //validations on respective form
            var validator = _validators
           .SingleOrDefault(v => v.CanHandle(formName))
           ?? throw new InvalidOperationException($"No validator registered for form '{formName}'");


            int transactionCount = await validator.ValidateAsync(jsonData);

            // set path
            _efileCommonMethods.NormalizeUndefinedPath(req);

            string blobFilename = _efileCommonMethods.CreateBlobFilename(formName);
            string decodedPath = WebUtility.UrlDecode(req.Path);

            //local api request
            var newApiRequest = _efileCommonMethods.CreateLocalApiRequest(decodedPath, filerId, jsonData, blobFilename, userId);

            //send api request to Efile API
            using var createResponse = await _efileCommonMethods.SendApiRequestToEfileApi(newApiRequest);

            //validate response from Efile API
            if (!createResponse.IsSuccessStatusCode)
            {
                foreach (var e in await _efileCommonMethods.GetApiErrorsFromResponse(createResponse))
                {
                    _ = _errors.Add(e);
                }

                throw new InvalidOperationException($"HTTP request error: {createResponse.StatusCode}");
            }

            var createdJson = await createResponse.Content.ReadAsStringAsync();
            var createdDbObj = JsonConvert.DeserializeObject<ApiRequest>(createdJson)
                ?? throw new InvalidOperationException("Unable to retrieve the newly created ApiRequest database object.");

            // Json Validation errors
            if (validationErrors.Count > 0)
            {
                foreach (var error in validationErrors)
                {
                    _ = _errors.Add($"PROPERTY: {error.ApiErrorField}, ERROR MESSAGE: {error.ApiErrorDescription}");
                }

                _ = await _efileCommonMethods.SendInvalidateApiRequestToEfileApi(createdDbObj, validationErrors);
                throw new InvalidOperationException("Validation errors detected.");
            }

            //Creates file in Blob storage
            var containerName = _configuration["AzureValues:BlobStorageContainerName"] ?? "";
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            await _dependencies.FileSvc.CreateFile(containerName, blobFilename, stream, CancellationToken.None);

            //Update status in ApiRequest table to Queued
            using var queuedResponse = await _efileCommonMethods.SetRequestStateToQueued(createdDbObj);

            // sets the queue size and sent to Processing queue 
            await _efileCommonMethods.QueueTheRequest(createdDbObj, transactionCount);

            var queuedJson = await queuedResponse.Content.ReadAsStringAsync();
            var queuedDbObj = JsonConvert.DeserializeObject<ApiRequest>(queuedJson)
                ?? throw new InvalidOperationException("Unable to retrieve the updated ApiRequest database object.");

            _logger.LogInformation("EfileValidationFunction() finished successfully");

            // validation func passed
            return new OkObjectResult(new { message = "success", SubmissionId = (long)queuedDbObj.Id });
        }
        catch (Exception ex)
        {
            return HandleFailure(ex);
        }
    }


    #region Private Methods

    /// <summary>
    /// Handle any exception during the processing of the main logic path of Efile Validation. Returns
    /// BadRequest (HTTP 400) under most circumstances. However, if error occurred while reading auth
    /// header then a Forbidden (HTTP 403) is returned instead.
    /// </summary>
    /// <param name="ex">Exception object from HandleRequest()</param>
    private IActionResult HandleFailure(Exception ex)
    {
        _errors.Add(ex.GetBaseException().Message);
        _logger.LogError(ex, "EfileValidationFunction() finished with error(s)");
        var errorResponse = new
        {
            message = "Error(s) encountered",
            errors = _errors
        };

        return ex.Message == AuthenticationErrMsg
            ? new ObjectResult(errorResponse) { StatusCode = StatusCodes.Status403Forbidden }
            : new BadRequestObjectResult(errorResponse);
    }

    /// <summary>
    /// Read the request body and return as a string. We're expecting this to be a serialized JSON
    /// string.
    /// </summary>
    /// <param name="request">Original HTTP request to the Azure Function</param>
    /// <returns>Entire request body as a string</returns>
    /// <exception cref="BadHttpRequestException">Thrown if body is empty</exception>
    private static async Task<string> ReadRequestBody(HttpRequest request)
    {
        string requestBody;
        try
        {
            using var requestBodyReader = new StreamReader(request.Body);
            requestBody = await requestBodyReader.ReadToEndAsync();
        }
        catch (Exception ex) when (ex is ArgumentException or InvalidOperationException or ObjectDisposedException)
        {
            throw new BadHttpRequestException($"Unable to read request body ({ex.GetType().Name})");
        }

        if (string.IsNullOrWhiteSpace(requestBody))
        {
            throw new BadHttpRequestException("Request body is empty");
        }

        return requestBody;
    }

    #endregion
}
