using Refit;
using SOS.CalAccess.Models.Authorization;

namespace SOS.CalAccess.Services.Technical.SystemAdministration;
public interface IFilerRoleMaintenanceSvc
{
    const string ListRolesPath = "api/FilerRole/ListRoles";
    const string AddRolePath = "api/FilerRole/AddRole";
    const string ViewRolePath = "api/FilerRole/ViewRole";
    const string UpdateRolePath = "api/FilerRole/UpdateRole";
    const string ListPermissionsPath = "api/FilerRole/ListPermissions";
    const string GetPermissionsPath = "api/FilerRole/GetFilerRolePermissionsByRoleId";
    const string RemovePermissionsPath = "api/FilerRole/RemoveFilerRolePermissions";
    const string RemoveRolePath = "api/FilerRole/RemoveRole";

    /// <summary>
    /// Gets list of roles
    /// </summary>
    /// <returns>Task<IEnumerable<Role>></returns>
    /// 
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="ListRoles()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="GetAll()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + ListRolesPath)]
    Task<IEnumerable<MaintainFilerRoleDto>> ListRoles();

    /// <summary>
    /// View role permissions of user account
    /// </summary>
    /// <param name="roleId"></param>
    /// <returns>Task<role></returns>
    /// 
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="ViewRole()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="GetById"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + ViewRolePath)]
    Task<MaintainFilerRoleDto> ViewRole(long roleId);

    /// <summary>
    /// Add role permissions to user account
    /// </summary>
    /// <param name="role"></param>
    /// 
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="AddRole()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="Create()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + AddRolePath)]
    Task AddRole(MaintainFilerRoleDto role);

    /// <summary>
    /// Update role permissions 
    /// </summary>
    /// <param name="role"></param>
    /// 
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="UpdateRole()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="Update()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + UpdateRolePath)]
    Task UpdateRole(MaintainFilerRoleDto role);

    /// <summary>
    /// Get authorization role permissions by role id
    /// </summary>
    /// <param name="roleId"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="GetFilerRolePermissionsByRoleId()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="GetFilerRolePermissionsByRoleId()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + GetPermissionsPath)]
    Task<List<FilerRolePermission>> GetFilerRolePermissionsByRoleId(long roleId);

    /// <summary>
    /// Remove authorization role permissions
    /// </summary>
    /// <param name="maintainFilerRoleDto"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="RemoveFilerRolePermissions()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="RemoveFilerRolePermissions()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + RemovePermissionsPath)]
    Task RemoveFilerRolePermissions(MaintainFilerRoleDto maintainFilerRoleDto);

    /// <summary>
    /// Remove the filer role if not assigned to any filer
    /// </summary>
    /// 
    /// \msc
    /// Actor, IFilerRoleMaintenanceSvc [label="IFilerRole \n MaintenanceSvc"], IFilerRoleRepository [label="IFilerRole \n Repository"];
    /// Actor => IFilerRoleMaintenanceSvc [label="RemoveRole()"];
    /// IFilerRoleMaintenanceSvc => IFilerRoleRepository [label="Delete()"];
    /// IFilerRoleRepository >> IFilerRoleMaintenanceSvc [label="\nreturn "];
    /// IFilerRoleMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + RemoveRolePath)]
    Task<bool> RemoveRole(long roleId);

}

