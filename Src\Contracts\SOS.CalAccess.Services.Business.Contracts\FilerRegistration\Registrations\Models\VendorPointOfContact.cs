using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for Vendor Registration VendorPointOfContact
/// </summary>
public class VendorPointOfContact
{
    [Required]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the Vendor Point of Contact  email.
    /// </summary>
    [Required]
    public required string Email { get; set; }

    /// <summary>
    /// Gets or sets the Vendor Point of Contact  phone.
    /// </summary>
    [Required]
    public required PhoneNumberDto Phone { get; set; }

    [Required]
    //Ask them for an IP address so that it can be white listed.
    public required string IPAddress { get; set; }
}


