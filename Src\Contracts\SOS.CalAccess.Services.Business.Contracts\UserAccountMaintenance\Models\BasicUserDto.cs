using System.Text.Json.Serialization;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

[method: JsonConstructor]
public record BasicUserDto(long Id, string Email, string FirstName, string LastName)
{
    /// <summary>
    /// Initializes a new instance of the <see cref="BasicUserDto"/> class.
    /// </summary>
    /// <param name="user">The <see cref="User"/> instance to use as source for data.</param>
    public BasicUserDto(User user)
        : this(user.Id, user.EmailAddress, user.FirstName, user.LastName)
    {
    }
}
