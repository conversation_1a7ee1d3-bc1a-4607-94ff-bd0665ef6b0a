using System.Collections.Generic;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
/// <summary>
/// Candidate Intention Statement (CIS) Registration Withdrawal DTO.
/// </summary>
public class CisRegistrationWithdrawalDto
{
    /// <summary>
    /// Gets or sets the Candidate.
    /// </summary>
    public CandidateDto? Candidate { get; set; }

    /// <summary>
    /// Gets or sets the Addresses.
    /// </summary>
    public List<AddressDto>? Addresses { get; set; }

    /// <summary>
    /// Gets or sets the Office Sought by the Candidate.
    /// </summary>
    public string? ElectionOfficeSought { get; set; }

    /// <summary>
    /// Gets or sets a reference to the Election Race that this registration participates on.
    /// </summary>
    public ElectionRace? ElectionRace { get; set; }

    /// <summary>
    /// Gets or sets the ExecutedOn.
    /// </summary>
    public DateTime? ExecutedOn { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="CisRegistrationWithdrawalDto"/> class.
    /// </summary>
    public CisRegistrationWithdrawalDto() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="CisRegistrationWithdrawalDto"/> class
    /// using the provided registration entity.
    /// </summary>
    /// <param name="registration">The registration entity.</param>
    public CisRegistrationWithdrawalDto(CandidateIntentionStatement registration)
    {
        ArgumentNullException.ThrowIfNull(registration);
        ArgumentNullException.ThrowIfNull(registration.Candidate);

        Candidate = new CandidateDto(registration.Candidate);
        Addresses = new List<AddressDto>(registration.AddressList!.Addresses.Select(a => new AddressDto(a)));
        ElectionOfficeSought = registration!.ElectionOfficeSought;
        ElectionRace = registration!.ElectionRace;
        ExecutedOn = registration.SubmittedAt;
    }
}

