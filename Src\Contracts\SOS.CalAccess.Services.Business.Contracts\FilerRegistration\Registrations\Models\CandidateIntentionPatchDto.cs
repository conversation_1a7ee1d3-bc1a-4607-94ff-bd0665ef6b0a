
namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
public class CandidateIntentionPatchDto
{
    /// <summary>
    /// Status request.
    /// </summary>
    public sealed record StatusRequest
    {
        /// <summary>
        /// Gets or sets the Status ID.
        /// </summary>
        public required long StatusId { get; set; }
    }

    /// <summary>
    /// Action completed ok.
    /// </summary>
    /// <param name="CompletedOk">Completed Ok</param>
    public sealed record StatusResponse(bool CompletedOk);
}
