// <copyright file="TokenClaimMapper.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Security.Claims;

using SOS.CalAccess.FilerPortal.Generated;

namespace SOS.CalAccess.FilerPortal.Authentication;

/// <summary>
/// A delegate for mapping tokens into claims.
/// </summary>
/// <param name="token">The token container.</param>
/// <param name="schema">The auth schema to use.</param>
/// <returns>A <see cref="ClaimsIdentity"/> constructed from the token.</returns>
public delegate ClaimsIdentity TokenClaimMapper(TokenResponse token, string schema);
