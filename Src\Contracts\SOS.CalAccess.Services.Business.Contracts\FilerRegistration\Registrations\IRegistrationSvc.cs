using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling registration operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the registration data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this service is to facilitate the creation and updating of registrations.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-03: Terminate a Registration</li>
/// <li>FR-04: Withdraw a Registration</li>
/// <li>FR-05: Establish Major Donor/Independent Expenditure and Payment To Influence Filer Entity Accounts</li>
/// <li>FR-06: Manage Penalty of Perjury Attestation</li>
/// <li>FR-09: Process a Lobbyist Photo</li>
/// <li>FR-12: Enter Lobbying Ethics Certification</li>
/// <li>FR-13: Void Lobbyist Registration</li>
/// <li>FR-14: Reinstate Lobbyist Registration</li>
/// <li>FR-15: Renew Lobbying Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IRegistrationRepository        | Create                         | Creates a new registration.  |
/// | IRegistrationRepository        | Update                         | Updates an existing registration. |
/// | IRegistrationRepository        | Delete                         | Deletes a registration.      |
/// | IRegistrationRepository        | FindById                       | Retrieves a registration by ID. |
/// | IRegistrationRepository        | GetAll                         | Retrieves all registrations. |
/// | IDecisionsSvc                  | InitiateWorkflow               | Initiates a workflow.        |
/// | IAuditSvc                      | RecordAuditEvent               | Records an audit event.      |
/// | IAgencyRepository              | GetAll                         | Retrieves all agencies.      |
/// | ICandidateRepository           | GetAll                         | Retrieves all candidates.    |
/// | IMultipurposeOrganizationRepository | GetAll                     | Retrieves all multipurpose organizations. |
/// | IRegistrationStatusRepository  | GetAll                         | Retrieves all registration statuses. |
#endregion

/// <summary>
/// Interface for the Registration Service.
/// </summary>
public interface IRegistrationSvc
{
    /// <summary>
    /// Creates a new registration.
    /// </summary>
    /// <param name="registration">The registration to create.</param>
    /// <returns>The ID of the created registration.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="CreateRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Create()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return registration"];
    /// IRegistrationSvc >> Actor [label="return registration"];
    /// \endmsc
    public Task<Registration> CreateRegistration(Registration registration);

    /// <summary>
    /// Updates an existing registration.
    /// </summary>
    /// <param name="registration">The registration to update.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="UpdateRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task UpdateRegistration(Registration registration);

    /// <summary>
    /// Deletes a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to delete.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="DeleteRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Delete()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task DeleteRegistration(long registrationId);

    /// <summary>
    /// Checks the eligibility to submit the registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to submit.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="SubmitRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task<bool> IsEligibleToSubmitRegistration(long registrationId);

    /// <summary>
    /// Submits a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to submit.</param>
    /// <param name="status">The status of the registration.</param>
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="SubmitRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task SubmitRegistration(long registrationId, RegistrationStatus status);

    /// <summary>
    /// Approves a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to approve.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="ApproveRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task ApproveRegistration(long registrationId);

    /// <summary>
    /// Withdraws a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to withdraw.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="WithdrawRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task WithdrawRegistration(long registrationId);

    /// <summary>
    /// Terminates a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to terminate.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="TerminateRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task TerminateRegistration(long registrationId);

    /// <summary>
    /// Updates the status of a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration.</param>
    /// <param name="status">The new status of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="UpdateRegistrationStatus()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task UpdateRegistrationStatus(long registrationId, RegistrationStatus status);

    /// <summary>
    /// Initiates the amendment of a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to amend.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="InitiateAmendRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Create()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task InitiateAmendRegistration(long registrationId);

    /// <summary>
    /// Amends a registration.
    /// </summary>
    /// <param name="newRegistration">The new registration data.</param>
    /// <param name="registrationId">The ID of the registration to amend.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="AmendRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task AmendRegistration(Registration newRegistration, long registrationId);

    /// <summary>
    /// Initiates the renewal of a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to renew.</param>
    /// <returns>The ID of the registration to be renewed.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="InitiateRenewRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task<long> InitiateRenewRegistration(long registrationId);

    /// <summary>
    /// Renews a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to renew.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="RenewRegistration()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task RenewRegistration(long registrationId);

    /// <summary>
    /// Initiates candidate attestation.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for candidate attestation.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="InitiateCandidateAttestation()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task InitiateCandidateAttestation(long registrationId);

    /// <summary>
    /// Initiates candidate attestation.
    /// </summary>
    /// <param name="attestation">Candidate attestation.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="InitiateCandidateAttestation()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task<Attestation> InitiateCandidateAttestation(Attestation attestation);

    /// <summary>
    /// Completes candidate attestation.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for candidate attestation.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="CompleteCandidateAttestation(registrationId)"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task CompleteCandidateAttestation(long registrationId);

    /// <summary>
    /// Initiates lobbying activity authorization.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for lobbying activity authorization.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="InitiateLobbyingActivityAuthorization()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task InitiateLobbyingActivityAuthorization(long registrationId);

    /// <summary>
    /// Completes lobbying activity authorization.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for lobbying activity authorization.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="CompleteLobbyingActivityAuthorization()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task CompleteLobbyingActivityAuthorization(long registrationId);

    /// <summary>
    /// Initiates lobbyist certification statement.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for lobbyist certification statement.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="InitiateLobbyistCertificationStatement()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task InitiateLobbyistCertificationStatement(long registrationId);

    /// <summary>
    /// Completes lobbyist certification statement.
    /// </summary>
    /// <param name="registrationId">The ID of the registration for lobbyist certification statement.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IDecisionsSvc  [label="IDecisions \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"], IAuditSvc;
    /// Actor => IRegistrationSvc [label="CompleteLobbyistCertificationStatement()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IRegistrationSvc [label="return TOutput"];
    /// IRegistrationSvc => IRegistrationRepository [label="Update()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IRegistrationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task CompleteLobbyistCertificationStatement(long registrationId);

    /// <summary>
    /// Gets a registration by ID.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to retrieve.</param>
    /// <returns>The registration with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="GetRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="FindById()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return Registration"];
    /// IRegistrationSvc >> Actor [label="return Registration"];
    /// \endmsc
    public Task<Registration?> GetRegistration(long registrationId);

    /// <summary>
    /// Gets all registrations.
    /// </summary>
    /// <returns>A collection of all registrations.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllRegistrations()"];
    /// IRegistrationSvc => IRegistrationRepository [label="GetAll()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return IEnumerable<Registration>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<Registration>"];
    /// \endmsc
    public Task<IEnumerable<Registration>> GetAllRegistrations();

    /// <summary>
    /// Gets all committees.
    /// </summary>
    /// <returns>A collection of all committees registrations.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllCommitteesRegistrations()"];
    /// IRegistrationSvc => IRegistrationRepository [label="GetAll()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return IEnumerable<Registration>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<Registration>"];
    /// \endmsc
    public Task<IEnumerable<Registration>> GetAllCommitteesRegistrations();

    /// <summary>
    /// Gets lobbyist employer registration.
    /// </summary>
    /// <param name="id">The ID of the user.</param>
    /// <returns>A registrations object of type lobbyist employer.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="GetLobbyistEmployerEntityRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="GetAll()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return Registration"];
    /// IRegistrationSvc >> Actor [label="return Registration"];
    /// \endmsc
    public Task<Registration> GetLobbyistEmployerEntityRegistration(long id);

    /// <summary>
    /// Gets lobbyist registration.
    /// </summary>
    /// <param name="id">The ID of the user.</param>
    /// <returns>A registrations object of type lobbyist.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="GetLobbyistEntityRegistration()"];
    /// IRegistrationSvc => IRegistrationRepository [label="GetAll()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return Registration"];
    /// IRegistrationSvc >> Actor [label="return Registration"];
    /// \endmsc
    public Task<Registration> GetLobbyistEntityRegistration(long id);

    /// <summary>
    /// Adds an address to a registration.
    /// </summary>
    /// <param name="address">The address to add.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="AddRegistrationAddress()"];
    /// IRegistrationSvc => IRegistrationRepository [label="AddAddress()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task AddRegistrationAddress(Address address, long registrationId);

    /// <summary>
    /// Updates an address of a registration.
    /// </summary>
    /// <param name="address">The address to update.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="UpdateRegistrationAddress()"];
    /// IRegistrationSvc => IRegistrationRepository [label="UpdateAddress()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task UpdateRegistrationAddress(Address address, long registrationId);

    /// <summary>
    /// Removes an address from a registration.
    /// </summary>
    /// <param name="addressId">The ID of the address to remove.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="RemoveRegistrationAddress()"];
    /// IRegistrationSvc => IRegistrationRepository [label="RemoveAddress()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task RemoveRegistrationAddress(long addressId, long registrationId);

    /// <summary>
    /// Adds a phone number to a registration.
    /// </summary>
    /// <param name="phoneNumber">The phone number to add.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="AddRegistrationPhoneNumber()"];
    /// IRegistrationSvc => IRegistrationRepository [label="AddPhoneNumber()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task AddRegistrationPhoneNumber(PhoneNumber phoneNumber, long registrationId);

    /// <summary>
    /// Updates a phone number of a registration.
    /// </summary>
    /// <param name="phoneNumber">The phone number to update.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="UpdateRegistrationPhoneNumber()"];
    /// IRegistrationSvc => IRegistrationRepository [label="UpdatePhoneNumber()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task UpdateRegistrationPhoneNumber(PhoneNumber phoneNumber, long registrationId);

    /// <summary>
    /// Removes a phone number from a registration.
    /// </summary>
    /// <param name="phoneNumberId">The ID of the phone number to remove.</param>
    /// <param name="registrationId">The ID of the registration.</param>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationRepository [label="IRegistration \n Repository"];
    /// Actor => IRegistrationSvc [label="RemoveRegistrationPhoneNumber()"];
    /// IRegistrationSvc => IRegistrationRepository [label="RemovePhoneNumber()"];
    /// IRegistrationRepository >> IRegistrationSvc [label="return"];
    /// IRegistrationSvc >> Actor [label="return"];
    /// \endmsc
    public Task RemoveRegistrationPhoneNumber(long phoneNumberId, long registrationId);

    /// <summary>
    /// Gets all agencies.
    /// </summary>
    /// <returns>A collection of all agencies.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IAgencyRepository [label="IAgency \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllAgencies()"];
    /// IRegistrationSvc => IAgencyRepository [label="GetAll()"];
    /// IAgencyRepository >> IRegistrationSvc [label="return IEnumerable<Agency>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<Agency>"];
    /// \endmsc
    public Task<IEnumerable<Agency>> GetAllAgencies();

    /// <summary>
    /// Gets all candidates.
    /// </summary>
    /// <returns>A collection of all candidates.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], ICandidateRepository [label="ICandidate \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllCandidates()"];
    /// IRegistrationSvc => ICandidateRepository [label="GetAll()"];
    /// ICandidateRepository >> IRegistrationSvc [label="return IEnumerable<Candidate>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<Candidate>"];
    /// \endmsc
    public Task<IEnumerable<Candidate>> GetAllCandidates();

    /// <summary>
    /// Gets all multipurpose organizations.
    /// </summary>
    /// <returns>A collection of all multipurpose organizations.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IMultipurposeOrganizationRepository [label="IMultipurpose \n Organization \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllMultipurposeOrganizations()"];
    /// IRegistrationSvc => IMultipurposeOrganizationRepository [label="GetAll()"];
    /// IMultipurposeOrganizationRepository >> IRegistrationSvc [label="return IEnumerable<MultipurposeOrganization>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<MultipurposeOrganization>"];
    /// \endmsc
    public Task<IEnumerable<MultipurposeOrganization>> GetAllMultipurposeOrganizations();

    /// <summary>
    /// Gets all registration statuses.
    /// </summary>
    /// <returns>A collection of all registration statuses.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistration \n Svc"], IRegistrationStatusRepository [label="IRegistration \n Status \n Repository"];
    /// Actor => IRegistrationSvc [label="GetAllRegistrationStatuses()"];
    /// IRegistrationSvc => IRegistrationStatusRepository [label="GetAll()"];
    /// IRegistrationStatusRepository >> IRegistrationSvc [label="return IEnumerable<RegistrationStatus>"];
    /// IRegistrationSvc >> Actor [label="return IEnumerable<RegistrationStatus>"];
    /// \endmsc
    public Task<IEnumerable<RegistrationStatus>> GetAllRegistrationStatuses();

    /// <summary>
    /// Cancels the draft registration.
    /// </summary>
    /// <param name="registrationId"></param>
    /// <returns></returns>
    public Task<Registration> CancelRegistration(long registrationId);

    /// <summary>
    /// GetCandidateRegistrationElectionData
    /// </summary>
    /// <param name="registrationId"></param>
    /// <returns>Returns the CandidateIntentionStatment with election data.</returns>
    public Task<CandidateIntentionStatement?> GetCandidateRegistrationElectionData(long registrationId);

    /// <summary>
    /// GetExpenditureExpenseAmountData
    /// </summary>
    /// <param name="registrationId"></param>
    /// <returns>Returns the Expenditure Expense Ceiling amounts.</returns>
    public Task<ExpenditureExpenseAmount?> GetExpenditureExpenseAmountData(long registrationId);

    /// <summary>
    /// Get my registration on the dashboard
    /// </summary>
    /// <param name="userId">Current logged in user ID</param>
    /// <returns>A collection of my registration on the dashboard</returns>
    Task<List<RegistrationDashboardDto>> GetMyRegistrations(long userId);

    /// <summary>
    /// Get registrations for a filer by filerId 
    /// </summary>
    /// <param name="filerId">filer id</param>
    /// <returns>List of registrations</returns>
    /// \msc
    /// Actor, IRegistrationSvc [label="IRegistrationSvc"], IRegistrationRepositorySvc [label="IRegistrationRepositorySvc"];
    /// Actor => IRegistrationSvc [label="GetRegistrationsForFiler()"];
    /// IRegistrationSvc => IRegistrationRepositorySvc [label="GetRegistrationsForFiler()"];
    /// IRegistrationRepositorySvc >> IRegistrationSvc [label="\nreturn List<Registration>"];
    /// IRegistrationSvc >> Actor [label="\nreturn List<Registration>"];
    /// \endmsc
    public Task<List<Registration>> GetRegistrationsForFiler(long filerId);


}
