using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interfaces defines the methods required for searching and creating(export) the Lobbying Directory.
/// The lobbying directory images( pics of head shots ) are stored in Azure Blob Storage and delivered via Azure CDN to ensure fast, scalable access across geographic regions.
/// When an image is updated, it is overwritten in Blob Storage, and a versioning strategy is applied using a query string based on a timestamp or unique identifier.
/// This versioned URL is generated by the backend service and ensures that the CDN fetches the updated image rather than serving a cached one.
/// The older, unused versions in the CDN are automatically purged over time through a least recently used (LRU) eviction policy.
///The frontend application receives versioned CDN URLs from the backend and uses them directly to render images.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service invoking this layer is developed.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Allows users to access the lobbying directory features in  CARS 
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-10: Access the Lobbying Directory</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service.
/// </p>
/// | Service                              | Operation  | Description                          |
/// | ------------------------------------ | ---------- | ------------------------------------ |
/// |   LobbyingDirectoryRepository  
#endregion
namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
public interface ILobbyingDirectorySvc
{
    /// <summary>
    /// Search the lobbying directory This is a search feature that is available to users online and real time
    /// </summary>
    /// <param name="lobbyingDirectorySearchCriteria"></param>
    /// <returns> paginated list of results along with the headshots</returns>
    /// \msc
    /// Actor, ILobbyingDirectorySvc [label="ILobbying\nDirectorySvc"], ILobbyingDirectoryRepository [label="ILobbying\nDirectoryRepository"];
    /// Actor => ILobbyingDirectorySvc [label="SearchLobbyingDirectory()"];
    /// ILobbyingDirectorySvc => ILobbyingDirectoryRepository [label="SearchLobbyingDirectory()"];
    /// ILobbyingDirectoryRepository >> ILobbyingDirectorySvc [label="\nreturn IEnnumerable<LobbyingDirectory>()"];
    /// ILobbyingDirectorySvc => ILobbyingDirectorySvc [label="CreateImageURL()"];
    /// ILobbyingDirectorySvc >> Actor [label="\nreturn  IEnnumerable<LobbyingDirectory>()"];
    /// \endmsc
    public Task<IEnumerable<LobbyingDirectorySearchResult>> SearchLobbyingDirectory(LobbyingDirectorySearchCriteria lobbyingDirectorySearchCriteria);




    /// <summary>
    /// Generate the Lobbying Directory . Will use the Lobbying directory template to generate it. We can plan to generate it once every day using a scheduled process.
    /// This method will be called by the scheduler to create the file. 
    /// </summary>
    /// <returns>void</returns>
    /// \msc
    /// Actor, ILobbyingDirectorySvc [label="ILobbying\nDirectorySvc"], ILobbyingDirectoryRepository [label="ILobbying\nDirectoryRepository"], ICorrespondenceSvc, IFileSvc;
    /// Actor => ILobbyingDirectorySvc [label="GenerateLobbyingDirectoryReport()"];
    /// ILobbyingDirectorySvc => ILobbyingDirectoryRepository [label="GetLobbyingDirectoryReportData()"];
    /// ILobbyingDirectoryRepository >> ILobbyingDirectorySvc [label="\nreturn LobbyingDirectoryReportData()"];
    /// ILobbyingDirectorySvc => ILobbyingDirectorySvc [label="GenerateImageURL()"];
    /// ILobbyingDirectorySvc => ICorrespondenceSvc [label="GenerateCorrespondence()"];
    /// ICorrespondenceSvc >> ILobbyingDirectorySvc [label="\nreturn void"];
    /// ILobbyingDirectorySvc => IFileSvc [label="CreateFile()"];
    /// IFileSvc >> ILobbyingDirectorySvc [label="\nreturn void"];
    /// ILobbyingDirectorySvc >> Actor [label="\nreturn void"];
    /// \endmsc
    public Task GenerateLobbyingDirectoryReport();

    /// <summary>
    /// When the user requests the Lobbying directory they just retrieve the generated file which is no more than one day old.
    /// The file will be stored in the CDN Activated Azure Blob 
    /// </summary>
    /// <returns>file URL</returns>
    /// \msc
    /// Actor, ILobbyingDirectorySvc [label="ILobbying\nDirectorySvc"], ILobbyingDirectoryRepository [label="ILobbying\nDirectoryRepository"];
    /// Actor => ILobbyingDirectorySvc [label="GetLobbyingDirectory()"];
    /// ILobbyingDirectorySvc => ILobbyingDirectoryRepository [label="GetLobbyingDirectoryURL()"];
    /// ILobbyingDirectoryRepository >> ILobbyingDirectorySvc [label="\nreturn string"];
    /// ILobbyingDirectorySvc >> Actor [label="\nreturn string"];
    /// \endmsc
    public Task<string> GetLobbyingDirectory();

}
