using SOS.CalAccess.Services.Business.SystemAdministration.AuditLogs.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.Logs;
#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface defines operations for managing function logs within the system. It supports creating, updating,
/// and retrieving function logs by various criteria such as ID and process name.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that function log data passed to the service is valid and conforms to the required data model.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The primary business function of this service is to provide logging and auditing of internal system operations
/// for debugging, compliance, and monitoring purposes.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA01: SA-01: Configure System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the repository operations referenced by this service.
/// </p>
/// | Service                  | Operation                  | Description                                          |
/// |-------------------------|----------------------------|------------------------------------------------------|
/// | IFunctionLogRepository  | Add                        | Adds a new function log entry.                      |
/// | IFunctionLogRepository  | Update                     | Updates an existing function log entry.             |
/// | IFunctionLogRepository  | Get                        | Retrieves all function log entries.                 |
/// | IFunctionLogRepository  | GetById                    | Retrieves a specific function log by its ID.        |
/// | IFunctionLogRepository  | GetFunctionLogByProcess    | Retrieves logs associated with a given process name.|
#endregion
public interface IFunctionLogSvc
{
    /// <summary>
    /// Creates a new function log entry
    /// </summary>
    /// <param name="functionLog">function log dto</param>
    /// <returns>Created function log dto</returns>
    /// \msc
    /// Actor, IFunctionLogSvc [label="IFunctionLogSvc"], IFunctionLogRepository [label="IFunctionLogRepository"];
    /// Actor => IFunctionLogSvc [label="CreateFunctionLog()"];
    /// IFunctionLogSvc => IFunctionLogRepository [label="Add()"];
    /// IFunctionLogRepository >> IFunctionLogSvc [label="\nreturn FunctionLog"];
    /// IFunctionLogSvc >> Actor [label="\nreturn FunctionLogDto"];
    /// \endmsc
    public Task<FunctionLogDto> CreateFunctionLog(FunctionLogDto functionLog);

    /// <summary>
    /// Updates an existing function log entry
    /// </summary>
    /// <param name="functionLog">function log dto</param>
    /// <returns>Updated function log dto</returns>
    /// \msc
    /// Actor, IFunctionLogSvc [label="IFunctionLogSvc"], IFunctionLogRepository [label="IFunctionLogRepository"];
    /// Actor => IFunctionLogSvc [label="UpdateFunctionLog()"];
    /// IFunctionLogSvc => IFunctionLogRepository [label="Update()"];
    /// IFunctionLogRepository >> IFunctionLogSvc [label="\nreturn FunctionLog"];
    /// IFunctionLogSvc >> Actor [label="\nreturn FunctionLogDto"];
    /// \endmsc
    public Task<FunctionLogDto> UpdateFunctionLog(FunctionLogDto functionLog);

    /// <summary>
    /// Retrieves all function log entries
    /// </summary>
    /// <returns>List of function log entries</returns>
    /// \msc
    /// Actor, IFunctionLogSvc [label="IFunctionLogSvc"], IFunctionLogRepository [label="IFunctionLogRepository"];
    /// Actor => IFunctionLogSvc [label="GetFunctionLogs()"];
    /// IFunctionLogSvc => IFunctionLogRepository [label="Get()"];
    /// IFunctionLogRepository >> IFunctionLogSvc [label="\nreturn IEnumerable<FunctionLog>"];
    /// IFunctionLogSvc >> Actor [label="\nreturn IEnumerable<FunctionLogDto>"];
    /// \endmsc
    public Task<IEnumerable<FunctionLogDto>> GetFunctionLogs();

    /// <summary>
    /// Retrieves a function log entry by ID
    /// </summary>
    /// <param name="functionLogId">function log ID</param>
    /// <returns>Function log entry</returns>
    /// \msc
    /// Actor, IFunctionLogSvc [label="IFunctionLogSvc"], IFunctionLogRepository [label="IFunctionLogRepository"];
    /// Actor => IFunctionLogSvc [label="GetFunctionLogById()"];
    /// IFunctionLogSvc => IFunctionLogRepository [label="GetById()"];
    /// IFunctionLogRepository >> IFunctionLogSvc [label="\nreturn FunctionLog"];
    /// IFunctionLogSvc >> Actor [label="\nreturn FunctionLogDto"];
    /// \endmsc
    public Task<FunctionLogDto> GetFunctionLogById(long functionLogId);

    /// <summary>
    /// Retrieves function logs associated with a specific process
    /// </summary>
    /// <param name="processName">name of the process</param>
    /// <returns>List of function log entries</returns>
    /// \msc
    /// Actor, IFunctionLogSvc [label="IFunctionLogSvc"], IFunctionLogRepository [label="IFunctionLogRepository"];
    /// Actor => IFunctionLogSvc [label="GetFunctionLogByProcess()"];
    /// IFunctionLogSvc => IFunctionLogRepository [label="GetFunctionLogByProcess()"];
    /// IFunctionLogRepository >> IFunctionLogSvc [label="\nreturn IEnumerable<FunctionLog>"];
    /// IFunctionLogSvc >> Actor [label="\nreturn IEnumerable<FunctionLogDto>"];
    /// \endmsc
    public Task<IEnumerable<FunctionLogDto>> GetFunctionLogByProcess(string processName);
}
