using Newtonsoft.Json.Linq;

namespace SOS.CalAccess.Services.Common.DataValidation;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IEfileValidationsSvc provides methods to support validating that a provided amendment meets a defined rules.
/// </p>
/// <p>
/// the service provides methods for performing the validation on a amendment filingId, parentId
/// </p>
/// <p>
/// FilingId are stored into the database and referenced by a unique identifier
/// </p>
/// <p>
/// In terms of Architecture Design this translates to a backend common service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS2 Data Validation</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IAmendmentRepository          | GetById                        | Get amendment values by json object        |
#endregion
public interface IEfileValidationsSvc
{
    /// <summary>
    /// Evaluate the amendment and validate that it meets the specific rules.
    /// </summary>
    /// <param name="json">The JSON JObject we're validating</param>
    /// <returns></returns>/returns>
    ///
    Task AmendmentValidation(JObject json);

    /// <summary>
    /// Validates the filer ID URL parameter to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="filerId">The filer ID associated with the eFile submission.</param>
    Task FilerIdValidation(string? filerId);

    /// <summary>
    /// Validates the agency ID  to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="agencyId">The agency ID associated with the eFile submission.</param>
    Task AgencyIdValidation(long agencyId);


    /// <summary>
    /// Validates the expenseTypeId ID  to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="expenseTypeId">The agency ID associated with the eFile submission.</param>
    Task ActivityExpenseTypeIdValidation(long expenseTypeId);


    /// <summary>
    /// Validates the payeeType ID  to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="payeeTypeId">The agency ID associated with the eFile submission.</param>
    Task PayeeTypeIdValidation(long payeeTypeId);


    /// <summary>
    /// Validates the officialPosition ID  to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="officialPositionId">The agency ID associated with the eFile submission.</param>
    Task OfficialPositionIdValidation(long officialPositionId);

    /// <summary>
    /// Validates the recipientCommitteeID  to ensure it meets the required format.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="recipientCommitteeID">The agency ID associated with the eFile submission.</param>
    Task RecipientCommitteeIdValidation(long recipientCommitteeID);

    Task FilingPeriodIdValidation(long? filingPeriodId);
    /// <summary>
    /// Validates the reportingPeriod.
    /// Throws an exception if invalid, along with the reason.
    ///
    /// Validation rules:
    /// 1. Must not be null or whitespace.
    /// 2. Must be an integer with less than 20 digits (max length for a long is 19 digits)
    /// </summary>
    /// <param name="startDate">The startDate of the reporting period of eFile submission.</param>
    /// <param name="endDate">The endDate of the reporting period of eFile submission.</param>
    Task FilingPeriodValidationByDates(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Validates the legislativeSessionId.
    /// </summary>
    /// <param name="legislativeSessionId"></param>
    /// <returns></returns>
    Task LegislativeSessionIdValidation(long? legislativeSessionId);
}
