using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;


namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling candidate intention statement registration operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the registration data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this service is to facilitate the creation and updating of registrations.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-03: Terminate a Registration</li>
/// <li>FR-04: Withdraw a Registration</li>
/// <li>FR-06: Manage Penalty of Perjury Attestation</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IRegistrationRepository        | Create                         | Creates a new registration.  |
/// | IRegistrationRepository        | Update                         | Updates an existing registration. |
/// | IRegistrationRepository        | UpdateProperty                 | Updates a single property on an existing registration. |
/// | IRegistrationRepository        | Delete                         | Deletes a registration.      |
/// | IRegistrationRepository        | FindCandidateIntentionStatementById | Retrieves a registration by ID. |
/// | IRegistrationRepository        | FindCandidateIntentionStatementWithElectionById | Retrieves a registration by ID. |
/// | IRegistrationRepository        | LinkElectionToCandidateIntentRegistration | Associate a registration to an election race |
/// | IAttestationRepository         | Create                         | Create an attestion record |
/// | IDecisionsSvc                  | InitiateWorkflow               | Initiates a workflow.        |
#endregion
public interface ICandidateIntentionRegistrationSvc
{
    /// <summary>
    /// Create a new draft Candidate Intention Statement Registration
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Returns a registration response containing the status of the create operation.</returns>
    [Post(CreateCandidateIntentionStatementPath)]
    Task<RegistrationResponseDto> CreateCandidateIntentionStatement([Body] CandidateIntentionStatementRequest request);
    const string CreateCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement";

    /// <summary>
    /// Retrieves the Candidate Intention Statement Registration Summary for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns the Candidate Intention Statement details if found; otherwise, null.</returns>
    [Get(GetCandidateIntentionStatementSummaryPath)]
    Task<CandidateIntentionStatement?> GetCandidateIntentionStatementSummary(long id);
    const string GetCandidateIntentionStatementSummaryPath = "/api/Registration/CandidateIntentionStatement/{id}/summary";

    /// <summary>
    /// Retrieves the Candidate Intention Statement for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns the Candidate Intention Statement details if found; otherwise, null.</returns>
    [Get(GetCandidateIntentionStatementPath)]
    Task<CandidateIntentionStatementResponseDto?> GetCandidateIntentionStatement(long id);
    const string GetCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}";

    /// <summary>
    /// Patches the Status for a Candidate Intention record
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <param name="request">statusId</param>
    /// <returns></returns>
    [Patch("/" + PatchCandidateIntentionStatementStatusByIdPath)]
    Task<CandidateIntentionPatchDto.StatusResponse> PatchCandidateIntentionStatementStatusById(long id, [Body] CandidateIntentionPatchDto.StatusRequest request);
    const string PatchCandidateIntentionStatementStatusByIdPath = "api/Registration/CandidateIntentionStatement/{id}";

    /// <summary>
    /// Retrieves the expenditure expense amount for a given registration.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns the expenditure expense amount if found; otherwise, null.</returns>
    [Get(GetCandidateIntentionStatementExpenditureExpenseAmountPath)]
    Task<decimal?> GetCandidateIntentionStatementExpenditureExpenseAmount(long id);
    const string GetCandidateIntentionStatementExpenditureExpenseAmountPath = "/api/Registration/CandidateIntentionStatement/{id}/ExpenditureExpenseAmount";

    /// <summary>
    /// Retrieves the election for a given registration id.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns the election if found; otherwise, null.</returns>
    [Get(GetCandidateIntentionStatementElectionByIdPath)]
    Task<Election?> GetCandidateIntentionStatementElection(long id);
    const string GetCandidateIntentionStatementElectionByIdPath = "/api/Registration/CandidateIntentionStatement/{id}/Election";

    /// <summary>
    /// Submits a Candidate Intention Statement for approval
    /// </summary>
    /// <param name="submission">A complete candidate intention statement (form 501), including attestation</param>
    /// <returns>Returns a registration response indicating whether the submission was successful.</returns>
    [Post(SubmitCandidateIntentionStatementForEfilePath)]
    Task<RegistrationResponseDto> SubmitCandidateIntentionStatementForEfile(CandidateIntentionStatementDto submission);
    const string SubmitCandidateIntentionStatementForEfilePath = "/api/Registration/CandidateIntentionStatement/Submit";

    /// <summary>
    /// Updates an existing Candidate Intention Statement.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <param name="request">The request object containing updated details.</param>
    /// <returns>Returns a registration response containing the status of the update operation.</returns>
    [Put(UpdateCandidateIntentionStatementPath)]
    Task<RegistrationResponseDto> UpdateCandidateIntentionStatement(long id, [Body] CandidateIntentionStatementRequest request);
    const string UpdateCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}";

    /// <summary>
    /// Updates the expenditure limit for a specific Candidate Intention Statement.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <param name="request">The request containing the updated expenditure limit details.</param>
    /// <returns>A response indicating the success or failure of the update operation.</returns>
    [Put(UpdateCandidateIntentionStatementExpenditureLimitPath)]
    Task<RegistrationResponseDto> UpdateCandidateIntentionStatementExpenditureLimit(long id, [Body] CandidateIntentionStatementExpenditureLimitRequest request);
    const string UpdateCandidateIntentionStatementExpenditureLimitPath = "/api/Registration/CandidateIntentionStatement/{id}/ExpenditureLimit";

    /// <summary>
    /// Sends a Candidate Intention Statement for attestation.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns a registration response indicating whether the submission was successful.</returns>
    [Post(SendForAttestationPath)]
    Task<RegistrationResponseDto> SendForAttestation(long id);
    const string SendForAttestationPath = "/api/Registration/CandidateIntentionStatement/{id}/SendForAttestation";

    /// <summary>
    /// Submits a Candidate Intention Statement for approval.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns>Returns a registration response indicating whether the submission was successful.</returns>
    [Post(SubmitCandidateIntentionStatementPath)]
    Task<RegistrationResponseDto> SubmitCandidateIntentionStatement(long id);
    const string SubmitCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}/Submit";

    /// <summary>
    /// Cancels the draft registration.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <returns></returns>
    [Post(CancelCandidateIntentionStatementPath)]
    Task CancelCandidateIntentionStatement(long id);
    const string CancelCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}/Cancel";

    /// <summary>
    /// Links an election to a Candidate Intention Statement.
    /// </summary>
    /// <param name="id">The unique identifier of the Candidate Intention Statement.</param>
    /// <param name="electionInfo">The election information to be linked.</param>
    /// <returns>Returns a registration response indicating the success or failure of the operation.</returns>
    [Post(LinkElectionToCandidateIntentionStatementPath)]
    Task<RegistrationResponseDto> LinkElectionToCandidateIntentionStatement(long id, [Body] UpdateRegistrationElectionRequest electionInfo);
    const string LinkElectionToCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}/Election";

    /// <summary>
    /// Link controlled committee to candidate registration
    /// </summary>
    /// <param name="id">The unique identifier of the controlled committee.</param>
    /// <returns>Returns the Candidate Intention Statement details if found; otherwise, null.</returns>
    [Post(LinkControlledCommitteeToCandidateIntentionStatementPath)]
    Task<List<WorkFlowError>> LinkControlledCommitteeToCandidateIntentionStatement(long id, [Body] ControlledCommitteeRequestDto request);
    const string LinkControlledCommitteeToCandidateIntentionStatementPath = "/api/Registration/CandidateIntentionStatement/{id}/ControlledCommittee";

    /// <summary>
    /// Searches for controlled committees by Id or Name.
    /// </summary>
    /// <param name="q">The search query string containing the controlled committees name.</param>
    /// <returns>Returns a list of committees matching the search criteria.</returns>
    [Get(SearchControlledCommitteeByIdOrNamePath)]
    Task<IEnumerable<ControlledCommitteeResponseDto>> SearchControlledCommitteeByIdOrName([Query] string q);
    const string SearchControlledCommitteeByIdOrNamePath = "/api/Registration/CandidateIntentionStatement/ControlledCommittee/Search";

    /// <summary>
    /// Gets the linked Controlled Committee
    /// </summary>
    /// <param name="q">The search query string containing the controlled committees name.</param>
    /// <returns>Returns a list of committees matching the search criteria.</returns>
    [Get(GetLinkedControlledCommitteePath)]
    Task<ControlledCommitteeResponseDto?> GetLinkedControlledCommittee(long id);
    const string GetLinkedControlledCommitteePath = "/api/Registration/CandidateIntentionStatement/{id}/ControlledCommittee";
}



