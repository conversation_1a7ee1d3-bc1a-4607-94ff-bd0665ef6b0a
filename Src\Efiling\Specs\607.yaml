openapi: 3.0.3
info:
  title: Form 607 - Notice of Withdrawal
  description: |
    API for submitting Form 607, the Notice of Withdrawal. This form is used by individuals or entities to withdraw their previously filed lobbying registration or certification statements.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/NoticeOfWithdrawal/{filerId}:
    post:
      summary: Submit Form 607
      description: Submit the Notice of Withdrawal (Form 607) to withdraw a Lobbyist Certification Statement or Lobbying Firm Registration Statement.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NoticeOfWithdrawal'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    NoticeOfWithdrawal:
      type: object
      properties:
        withdrawalDetails:
          $ref: '#/components/schemas/WithdrawalDetails'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - withdrawalDetails
        - attestation

    WithdrawalDetails:
      type: object
      properties:
        effectiveDate:
          type: string
          format: date
          description: Effective date of withdrawal.
        legislativeSession:
          type: string
          description: Legislative session associated with the withdrawal (e.g., "2023-2024").
      required:
        - effectiveDate
