using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace SOS.CalAccess.Services.Business.Lookups;

/// <summary>
/// Model for response body of CandidateRegistrationModel.SearchCandidateByName API.
/// </summary>
public class ContactSearchResultDto
{
    /// <summary>
    /// Gets or sets the contact's name.
    /// </summary>
    [Required]
    [JsonPropertyName("Name")]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the registration ID (if sourced from registion).
    /// </summary>
    [JsonPropertyName("RegistrationFilingId")]
    public long? RegistrationFilingId { get; set; }

    /// <summary>
    ///   Gets or sets the filer ID (if sourced from registration).
    /// </summary>
    [JsonPropertyName("FilerId")]
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets or sets the contact ID (if sourced from filer contact).
    /// </summary>
    [Json<PERSON>ropertyName("ContactId")]
    public long? ContactId { get; set; }

    /// <summary>
    /// Gets or sets the contact's address line 1 (Street/Street2).
    /// </summary>
    [JsonPropertyName("AddressLine1")]
    public string? AddressLine1 { get; set; }

    /// <summary>
    /// Gets or sets the contact's address line 2 (City/State/Zip).
    /// </summary>
    [JsonPropertyName("AddressLine2")]
    public string? AddressLine2 { get; set; }
}
