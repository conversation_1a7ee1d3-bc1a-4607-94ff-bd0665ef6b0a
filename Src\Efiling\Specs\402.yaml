openapi: 3.0.3
info:
  title: Statement of Termination
  description: API schema for submitting the Statement of Termination (Form 402) for Slate Mailer Organizations.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Termination/SlateMailer/{filerId}:
    post:
      summary: Submit Statement of Termination
      description: Submit a Statement of Termination for Slate Mailer Organizations.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                slateMailerOrganization:
                  $ref: '#/components/schemas/SlateMailerOrganizationInformation'
                officers:               
                  type: array
                  items:
                    type: object
                    properties:
                      firstName:
                        type: string
                        description: First name of the officer.
                      lastName:
                        type: string
                        description: Last name of the officer.
                      role:
                        type: string
                        enum:
                          - Treasurer
                        description: Position of the officer (Treasurer).
                      address:
                        $ref: './common-schemas.yaml#/components/schemas/Address'
                      phone:
                        type: string
                        description: Daytime phone number of the officer as digits only.
                        pattern: '^\d+$'
                        maxLength: 40
                      email:
                        type: string
                        format: email
                        description: email address of the officer.
                    required:
                      - firstName
                      - lastName      
                      - role
                      - address
                      - phone
                      - email
  
                  description: List of Treasurers and Principal Officers, including their roles, contact information, and addresses.    
                    
                amendment:
                  $ref: './common-schemas.yaml#/components/schemas/Amendment'
                attestation:
                  $ref: './common-schemas.yaml#/components/schemas/Attestation'
              required:
                - slateMailerOrganization
                - attestation
                - amendment
                - officers
      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    SlateMailerOrganizationInformation:
      description: Basic information about the Slate Mailer Organization, including its level of activity and addresses and termination.
      type: object
      properties:
        submittedDate:
          type: string
          format: date
          description: Date submitted
        terminationDate:
          type: string
          format: date
          description: Terminated date   
        name:
          type: string
          description: Full name of the Slate Mailer Organization.       
        address:
          $ref: './common-schemas.yaml#/components/schemas/OrgAddress'
        phone:
          type: string
          description: Phone number of the organization as digits only.
          pattern: '^\d+$'
          maxLength: 40
        fax:
          type: string
          description: Optional fax number for the organization as digits only.
          pattern: '^\d+$'
          maxLength: 40
        email:
          type: string
          format: email
          description: email address for the organization.
      required:
        - name
        - address
        - phone
        - email
