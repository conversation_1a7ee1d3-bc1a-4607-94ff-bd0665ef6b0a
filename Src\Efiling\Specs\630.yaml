openapi: 3.0.3
info:
  title: Form 630 - Payments Made to Lobbying Coalitions
  description: |
    API for submitting Form 630, the Payments Made to Lobbying Coalitions form.
    This form discloses payments made to lobbying coalitions by lobbyist employers or lobbying firms.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Activity/PaymentsMade/{filerId}:
    post:
      summary: Submit Form 630
      description: Submit the Payments Made to Lobbying Coalitions (Form 630).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentsMade'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    PaymentsMade:
      type: object
      properties:
        periodCovered:
          type: object
          properties:
            startDate:
              type: string
              format: date
              description: Start date of the reporting period.
            endDate:
              type: string
              format: date
              description: End date of the reporting period.
          required:
            - startDate
            - endDate
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
          description: List of payments made to lobbying coalitions during the reporting period.
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - periodCovered
        - payments
        - attestation

    Payment:
      type: object
      properties:
        coalitionName:
          type: string
          description: Name of the lobbying coalition receiving payments.
        coalitionAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        amountReceivedThisPeriod:
          type: number
          format: double
          description: Total amount received this reporting period.
         #Todo based on dicussion we have how to caluclate Cummulative amount 
       # cumulativeAmount:
        #  type: number
        #  format: double
         # description: Total amount paid to the coalition since the beginning of the legislative session.
      required:
        - coalitionName
        - coalitionAddress
        - amountPaidThisPeriod
        #- cumulativeAmount
