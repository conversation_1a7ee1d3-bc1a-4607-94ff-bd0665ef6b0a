namespace SOS.CalAccess.Services.Common.BusinessRules;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IDecisionsSvc provides methods to interact with business rules and configurable workflows managed in the Decisions Software using integrations exposed as REST apis.
/// </p>
/// <p>
/// A Flow or Ruleset in Decisions can be made into a REST Web Service that supports either HTTP GET or POST methods. 
/// Calling a Flow as a REST Service allows external applications to send data into Decisions for use in a Flow. 
/// The new data can be returned once acted upon or evaluated in the Flow. 
/// REST calls to a Flow require either a valid User Session ID or a valid User Authentication ID.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend business application layer service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// All methods handle Authorization to the Decisions services using Decisions "Named Session" functionality.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to call configurable rules and workflows hosted in the Decisions Platform from the CARS application.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS11 Business Rules Configuration</li>
/// <li>BCS12 Workflow  Configuration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | Decisions REST api | POST |  Allows initiation of Flows and rulesets in Decisions |
#endregion
public interface IDecisionsSvc
{

    /// <summary>
    /// Generate a POST request to the Decisions REST API integration endpoint for the workflow identified by the workflowId 
    /// with the body of the request containing a JSON serializiation of the provided workflowData parameter object.  The resulting
    /// json response from Decisions will be deserialized into an instance of the TOutput class and returned.
    /// </summary>
    /// <typeparam name="TInput"></typeparam>
    /// <typeparam name="TOutput"></typeparam>
    /// <param name="workflowId">enum indicating which workflow should be triggered in Decisions</param>
    /// <param name="input">The object that will be serialized to json and sent to Decisions in the Input property of the request</param>
    /// <param name="checkRequiredFields">sets the common CheckRequiredFields property sent with all requests to decisions. If true required fields without a value should generate an error.  If false business rules that require a value should be ignored</param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IDecisionsSvc, Decisions;
    /// Actor => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc => Decisions [label="POST"];
    /// Decisions => IDecisionsSvc [label="return"];
    /// IDecisionsSvc >> Actor [label="return"];
    /// \endmsc
    Task<TOutput> InitiateWorkflow<TInput, TOutput>(DecisionsWorkflow workflowId, TInput input, bool checkRequiredFields = false, bool includeEmptyErrors = false) where TOutput : class;

    /// <summary>
    /// Generate a POST request to the Decisions REST API integration endpoint for the rule identified by the ruleId 
    /// with the body of the request containing a JSON serializiation of the provided ruleData parameter object.  
    /// The referenced rule should return a simple pass/fail result.
    ///
    /// CheckRequiredFields property will be true
    /// </summary>
    /// <typeparam name="IInput"></typeparam>
    /// <param name="ruleId"></param>
    /// <param name="input"></param>
    /// <returns>true if the rule passed</returns>
    /// 
    /// \msc
    /// Actor, IDecisionsSvc, Decisions;
    /// Actor => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc => Decisions [label="POST"];
    /// Decisions => IDecisionsSvc [label="return"];
    /// IDecisionsSvc >> Actor [label="return"];
    /// \endmsc
    Task<bool> ExecuteSimpleRule<TInput>(DecisionsWorkflow ruleId, TInput input);

}
