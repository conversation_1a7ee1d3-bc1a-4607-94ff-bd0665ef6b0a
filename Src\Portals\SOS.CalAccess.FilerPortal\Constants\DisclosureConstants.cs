using FileExtension = SOS.CalAccess.UI.Common.Constants.CommonConstants.FileExtension;

namespace SOS.CalAccess.FilerPortal.Constants;
public static class DisclosureConstants
{
    public static class Controller
    {
        public const string DisclosureSummaryViewModelTempDataKey = "DisclosureSummaryViewModel";
        public const string DashboardViewName = "Dashboard";
        public const string IndexViewName = "Index";
        public const string GeneralInfo = "GeneralInfo";
        public const string UpdateTransactionSuccessMessageKey = "FilerPortal.Disclosure.Dashboard.UpdateTransactionSuccessMessage";
        public const string SmoCampaignStatement = "SmoCampaignStatement";
        public const string CoalitionReceivedTransaction = "CoalitionReceivedTransaction";
        public const string AmendmentExplanationViewName = "AmendmentExplanation";
    }

    public static class CandidateOrMeasure
    {
        public const string Candidate = "Candidate";
        public const string BallotMeasure = "Ballot Measure";
        public const string Local = "Local";
        public const string State = "State";
        public const string Support = "Support";
        public const string Oppose = "Oppose";
    }

    public static class Transaction
    {
        public const string PayorFilerContactForm = "UpsertPayor";
        public const string PayeeFilerContactForm = "PayeeInformation";
        public const string OfficerFilerContactForm = "Officer";
        public const string AllowedFileExtension = $"{FileExtension.Image},{FileExtension.Document},{FileExtension.Video},{FileExtension.Audio}";
    }

    public static class AdvertisementSubject
    {
        public const string Legislation = "Legislation";
        public const string AdministrativeAction = "AdministrativeAction";
        public const string Other = "Other";
    }
}
