namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Subset of CandidateRegistrationModel for response body of
/// CandidateRegistrationModel.SearchCandidateByName API.
/// </summary>
public class CandidateSearchRegistrationResultDto
{
    /// <summary>
    /// Gets or sets the registration's target election.
    /// </summary>
    public required string Election { get; set; }

    /// <summary>
    /// Gets or sets the office sought by this registration.
    /// </summary>
    public required string Office { get; set; }
    /// <summary>
    /// Gets or sets the election Date
    /// </summary>
    public required DateTime ElectionDate { get; set; }
}
