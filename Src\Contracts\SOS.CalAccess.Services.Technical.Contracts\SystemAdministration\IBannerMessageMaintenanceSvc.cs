using Refit;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Services.Technical.SystemAdministration;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Functionality for adding and maintaining banner messages on a portal
/// </p>
/// <h4>Assumptions</h4>
/// <h4>Business Function</h4>
/// <p>
/// Enable PRD Staff to create and maintain banner messages
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UN-01 Create System Generated User Notification</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service
/// </p>
/// | Service                   | Operation               | Description                 |
/// | ------------------------- | ----------------------- | --------------------------- |
/// | IBannerMessageRepository  | Create                  | Insert a BannerMessage      |
/// | IBannerMessageRepository  | GetById                 | Retrieve a BannerMessage    |
/// | IBannerMessageRepository  | GetAll                  | Fetch all BannerMessages    |
/// | IBannerMessageRepository  | Update                  | Update a BannerMessage      |
/// | IBannerMessageRepository  | Delete                  | Delete a BannerMessage      |
/// | IBannerMessageRepository  | ActivateBannerMessage   | Activate a BannerMessage    |
/// | IBannerMessageRepository  | DeactivateBannerMessage | Deactivate a BannerMessage  |
#endregion

public interface IBannerMessageMaintenanceSvc
{
    /// <summary>
    /// Create a banner message
    /// </summary>
    /// <param name="bannerMessage"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="CreateBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="Create()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn Id"];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + CreateBannerMessagePath)]
    Task CreateBannerMessage([Body] BannerMessageDto bannerMessageDto);
    const string CreateBannerMessagePath = "api/Banners/CreateBannerMessage";

    /// <summary>
    /// Fetch banner messagges
    /// </summary>
    /// <returns>Task<IEnumerable<BannerMessage>></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="ListBannerMessages() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="GetAll()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn IEnumerable<BannerMessage>"];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn IEnumerable<BannerMessage>"];
    /// \endmsc
    [Get("/" + ListBannerMessagesPath)]
    Task<IEnumerable<BannerMessagesGridRow>> ListBannerMessages();
    const string ListBannerMessagesPath = "api/Banners/ListBannerMessages";

    /// <summary>
    /// Fetch a banner message
    /// </summary>
    /// <paramref name="bannerId"/>
    /// <returns>Task<BannerMessage></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="ViewBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="GetById()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn BannerMessage"];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn BannerMessage"];
    /// \endmsc
    [Get("/" + ViewBannerMessagePath)]
    Task<BannerMessageDto?> ViewBannerMessage(long bannerId);
    const string ViewBannerMessagePath = "api/Banners/ViewBannerMessage";

    /// <summary>
    /// Update a banner message
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="UpdateBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="Update()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn "];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + UpdateBannerMessagePath)]
    Task UpdateBannerMessage([Body] BannerMessageDto bannerMessageDto);
    const string UpdateBannerMessagePath = "api/Banners/UpdateBannerMessage";

    /// <summary>
    /// Remove a banner message
    /// </summary>
    /// <param name="bannerId"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="RemoveBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="Delete()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn "];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + RemoveBannerMessagePath)]
    Task RemoveBannerMessage(long bannerId);
    const string RemoveBannerMessagePath = "api/Banners/RemoveBannerMessage";

    /// <summary>
    /// Activate a banner message
    /// </summary>
    /// <param name="bannerId"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="ActivateBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="ActivateBannerMessage()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn "];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + ActivateBannerMessagePath)]
    Task ActivateBannerMessage(long bannerId);
    const string ActivateBannerMessagePath = "api/Banners/ActivateBannerMessage";

    /// <summary>
    /// Deactivate a banner message
    /// </summary>
    /// <param name="bannerId"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageMaintenanceSvc [label="IBannerMessage\nMaintenanceSvc"], IBannerMessageRepository [label="IBannerMessage\nRepository"];
    /// Actor => IBannerMessageMaintenanceSvc [label="DeactivateBannerMessage() "];
    /// IBannerMessageMaintenanceSvc => IBannerMessageRepository [label="DeactivateBannerMessage()"];
    /// IBannerMessageRepository >> IBannerMessageMaintenanceSvc [label="\nreturn "];
    /// IBannerMessageMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + DeactivateBannerMessagePath)]
    Task DeactivateBannerMessage(long bannerId);
    const string DeactivateBannerMessagePath = "api/Banners/DeactivateBannerMessage";
}
