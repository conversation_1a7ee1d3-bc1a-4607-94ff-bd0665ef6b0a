{"version": "2.0", "extensions": {"serviceBus": {"prefetchCount": 1, "autoCompleteMessages": false, "maxConcurrentCalls": 1, "maxAutoRenewDuration": "5m", "maxMessageBatchSize": 1, "duplicateDetectionHistoryTimeWindow": "00:10:00", "requiresSession": false}}, "logging": {"logLevel": {"Default": "Information", "Microsoft": "Warning"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}, "enableLiveMetricsFilters": true}}}