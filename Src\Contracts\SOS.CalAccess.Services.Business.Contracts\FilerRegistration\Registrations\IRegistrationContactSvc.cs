using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for creating and updating registration contacts within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the contact information provided is valid and conforms to the required format.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The primary business function of this service is to manage the lifecycle of registration contacts, including creation and updates.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-05: Establish Major Donor/Independent Expenditure and Payment To Influence Filer Entity Accounts</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IRegistrationContactRepository | Create                         | Creates a new registration contact. |
/// | IRegistrationContactRepository | Update                         | Updates an existing registration contact. |
/// | IRegistrationContactRepository | Delete                         | Deletes a registration contact. |
/// | IRegistrationContactRepository | FindById                       | Retrieves a registration contact by ID. |
/// | IRegistrationContactRepository | GetAll                         | Retrieves all registration contacts. |
/// | IRegistrationContactRepository | GetAllByRegistrationId         | Retrieves all registration contacts for a registration. |
/// | IRegistrationContactRepository | AddAddress                     | Adds an address to a registration contact. |
/// | IRegistrationContactRepository | UpdateAddress                  | Updates an address of a registration contact. |
/// | IRegistrationContactRepository | RemoveAddress                  | Removes an address from a registration contact. |
/// | IRegistrationContactRepository | AddPhoneNumber                 | Adds a phone number to a registration contact. |
/// | IRegistrationContactRepository | UpdatePhoneNumber              | Updates a phone number of a registration contact. |
/// | IRegistrationContactRepository | RemovePhoneNumber              | Removes a phone number from a registration contact. |
/// | IRegistrationContactTypeRepository | GetAll                      | Retrieves all registration contact types. |
/// | IAddressValidationSvc          | ValidateAddress                | Validates an address. |
#endregion

/// <summary>
/// Interface for the Registration Contact Service.
/// </summary>
public interface IRegistrationContactSvc
{
    /// <summary>
    /// Creates a new registration contact.
    /// </summary>
    /// <param name="contact">The registration contact to create.</param>
    /// <returns>The ID of the created registration contact.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="CreateRegistrationContact()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="Create()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return long"];
    /// IRegistrationContactSvc >> Actor [label="return long"];
    /// \endmsc
    public long CreateRegistrationContact(RegistrationContact contact);

    /// <summary>
    /// Updates an existing registration contact.
    /// </summary>
    /// <param name="contact">The registration contact to update.</param>
    /// <returns>The ID of the updated registration contact.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="UpdateRegistrationContact()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="Update()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return long"];
    /// IRegistrationContactSvc >> Actor [label="return long"];
    /// \endmsc
    public long UpdateRegistrationContact(RegistrationContact contact);

    /// <summary>
    /// Deletes a registration contact.
    /// </summary>
    /// <param name="contactId">The ID of the registration contact to delete.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="DeleteRegistrationContact()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="Delete()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void DeleteRegistrationContact(long contactId);

    /// <summary>
    /// Gets a registration contact by ID.
    /// </summary>
    /// <param name="contactId">The ID of the registration contact to retrieve.</param>
    /// <returns>The registration contact with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="GetRegistrationContact()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="FindById()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return RegistrationContact"];
    /// IRegistrationContactSvc >> Actor [label="return RegistrationContact"];
    /// \endmsc
    public Task<RegistrationContact> GetRegistrationContact(long contactId);

    /// <summary>
    /// Gets all registration contacts.
    /// </summary>
    /// <returns>A collection of all registration contacts.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="GetAllRegistrationContacts()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="GetAll()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return IEnumerable<RegistrationContact>"];
    /// IRegistrationContactSvc >> Actor [label="return IEnumerable<RegistrationContact>"];
    /// \endmsc
    public Task<IEnumerable<RegistrationContact>> GetAllRegistrationContacts();

    /// <summary>
    /// Gets all registration contacts for a registration.
    /// </summary>
    /// <param name="registrationId">The ID of the registration to get contacts for.</param>e
    /// <returns>A collection of all registration contacts for the specified registration.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="GetRegistrationContacts()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="GetAllByRegistrationId()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return IEnumerable<RegistrationContact>"];
    /// IRegistrationContactSvc >> Actor [label="return IEnumerable<RegistrationContact>"];
    /// \endmsc
    public Task<IEnumerable<RegistrationContact>> GetRegistrationContacts(long registrationId);

    /// <summary>
    /// Adds an address to a registration contact.
    /// </summary>
    /// <param name="address">The address to add.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IAddressValidationSvc [label="IAddress \n Validation \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="AddRegistrationContactAddress()"];
    /// IRegistrationContactSvc => IAddressValidationSvc [label="ValidateAddress()"];
    /// IAddressValidationSvc >> IRegistrationContactSvc [label="return ValidatedAddress"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="AddAddress()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void AddRegistrationContactAddress(Address address, long contactId);

    /// <summary>
    /// Updates an address of a registration contact.
    /// </summary>
    /// <param name="address">The address to update.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IAddressValidationSvc [label="IAddress \n Validation \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="UpdateRegistrationContactAddress()"];
    /// IRegistrationContactSvc => IAddressValidationSvc [label="ValidateAddress()"];
    /// IAddressValidationSvc >> IRegistrationContactSvc [label="return ValidatedAddress"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="UpdateAddress()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void UpdateRegistrationContactAddress(Address address, long contactId);

    /// <summary>
    /// Removes an address from a registration contact.
    /// </summary>
    /// <param name="addressId">The ID of the address to remove.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="RemoveRegistrationContactAddress()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="RemoveAddress()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void RemoveRegistrationContactAddress(long addressId, long contactId);

    /// <summary>
    /// Adds a phone number to a registration contact.
    /// </summary>
    /// <param name="phoneNumber">The phone number to add.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \n Svc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="AddRegistrationContactPhoneNumber()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="AddPhoneNumber()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void AddRegistrationContactPhoneNumber(PhoneNumber phoneNumber, long contactId);

    /// <summary>
    /// Updates a phone number of a registration contact.
    /// </summary>
    /// <param name="phoneNumber">The phone number to update.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \nSvc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="UpdateRegistrationContactPhoneNumber()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="UpdatePhoneNumber()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void UpdateRegistrationContactPhoneNumber(PhoneNumber phoneNumber, long contactId);

    /// <summary>
    /// Removes a phone number from a registration contact.
    /// </summary>
    /// <param name="phoneNumberId">The ID of the phone number to remove.</param>
    /// <param name="contactId">The ID of the registration contact.</param>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \nSvc"], IRegistrationContactRepository [label="IRegistration \n Contact \n Repository"];
    /// Actor => IRegistrationContactSvc [label="RemoveRegistrationContactPhoneNumber()"];
    /// IRegistrationContactSvc => IRegistrationContactRepository [label="RemovePhoneNumber()"];
    /// IRegistrationContactRepository >> IRegistrationContactSvc [label="return"];
    /// IRegistrationContactSvc >> Actor [label="return"];
    /// \endmsc
    public void RemoveRegistrationContactPhoneNumber(long phoneNumberId, long contactId);

    /// <summary>
    /// Gets all registration contact types.
    /// </summary>
    /// <returns>A collection of all registration contact types.</returns>
    ///
    /// \msc
    /// Actor, IRegistrationContactSvc [label="IRegistration \n Contact \nSvc"], IRegistrationContactTypeRepository [label="IRegistration \n Contact \n Type \n Repository"];
    /// Actor => IRegistrationContactSvc [label="GetAllRegistrationContactTypes()"];
    /// IRegistrationContactSvc => IRegistrationContactTypeRepository [label="GetAll()"];
    /// IRegistrationContactTypeRepository >> IRegistrationContactSvc [label="return IEnumerable<RegistrationContactType>"];
    /// IRegistrationContactSvc >> Actor [label="return IEnumerable<RegistrationContactType>"];
    /// \endmsc
    public Task<IEnumerable<RegistrationContactType>> GetAllRegistrationContactTypes();
}
