using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Services.Common.BusinessRules.Models;
public class DecisionsResponse
{
    protected DecisionsResponse()
    {
        Result = new List<WorkFlowError>();
    }
    protected DecisionsResponse(List<WorkFlowError> result)
    {
        Result = result ?? new List<WorkFlowError>();
    }
    public List<WorkFlowError> Result { get; init; }

    public List<NotificationTrigger>? Notifications { get; set; }
}
