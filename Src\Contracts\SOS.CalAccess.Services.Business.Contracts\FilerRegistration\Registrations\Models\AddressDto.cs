using System.ComponentModel.DataAnnotations;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Address information.
/// </summary>
public sealed class AddressDto
{
    public AddressDto()
    {
    }

    public AddressDto(Address address)
    {
        Id = address.Id;
        Street = address.Street;
        Street2 = address.Street2;
        City = address.City;
        State = address.State;
        Zip = address.Zip;
        Type = address.Type;
        Country = address.Country;
        Purpose = address.Purpose;
    }

    /// <summary>
    /// Gets or sets the unique identifier of the address.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the street.
    /// </summary>
    public string? Street { get; set; }

    /// <summary>
    /// Gets or sets the second line of the street address.
    /// </summary>
    [Display(Name = "Street 2")]
    public string? Street2 { get; set; }

    /// <summary>
    /// Gets or sets the city.
    /// </summary>
    [Display(Name = "City")]
    public string? City { get; set; }

    /// <summary>
    /// Gets or sets the state.
    /// </summary>
    public string? State { get; set; }

    /// <summary>
    /// Gets or sets the ZIP code.
    /// </summary>
    [Display(Name = "ZIP")]
    public string? Zip { get; set; }

    /// <summary>
    /// Gets or sets the Type of address.
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Gets or sets the Country.
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Gets or sets the Mailing or Candidate address.
    /// </summary>
    public string? Purpose { get; set; }
}
