using System.ComponentModel.DataAnnotations;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for response body of CandidateRegistrationModel.SearchCandidateByName API.
/// </summary>
public class CandidateDto
{
    [Required]
    public long Id { get; set; }
    /// <summary>
    /// Gets or sets the candidate's First name.
    /// </summary>
    [Required]
    public string FirstName { get; set; }
    /// <summary>
    /// Gets or sets the candidate's middle name.
    /// </summary>
    public string? MiddleName { get; set; }
    /// <summary>
    /// Gets or sets the candidate's middle initial.
    /// </summary>
    public string? MiddleInitial { get; set; }

    /// <summary>
    /// Gets or sets the candidate's last name.
    /// </summary>
    [Required]
    public string LastName { get; set; }

    /// <summary>
    /// Gets or sets the candidate's email.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the candidate's address.
    /// </summary>
    public AddressDto? CandidateAddress { get; set; }

    /// <summary>
    /// Gets or sets the candidate's mailing address.
    /// </summary>
    public AddressDto? MailingAddress { get; set; }

    /// <summary>
    /// Gets or sets the candidate's phone number.
    /// </summary>
    public PhoneNumberDto? Phone { get; set; }

    /// <summary>
    /// Gets or sets the candidate's fax number.
    /// </summary>
    public PhoneNumberDto? Fax { get; set; }

    public CandidateDto()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
    }

    public CandidateDto(Candidate candidate)
    {
        ArgumentNullException.ThrowIfNull(candidate);

        ArgumentNullException.ThrowIfNull(candidate.User);

        Id = candidate.Id;
        FirstName = candidate.User!.FirstName;
        MiddleName = candidate.User?.MiddleName;
        MiddleInitial = candidate.User?.MiddleName?[0].ToString();
        LastName = candidate.User!.LastName;
        Email = candidate.User.EmailAddress;
    }
}
