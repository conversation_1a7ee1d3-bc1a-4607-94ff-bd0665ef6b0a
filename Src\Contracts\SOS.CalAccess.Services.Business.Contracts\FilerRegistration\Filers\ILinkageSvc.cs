
using Refit;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SendLinkageRequestToEmailDto = SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models.SendLinkageRequestToEmailDto;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers;

/// <summary>
/// Defines the service contract for handling linkage requests.
/// </summary>
public interface ILinkageSvc
{
    /// <summary>
    /// Gets the list of linkages for the currently authenticated user, including current, pending, and rejected linkages.
    /// </summary>
    /// <returns>A <see cref="LinkageResponseDto"/> containing categorized linkage data.</returns>
    [Get(GetLinkagesPath)]
    Task<LinkageResponseDto> GetLinkages();
    const string GetLinkagesPath = "/api/Linkages";

    /// <summary>
    /// Sends a linkage request to a person's email address.
    /// </summary>
    /// <param name="SendLinkageRequestToEmailDto">The linkage request details.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    [Post(SendLinkageRequestToEmailPath)]
    Task<long> SendLinkageRequestToEmail(SendLinkageRequestToEmailDto data);
    const string SendLinkageRequestToEmailPath = "/api/Linkages/SendLinkageRequestToEmail";

    /// <summary>
    /// Sends a linkage request to a person.
    /// </summary>
    /// <param name="SendLinkageRequestToPersonDto">The linkage request details.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    [Post(SendLinkageRequestToPersonPath)]
    Task<long> SendLinkageRequestToPerson(SendLinkageRequestToPersonDto data);
    const string SendLinkageRequestToPersonPath = "/api/Linkages/SendLinkageRequestToPerson";


    /// <summary>
    /// Searches for filer officers by name or ID, filtered by filer type, and returns simplified response data for linkage purposes.
    /// </summary>   
    /// <param name="filerSearchRequestDto">The filer search request to filter the search results.</param>    
    /// <returns>A task that represents the asynchronous operation. The task result contains a collection of <see cref="LinkageFilerSearchResponseDto"/> objects with relevant officer and filer details.</returns>
    [Get(GetFilersBySearchCriteriaPath)]
    Task<IEnumerable<LinkageFilerSearchResponseDto>> GetFilersBySearchCriteria(FilerSearchRequestDto filerSearchRequestDto);
    const string GetFilersBySearchCriteriaPath = "/api/Linkages/Filers/Search";


    /// <summary>
    /// Creates a new linkage request for filer.
    /// </summary>
    /// <param name="sendLinkageRequestToFilerDto">The linkage request details.</param>
    /// <returns>The created entity ID.</returns>
    [Post(SendLinkageRequestToFilerPath)]
    Task<long> SendLinkageRequestToFiler(SendLinkageRequestToFilerDto data);
    const string SendLinkageRequestToFilerPath = "/api/Linkages/SendLinkageRequestToFiler";

    /// <summary>
    /// Verify linkage code and return linkage information.
    /// </summary>
    /// <param name="request">A <see cref="LinkageReviewRequestDto"/> reqest.</param>
    /// <returns>A <see cref="LinkageReviewResponseDto"/> containing linkage data.</returns>
    [Post(GetLinkageRequestForReviewPath)]
    Task<LinkageReviewResponseDto> GetLinkageRequestForReview(LinkageReviewRequestDto request);
    const string GetLinkageRequestForReviewPath = "/api/Filer/Linkage/GetLinkageRequestForReview";


    /// <summary>
    /// Retrieves pending user linkage requests for the specified filer.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a collection of pending <see cref="LinkageReviewResponseDto"/>.</returns>
    [Get(PendingUserLinkagesPath)]
    Task<IEnumerable<LinkageRequestDto>> GetPendingUserLinkages(long filerId);
    const string PendingUserLinkagesPath = "/api/Linkages/PendingUserLinkages";

    /// <summary>
    /// Retrieves the currently authorized users associated with the specified filer.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a collection of <see cref="LinkageReviewResponseDto"/> for authorized users.</returns>
    [Get(CurrentAuthorizedUsersPath)]
    Task<IEnumerable<LinkageDto>> GetCurrentAuthorizedUsers(long filerId);
    const string CurrentAuthorizedUsersPath = "/api/Linkages/CurrentAuthorizedUsers";

    /// <summary>
    /// Retrieves the list of officers for the specified filer.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a collection of <see cref="OfficerResponseDto"/>.</returns>
    [Get(OfficersPath)]
    Task<IEnumerable<OfficerResponseDto>> GetOfficers(long filerId);
    const string OfficersPath = "/api/Linkages/Officers";

    /// <summary>
    /// Terminates an existing FilerUser
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <param name="filerUserId">The unique identifier of the filer user.</param>
    /// <returns>A task that represents the asynchronous operation. The task will complete successfully or throw an exception.</returns>
    [Delete(TerminateFilerUserPath)]
    Task TerminateFilerUser(long filerId, long filerUserId);
    const string TerminateFilerUserPath = "/api/Linkages/FilerUser/Terminate";

    /// <summary>
    /// Terminate linkage by Id and setting status Id to canceled
    /// </summary>
    /// <param name=filerUserId">A <see cref="long"/> filerUserId </param>
    /// <returns>/> successful task </returns>
    [Delete(TerminateLinkageForUserPath)]
    Task TerminateLinkageForUser(long filerUserId);
    const string TerminateLinkageForUserPath = "/api/Linkage/TerminateLinkageForUser/{filerUserId}";

    /// <summary>
    /// Accepts a pending linkage request with the specified linkage ID.
    /// </summary>
    /// <param name="linkageId">The unique identifier of the linkage request to accept.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Put(AcceptLinkageRequestPath)]
    Task AcceptLinkageRequest(long linkageId);
    const string AcceptLinkageRequestPath = "/api/Linkages/{linkageId}/accept";

    /// <summary>
    /// Rejects a pending linkage request with the specified linkage ID.
    /// </summary>
    /// <param name="linkageId">The unique identifier of the linkage request to reject.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Put(RejectLinkageRequestPath)]
    Task RejectLinkageRequest(long linkageId);
    const string RejectLinkageRequestPath = "/api/Linkages/{linkageId}/reject";
}
