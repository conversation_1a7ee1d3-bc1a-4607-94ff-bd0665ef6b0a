﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\SOS.CalAccess.Foundation\SOS.CalAccess.Foundation.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Logging\SOS.CalAccess.Logging.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Models\SOS.CalAccess.Models.csproj" />
    <ProjectReference Include="..\..\Contracts\SOS.CalAccess.Services.Business.Contracts\SOS.CalAccess.Services.Business.Contracts.csproj" />
    <ProjectReference Include="..\..\Clients\SOS.CalAccess.Services.Business.WebApiClient\SOS.CalAccess.Services.Business.WebApiClient.csproj" />
    <ProjectReference Include="..\..\Contracts\SOS.CalAccess.Services.Technical.Contracts\SOS.CalAccess.Services.Technical.Contracts.csproj" />
    <ProjectReference Include="..\..\Clients\SOS.CalAccess.Services.Technical.WebApiClient\SOS.CalAccess.Services.Technical.WebApiClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
  </ItemGroup>

</Project>
