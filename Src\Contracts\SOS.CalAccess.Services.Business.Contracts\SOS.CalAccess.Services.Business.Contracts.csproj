<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>SOS.CalAccess.Services.Business</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SystemAdministration\Configure Limits\**" />
    <EmbeddedResource Remove="SystemAdministration\Configure Limits\**" />
    <None Remove="SystemAdministration\Configure Limits\**" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Correspondence\" />
    <Folder Include="FinancialTransactions\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Refit" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\SOS.CalAccess.Models\SOS.CalAccess.Models.csproj" />
    <ProjectReference Include="..\SOS.CalAccess.Data\SOS.CalAccess.Data.Contracts.csproj" />
    <ProjectReference Include="..\SOS.CalAccess.Services.Common.Contracts\SOS.CalAccess.Services.Common.Contracts.csproj" />
  </ItemGroup>

</Project>
