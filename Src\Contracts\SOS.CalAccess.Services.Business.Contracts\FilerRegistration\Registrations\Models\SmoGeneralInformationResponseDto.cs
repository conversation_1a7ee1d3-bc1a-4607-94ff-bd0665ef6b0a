namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Smo General Information
/// </summary>
public sealed record SmoGeneralInformationResponseDto
{
    public SmoGeneralInformationResponseDto(SmoRegistrationResponseDto? smoRegistrationDetail, CommitteeSearchResultDto? smoCommitteeDetail, SmoRegistrationContactDto? smoTreasurerDetail)
    {
        RegistrationDetail = smoRegistrationDetail;
        CommitteeDetail = smoCommitteeDetail;
        Treasurer = smoTreasurerDetail;
    }
    public SmoGeneralInformationResponseDto()
    {
    }

    /// <summary>
    /// Gets or sets the smo detail
    /// </summary>
    public SmoRegistrationResponseDto? RegistrationDetail { get; set; }

    /// <summary>
    /// Gets or sets the committee detail
    /// </summary>
    public CommitteeSearchResultDto? CommitteeDetail { get; set; }

    /// <summary>
    /// Gets or sets the treasurer information.
    /// </summary>
    public SmoRegistrationContactDto? Treasurer { get; set; }

}
