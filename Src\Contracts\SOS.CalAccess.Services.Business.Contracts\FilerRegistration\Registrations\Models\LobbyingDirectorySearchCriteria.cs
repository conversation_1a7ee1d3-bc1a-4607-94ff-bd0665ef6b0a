using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

public class LobbyingDirectorySearchCriteria
{

    /// <summary>
    /// Name of the Lobbyist or Lobbyist Employer
    /// </summary>
    [Required]
    public string? LobbyistName { get; set; }

    /// <summary>
    /// From Date 
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// To Date 
    /// </summary>
    public DateTime? ToDate { get; set; }







}
