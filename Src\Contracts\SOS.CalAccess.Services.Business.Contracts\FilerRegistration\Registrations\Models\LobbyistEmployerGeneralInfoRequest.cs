namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// LobbyistEmployerGeneralInfoRequest.
/// </summary>
public sealed record LobbyistEmployerGeneralInfoRequest : IHasBusinessMailingAddresses
{
    /// <summary>
    /// Gets or sets the Original Id.
    /// </summary>
    public long? OriginalId { get; set; }

    /// <summary>
    /// Gets or sets the EmployerName.
    /// </summary>
    public string? EmployerName { get; set; }

    /// <summary>
    /// Gets or sets the Email.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the PhoneNumber.
    /// </summary>
    public PhoneNumberDto? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the FaxNumber.
    /// </summary>
    public PhoneNumberDto? FaxNumber { get; set; }

    /// <summary>
    /// Gets or sets the BusinessAddress.
    /// </summary>
    public AddressDto? BusinessAddress { get; set; }

    /// <summary>
    /// Gets or sets the MailingAddress.
    /// </summary>
    public AddressDto? MailingAddress { get; set; }

    /// <summary>
    /// Gets or sets the LegislativeSessionId.
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets or sets the QualificationDate.
    /// </summary>
    public DateTime? QualificationDate { get; set; }

    /// <summary>
    /// Gets or sets the IsLobbyingCoalition.
    /// </summary>
    public bool? IsLobbyingCoalition { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

}
