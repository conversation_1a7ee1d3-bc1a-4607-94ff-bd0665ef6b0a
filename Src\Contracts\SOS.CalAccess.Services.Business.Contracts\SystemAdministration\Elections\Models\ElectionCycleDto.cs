

namespace SOS.CalAccess.Services.Business.SystemAdministration.Elections.Models;
/// <summary>
/// Represents information about a specific election cycle.
/// </summary>
public class ElectionCycleDto
{
    /// <summary>
    /// The start date of the election cycle.
    /// </summary>
    public required DateTime ElectionCycleStartDate { get; set; }

    /// <summary>
    /// The start date of the election cycle..
    /// </summary>
    public required DateTime ElectionCycleEndDate { get; set; }

    /// <summary>
    /// Description of Election Cycle  = 2024-2025 
    /// </summary>
    public required string Description { get; set; }


    /// <summary>
    /// Name represents the starting year of the cycle - 2024
    /// </summary>
    public required string Name { get; set; }



}
