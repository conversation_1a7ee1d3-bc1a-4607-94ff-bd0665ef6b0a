openapi: 3.0.3
info:
  title: Form 496 - Independent Expenditure Report
  description: |
    API for submitting Form 496, the 24-hour/10-day Independent Expenditure Report.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/IndependentExpenditureReport/{filerId}:
    post:
      summary: Submit Form 496
      description: Submit the 24-hour/10-day Independent Expenditure Report (Form 496).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IndependentExpenditureReport'
      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    IndependentExpenditureReport:
      type: object
      properties:
        dateOfFiling:
          type: string
          format: date
        submittedDate:
          type: string
          format: date
        reportNumber:
          type: string
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
        filerDetails:
          $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
        candidateOrMeasure:
          oneOf:
          - $ref: './common-schemas.yaml#/components/schemas/Candidate'
          - $ref: './common-schemas.yaml#/components/schemas/Measure'
        independentExpenditures:
          type: array
          items:
            $ref: '#/components/schemas/IndependentExpenditure'
        contributionsReceived:
          type: array
          items:
            $ref: '#/components/schemas/Contribution'
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - filerDetails
        - candidateOrMeasure
        - independentExpenditures
        - attestation

    IndependentExpenditure:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of the expenditure.
        description:
          type: string
          description: Description of the expenditure (e.g., radio ad, billboard, mailing).
        amount:
          type: number
          format: double
          description: Amount of the expenditure.
      required:
        - date
        - description
        - amount

    Contribution:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date the contribution was received.
        firstName:
          type: string
          description: First name of the contributor.
        lastName:
          type: string
          description: Last name of the contributor.
        committeeId:
          type: string
          description: Committee Id if contributor is a Committee .
        contributorAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        contributorType:
          type: string
          # enum:
          #   - Individual
          #   - Committee
          #   - Other
          #   - PoliticalParty
          #   - SmallContributorCommittee
          description: Type of contributor.
        occupation:
          type: string
          description: Contributor's occupation (if individual).
        employer:
          type: string
          description: Employer of the contributor (if individual).
        amount:
          type: number
          format: double
          description: Amount of the contribution.
        interestRate:
          type: number
          format: double
          description: Interest rate (if the contribution is a loan).
      required:
        - date
        - firstName
        - contributorType
        - amount
