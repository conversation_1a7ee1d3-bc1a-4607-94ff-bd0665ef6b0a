using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration;

#region Design Notes
/// <summary>
/// Interface for the Candidate Service.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the services for the retrieval of candidate-related data for use in other processes.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating candidate data.
/// </p>
/// <p>
/// The service does not create or update candidate information.  Candidate information should be updated using the ICandidateIntentionRegistrationSvc.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the retrieval of candidate-related data.  
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                                 | Operation            | Description                  |
/// | --------------------------------------- | -------------------- | ---------------------------- |
/// | ICandidateRepository               | FindById               | Retrieves candidate by id |
#endregion

public interface ICandidateSvc
{
    /// <summary>
    /// Searches for candidates based on their name.
    /// </summary>
    /// <param name="q">The search query string containing the candidate's name or part of the name.</param>
    /// <returns>Returns a list of candidates matching the search criteria.</returns>
    /// \msc
    /// Actor, ICandidateSvc [label="ICandidate \n Svc"], ICandidateRepository [label="ICandidate \n Repository"];
    /// Actor => ICandidateSvc [label="SearchCandidateByName()"];
    /// ICandidateSvc => ICandidateRepository [label="FindCandidateRegistrationsWithElectionByName()"];
    /// ICandidateRepository >> ICandidateSvc [label="return candidates"];
    /// ICandidateSvc >> Actor [label="return CandidateSearchResultDtos"];
    /// \endmsc
    [Get(SearchCandidateByNamePath)]
    Task<IEnumerable<CandidateSearchResultDto>> SearchCandidateByName(string q);
    const string SearchCandidateByNamePath = "/api/Candidate/Search";

    /// <summary>
    /// Gets Candidate Information from the database
    /// </summary>
    /// <param name="id">CandidateId PK</param>
    /// <returns></returns>
    /// \msc
    /// Actor, ICandidateSvc [label="ICandidate \n Svc"], ICandidateRepository [label="ICandidate \n Repository"];
    /// Actor => ICandidateSvc [label="GetCandidateById()"];
    /// ICandidateSvc => ICandidateRepository [label="FindCandidateById()"];
    /// ICandidateRepository >> ICandidateSvc [label="return candidate"];
    /// ICandidateSvc >> Actor [label="return CandidateDto"];
    /// \endmsc
    [Get(GetCandidateByIdPath)]
    Task<CandidateDto?> GetCandidateById(long id);
    const string GetCandidateByIdPath = "/api/Candidate/Info/{id}";
}
