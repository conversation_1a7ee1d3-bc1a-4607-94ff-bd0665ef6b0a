using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SOS.CalAccess.Efile.Processing;

IHost host = new HostBuilder()
    .ConfigureAppConfiguration((context, config) =>
    {
        string environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Development";
        bool isDevelopment = environment.Equals("Development", StringComparison.OrdinalIgnoreCase);
        if (isDevelopment)
        {
            config.SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("local.settings.json", optional: false, reloadOnChange: true);
        }

        config.AddEnvironmentVariables();

        // Manually add the "AzureValues" section to the configuration.
        IConfigurationRoot? configRoot = config.Build();
        IConfigurationSection? azureValues = configRoot?.GetSection("AzureValues");
        if (azureValues?.Exists() == true)
        {
            config.AddInMemoryCollection(azureValues.AsEnumerable());
        }
    })
    .ConfigureFunctionsWebApplication()
    .ConfigureServices(services =>
    {
        services.AddApplicationInsightsTelemetryWorkerService(options =>
        {
            options.ConnectionString = Environment.GetEnvironmentVariable("AppInsights_ConnectionString");
        });
        DependencyInjectionConfiguration setupDI = new(services);
        setupDI.AddRepositories();
        setupDI.AddCommonServices();
        setupDI.AddBusinessServices();
        setupDI.AddMaplightAcceleratorClasses();
        services.ConfigureFunctionsApplicationInsights();
    })
    .Build();

host.Run();
