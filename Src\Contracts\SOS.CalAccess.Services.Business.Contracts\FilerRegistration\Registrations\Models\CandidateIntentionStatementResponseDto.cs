using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;


namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

public class CandidateIntentionStatementResponseDto
{
    public CandidateIntentionStatementResponseDto() { }

    public CandidateIntentionStatementResponseDto(CandidateIntentionStatement candidateIntentionStatement)
    {
        ArgumentNullException.ThrowIfNull(candidateIntentionStatement);
        Id = candidateIntentionStatement.Id;
        Email = candidateIntentionStatement.Email;
        FirstName = candidateIntentionStatement.FirstName;
        LastName = candidateIntentionStatement.LastName;
        MiddleName = candidateIntentionStatement.MiddleName;
        IsSameAsCandidateAddress = candidateIntentionStatement.IsSameAsCandidateAddress;
        PreviousCandidate = candidateIntentionStatement.PreviousCandidate;
        SelfRegister = candidateIntentionStatement.SelfRegister;
        ExpenditureCeilingAmount = candidateIntentionStatement.ExpenditureCeilingAmount;
        ExpenditureLimitAccepted = candidateIntentionStatement.ExpenditureLimitAccepted;
        ExpenditureExceeded = candidateIntentionStatement.ExpenditureExceeded;
        ContributedPersonalExcessFundsOn = candidateIntentionStatement.ContributedPersonalExcessFundsOn;
        ElectionCounty = candidateIntentionStatement.ElectionCounty;
        ElectionRaceId = candidateIntentionStatement.ElectionRaceId;
        ElectionJurisdiction = candidateIntentionStatement.ElectionJurisdiction;
        ElectionOfficeSought = candidateIntentionStatement.ElectionOfficeSought;
        ElectionDistrictNumber = candidateIntentionStatement.ElectionDistrictNumber;
        PoliticalPartyId = candidateIntentionStatement.PoliticalPartyId;
        CandidateId = candidateIntentionStatement.CandidateId;
        OriginalId = candidateIntentionStatement.OriginalId;
        ParentId = candidateIntentionStatement.ParentId;
        StatusId = candidateIntentionStatement.StatusId;
        FilerId = candidateIntentionStatement.FilerId;
        Version = candidateIntentionStatement.Version;
        SetMailingAddress(candidateIntentionStatement.AddressList);
        SetCandidateAddress(candidateIntentionStatement.AddressList);
        SetPhoneNumber(candidateIntentionStatement.PhoneNumberList);
        SetFaxNumber(candidateIntentionStatement.PhoneNumberList);
    }

    private void SetPhoneNumber(PhoneNumberList? phoneNumberList)
    {
        if (phoneNumberList == null || phoneNumberList.PhoneNumbers.Count == 0)
        {
            return;
        }

        PhoneNumber = new(phoneNumberList.PhoneNumbers.Find(a => a.Type == "Home"));
    }

    private void SetFaxNumber(PhoneNumberList? faxNumberList)
    {
        if (faxNumberList == null || faxNumberList.PhoneNumbers.Count == 0)
        {
            return;
        }

        FaxNumber = new(faxNumberList.PhoneNumbers.Find(a => a.Type == "Fax"));
    }

    public PhoneNumberDto? PhoneNumber { get; set; }

    public PhoneNumberDto? FaxNumber { get; set; }
    private void SetMailingAddress(AddressList? addressList)
    {
        if (addressList == null || addressList.Addresses.Count == 0)
        {
            return;
        }

        var address = addressList.Addresses.Find(a => a.Purpose == "Mailing");
        if (address == null)
        {
            return;
        }

        MailingAddress = new AddressDto
        {
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            State = address.State,
            Country = address.Country,
            Zip = address.Zip,
            Type = address.Type,
            Purpose = address.Purpose,
        };
    }

    private void SetCandidateAddress(AddressList? addressList)
    {
        if (addressList == null || addressList.Addresses.Count == 0)
        {
            return;
        }

        var address = addressList.Addresses.Find(a => a.Purpose == "Candidate");

        if (address == null)
        {
            return;
        }

        CandidateAddress = new AddressDto
        {
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            State = address.State,
            Country = address.Country,
            Zip = address.Zip,
            Type = address.Type,
            Purpose = address.Purpose,
        };
    }

    public AddressList? AddressList { get; set; }
    public AddressDto? MailingAddress { get; set; }
    public AddressDto? CandidateAddress { get; set; }
    public DateTime? ContributedPersonalExcessFundsOn { get; set; }
    public DateTime? VerificationExecutedAt { get; set; }
    public bool ExpenditureExceeded { get; set; }
    public bool ExpenditureLimitAccepted { get; set; }
    public bool IsSameAsCandidateAddress { get; set; }
    public bool PreviousCandidate { get; set; }
    public bool SelfRegister { get; set; }
    public decimal ExpenditureCeilingAmount { get; set; }
    public long Id { get; set; }
    public long StatusId { get; set; }
    public long? CandidateId { get; set; }
    public long? ElectionRaceId { get; set; }
    public long? VerifiedById { get; set; }
    public string? ElectionCounty { get; set; }
    public string? ElectionJurisdiction { get; set; }
    public string? ElectionDistrictNumber { get; set; }
    public string? ElectionOfficeSought { get; set; }
    public long? PoliticalPartyId { get; set; }
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? MiddleName { get; set; }
    public long? OriginalId { get; set; }
    public long? ParentId { get; set; }
    public long? FilerId { get; set; }
    public string? VerificationSignature { get; set; }
    public int? Version { get; set; }
}
