using SOS.CalAccess.Models.FilerRegistration.Filers;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
public class FilerDto
{
    /// <summary>
    /// Gets or sets Id
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// Gets or sets FilerStatusId
    /// </summary>
    public long FilerStatusId { get; set; }
    /// <summary>
    /// Gets or sets CurrentRegistrationId
    /// </summary>
    public long CurrentRegistrationId { get; set; }
    /// <summary>
    /// Gets or sets EffectiveDate
    /// </summary>
    public DateTime? EffectiveDate { get; set; }
    /// <summary>
    /// Gets or sets FilerTypeId
    /// </summary>
    public long FilerTypeId { get; set; }

    public FilerDto() { }

    public FilerDto(Filer input)
    {
        Id = input.Id;
        FilerStatusId = input.FilerStatusId;
        CurrentRegistrationId = input.CurrentRegistrationId;
        EffectiveDate = input.EffectiveDate;
        FilerTypeId = input.FilerTypeId;
    }
}
