using System.Collections.Immutable;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;

public static class RegistrationStatusMapping
{
    public static readonly ImmutableDictionary<string, RegistrationStatus> RegistrationStatusByName = new Dictionary<string, RegistrationStatus>
    {
        [RegistrationStatus.Draft.Name] = RegistrationStatus.Draft,
        [RegistrationStatus.Submitted.Name] = RegistrationStatus.Submitted,
        [RegistrationStatus.Accepted.Name] = RegistrationStatus.Accepted,
        [RegistrationStatus.Rejected.Name] = RegistrationStatus.Rejected,
        [RegistrationStatus.Terminated.Name] = RegistrationStatus.Terminated,
        [RegistrationStatus.Canceled.Name] = RegistrationStatus.Canceled,
        [RegistrationStatus.HoldForPRDReview.Name] = RegistrationStatus.HoldForPRDReview,
        [RegistrationStatus.Pending.Name] = RegistrationStatus.Pending,
        [RegistrationStatus.Withdrawn.Name] = RegistrationStatus.Withdrawn,
    }.ToImmutableDictionary();

    public static long GetIdByName(string name)
    {
        if (RegistrationStatusByName.TryGetValue(name, out var status))
        {
            return status.Id;
        }

        throw new ArgumentException($"Invalid registration status name: {name}", nameof(name));
    }

}
