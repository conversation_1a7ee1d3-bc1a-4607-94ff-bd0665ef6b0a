using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for Vendor Registration Vendor
/// </summary>
public class Vendor
{

    public long Id { get; set; }
    /// <summary>
    /// Gets or sets the Vendor's First name.
    /// </summary>
    [Required]
    public required string FirstName { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's middle initial.
    /// </summary>
    public string? MiddleInitial { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's last name.
    /// </summary>
    [Required]
    public required string LastName { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's email.
    /// </summary>
    [Required]
    public required string? Email { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's phone.
    /// </summary>
    [Required]
    public required PhoneNumberDto Phone { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's phone.
    /// </summary>
    [Required]
    public required AddressDto VendorAddress { get; set; }

    //is Address Type Residential or Business- stored in purpose ?

    /// <summary>
    /// Gets or sets the Vendor's Point of Contact.
    /// </summary>
    [Required]
    public required VendorPointOfContact PointOfContact { get; set; }


    /// <summary>
    /// Gets or sets the Vendor's NameOfSystem.
    /// </summary>
    [Required]
    public required string NameOfSystem { get; set; }

    /// <summary>
    /// Gets or sets the Vendor's Major Release Version Number.
    /// </summary>
    [Required]
    public required string MajorReleaseVersionNumber { get; set; }


    /// <summary>
    /// Gets or sets the List of forms that the Vendor is certified for
    /// </summary>
    [Required]
    public required IEnumerable<FppcForm> ListOfForms { get; set; }





}
