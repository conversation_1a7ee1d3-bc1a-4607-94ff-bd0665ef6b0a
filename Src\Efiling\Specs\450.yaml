openapi: 3.0.3
info:
  title: Form 450 - Recipient Committee Campaign Statement
  description: |
    This API is designed to handle submissions of the Form 450, a Recipient Committee Campaign Statement - Short Form.
    Form details include committee information, expenditures, contributions, and verifications.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/RecipientCommittee/Short/{filerId}:
    post:
      summary: Submit Form 450
      description: Submit the Recipient Committee Campaign Statement - Short Form (Form 450).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecipientCommitteeCampaignStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:

    RecipientCommitteeCampaignStatement:
      type: object
      properties:
        periodFrom:
          type: string
          format: date
          description: Statement covers period from.
        periodThrough:
          type: string
          format: date
          description: Statement covers period through.
        dateOfElection:
          type: string
          format: date
          description: Date of the election, if applicable.
        typeOfRecipientCommittee:
          type: string
          # enum:
          #   - Ballot Measure Committee - Primarily Formed
          #   - Ballot Measure Committee - Controlled
          #   - Ballot Measure Committee - Sponsored
          #   - Primarily Formed Candidate/Officeholder Committee
          #   - General Purpose Committee - Sponsored
          #   - General Purpose Committee - Small Contributor Committee
          description: Type of recipent committee.
        expendituresMade:
          $ref: '#/components/schemas/Expenditures'
        contributionsReceived:
          $ref: '#/components/schemas/Contributions'
        cashStatement:
          $ref: '#/components/schemas/CashStatement'

        paymentsMade:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
          description: List of payments made during the period.

        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          type: array
          items:
            $ref: './common-schemas.yaml#/components/schemas/Attestation'
          description: Up to 4 separate attestations
      required:
        - attestation

    Expenditures:
      type: object
      properties:
        over100:
          type: number
          format: double
          description: Expenditures of $100 or more made this period.
        under100:
          type: number
          format: double
          description: Expenditures under $100 made this period.
        subtotal:
          type: number
          format: double
          description: Subtotal of expenditures made this period.
        nonmonetaryAdjustment:
          type: number
          format: double
          description: Nonmonetary adjustment.
        previousStatementTotalExpenditures:
          type: number
          format: double
          description: Total expenditures made from previous statement (If this is the first statement for the calendar year, enter zero.)
        totalToDate:
          type: number
          format: double
          description: Total expenditures made to date.
      required:
        - over100
        - under100
        - subtotal
        - totalToDate

    Contributions:
      type: object
      properties:
        monetary:
          type: number
          format: double
          description: Monetary contributions received this period.
        nonMonetary:
          type: number
          format: double
          description: Non-monetary contributions received this period.
        previousStatementTotalContributions:
          type: number
          format: double
          description: Total contributions received from previous statement. (If this is the first statement for the calendar year, enter zero.)
        totalToDate:
          type: number
          format: double
          description: Total contributions received to date.
      required:
        - monetary
        - nonMonetary
        - totalToDate

    CashStatement:
      type: object
      properties:
        beginningBalance:
          type: number
          format: double
          description: Beginning cash balance for the period.
        cashReceipts:
          type: number
          format: double
          description: Total cash receipts this period.
        miscellaneousIncreases:
          type: number
          format: double
          description: Miscellaneous increases to cash this period.
        cashExpenditures:
          type: number
          format: double
          description: Total cash expenditures this period.
        endingBalance:
          type: number
          format: double
          description: Ending cash balance for the period.
      required:
        - beginningBalance
        - cashReceipts
        - cashExpenditures
        - endingBalance

    Payment:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of payment.
        payee:
          type: string
          description: Name of the payee.
        address:
          allOf:
            - $ref: './common-schemas.yaml#/components/schemas/Address'
          description: Address of the payee.
        committeeId:
          type: integer
          format: int64
        description:
          type: string
          description: Description of the payment.
        amount:
          type: number
          format: double
          description: Amount of the payment.
        calendarYearCumulativeToDate:
          type: number
          format: double
          description: Cumulative amount paid to date for calendar year.
        otherCumulativeToDate:
          format: string
          description: Cumulative amount paid to date for other. May contain a alphanumeric code to indicate the limitation cycle and year of election.
          example: "$4,200 P-16"
        candidateName:
          type: string
          description: Name of candidate.
        candidateOffice:
          type: string
          description: Name of candidate office.
        ballotMeasureName:
          type: string
          description: Name of ballot measure.
        ballotMeasureId:
          type: string
          description: Ballot measure number or letter and jurisdiction.
        supportOppose:
          type: string
          enum:
            - Support
            - Oppose
          description: Indicates if the payment supports or opposes a candidate or measure.
        type:
          type: string
          enum:
            - Contribution
            - Independent Expenditure
          description: Indicates the payment type.
      required:
        - date
        - payee
        - description
        - amount
