openapi: 3.0.3
info:
  title: Form 601 - Lobbying Firm Registration Statement
  description: |
    API for submitting Form 601, the Lobbying Firm Registration Statement. This form is used for the registration, renewal, or amendment of lobbying firm activities.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Registration/LobbyingFirm/{filerId}:
    post:
      summary: Submit Form 601
      description: Submit the Lobbying Firm Registration Statement (Form 601).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingFirmRegistrationStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingFirmRegistrationStatement:
      type: object
      properties:
        registrationType:
          type: string
          # enum:
          #   - Initial Registration
          #   - Renewal
          #   - Amendment
          description: The type of registration being submitted.
        filerDetails:
          $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
        dateQualified:
          type: string
          format: date
          description: If this is an initial registration, provide the Date Qualified as a Lobbying Firm.
        legislativeSession:
          type: string
          description: Legislative session for the registration (e.g., "2023-2024").
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
        lobbyistEmployers:
          type: array
          items:
            $ref: './602.yaml#/components/schemas/LobbyingFirmActivityAuthorization'
        individualLobbyists:
          type: array
          items:
            $ref: './604.yaml#/components/schemas/LobbyistCertificationStatement'
      required:
        - registrationType
        - legislativeSession
        - filerDetails
        - attestation

