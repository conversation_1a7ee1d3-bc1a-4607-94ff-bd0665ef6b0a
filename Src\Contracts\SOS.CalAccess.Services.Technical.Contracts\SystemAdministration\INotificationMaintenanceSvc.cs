using Refit;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Technical.SystemAdministration.Models;

namespace SOS.CalAccess.Services.Technical.SystemAdministration;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// NotificationMaintenenaceService is responsible for maintaining notfication templates and notification tempate translation that is going to be used by System Admin.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to handle all the functionality to Create , update, remove and View Notification Templates and their associated translations
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UN01</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// | Service                        | Operation                       | Description                                 |
/// | ------------------------------ | ------------------------------- | ------------------------------------------- |
/// | INotificationTemplateRepository | GetAll, GetById, Create, Update, Delete |                                             |
/// | INotificationTemplateTranslationRepository | GetAll, GetById, Create, Update, Delete |                                             |
#endregion

public interface INotificationMaintenanceSvc
{
    /// <summary>
    /// ListNotificationTemplates is called by System Admin to get a list of all Notification Templates
    /// </summary>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotification\nMaintenanceSvc"], INotificationTemplateRepository [label="INotification\nTemplateRepository"];
    /// Actor => INotificationMaintenanceSvc [label="ListNotificationTemplates()"];
    /// INotificationMaintenanceSvc => INotificationTemplateRepository [label="GetAll()"];
    /// INotificationTemplateRepository >> INotificationMaintenanceSvc [label="\nreturn List NotificationTemplates"];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn"];
    /// \endmsc
    [Get("/" + GetNotificationTemplatesPath)]
    Task<IEnumerable<NotificationTemplateDto>> GetNotificationTemplates();
    const string GetNotificationTemplatesPath = "api/admin/Notifications/Template";

    /// <summary>
    /// CreateNotificationTemplate is called by System Admin to create a new notification template
    /// </summary>
    /// <param name="notificationTemplate"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateRepository [label="INotification\nTemplateRepository"];
    /// Actor => INotificationMaintenanceSvc [label="CreateNotificationTemplate()"];
    /// INotificationMaintenanceSvc => INotificationTemplateRepository [label="Create()"];
    /// INotificationTemplateRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + CreateNotificationTemplatePath)]
    Task<NotificationTemplateDto> CreateNotificationTemplate([Body] NotificationTemplateDto notificationTemplate);
    const string CreateNotificationTemplatePath = "api/admin/Notifications/Template";

    /// <summary>
    /// ViewNotificationTemplate is called by System Admin to view a notification template
    /// </summary>
    /// <param name="notificationTemplateId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateRepository [label="INotification\nTemplateRepository"];
    /// Actor => INotificationMaintenanceSvc [label="ViewNotificationTemplate()"];
    /// INotificationMaintenanceSvc => INotificationTemplateRepository [label="GetById()"];
    /// INotificationTemplateRepository >> INotificationMaintenanceSvc [label="\nreturn  NotificationTemplate"];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn NotificationTemplate"];
    /// \endmsc
    [Get("/" + ViewNotificationTemplatePath)]
    Task<NotificationTemplateDto?> ViewNotificationTemplate(long notificationTemplateId);
    const string ViewNotificationTemplatePath = "api/admin/Notifications/Template/{notificationTemplateId}";

    /// <summary>
    /// UpdateNotificationTemplate is called by System Admin to update a notification template
    /// </summary>
    /// <param name="notificationTemplateId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateRepository [label="INotification\nTemplateRepository"];
    /// Actor => INotificationMaintenanceSvc [label="UpdateNotificationTemplate()"];
    /// INotificationMaintenanceSvc => INotificationTemplateRepository [label="Update()"];
    /// INotificationTemplateRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Put("/" + UpdateNotificationTemplatePath)]
    Task UpdateNotificationTemplate([Body] NotificationTemplateDto notificationTemplate);
    const string UpdateNotificationTemplatePath = "api/admin/Notifications/Template";

    /// <summary>
    /// RemoveNotificationTemplate is called by System Admin to remove a notification template
    /// </summary>
    /// <param name="notificationTemplateId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateRepository [label="INotification\nTemplateRepository"];
    /// Actor => INotificationMaintenanceSvc [label="RemoveNotificationTemplate()"];
    /// INotificationMaintenanceSvc => INotificationTemplateRepository [label="Delete()"];
    /// INotificationTemplateRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Delete("/" + RemoveNotificationTemplatePath)]
    Task RemoveNotificationTemplate(long notificationTemplateId);
    const string RemoveNotificationTemplatePath = "api/admin/Notifications/Template/{notificationTemplateId}";

    /// <summary>
    /// AddNotificationTemplateTranslation is called by System Admin to remove a notification template translation
    /// </summary>
    /// <param name="notificationTemplateTranslation"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateTranslationRepository [label="INotification\nTemplateTranslation\nRepository"];
    /// Actor => INotificationMaintenanceSvc [label="AddNotificationTemplateTranslation()"];
    /// INotificationMaintenanceSvc => INotificationTemplateTranslationRepository [label="Create()"];
    /// INotificationTemplateTranslationRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + AddNotificationTemplateTranslationPath)]
    Task<NotificationTemplateTranslation> AddNotificationTemplateTranslation([Body] NotificationTemplateTranslation notificationTemplateTranslation);
    const string AddNotificationTemplateTranslationPath = "api/admin/Notifications/Template/Translation";

    /// <summary>
    /// ViewNotificationTemplateTranslation is called by System Admin to view a notification template translation
    /// </summary>
    /// <param name="notificationTemplateTranslationId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateTranslationRepository [label="INotification\nTemplateTranslation\nRepository"];
    /// Actor => INotificationMaintenanceSvc [label="ViewNotificationTemplateTranslation()"];
    /// INotificationMaintenanceSvc => INotificationTemplateTranslationRepository [label="GetById()"];
    /// INotificationTemplateTranslationRepository >> INotificationMaintenanceSvc [label="\nreturn NotificationTemplateTranslation"];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn NotificationTemplateTranslation"];
    /// \endmsc
    [Get("/" + ViewNotificationTemplateTranslationPath)]
    Task<NotificationTemplateTranslation?> ViewNotificationTemplateTranslation(long notificationTemplateTranslationId);
    const string ViewNotificationTemplateTranslationPath = "api/admin/Notifications/Template/Translation/{notificationTemplateTranslationId}";

    /// <summary>
    /// UpdateNotificationTemplateTranslation is called by System Admin to update a notification template translation
    /// </summary>
    /// <param name="notificationTemplateTranslation"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateTranslationRepository [label="INotification\nTemplateTranslation\nRepository"];
    /// Actor => INotificationMaintenanceSvc [label="UpdateNotificationTemplateTranslation()"];
    /// INotificationMaintenanceSvc => INotificationTemplateTranslationRepository [label="Update()"];
    /// INotificationTemplateTranslationRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Put("/" + UpdateNotificationTemplateTranslationPath)]
    Task<NotificationTemplateTranslation> UpdateNotificationTemplateTranslation([Body] NotificationTemplateTranslation notificationTemplateTranslation);
    const string UpdateNotificationTemplateTranslationPath = "api/admin/Notifications/Template/Translation";


    /// <summary>
    /// RemoveNotificationTemplateTranslation is called by System Admin to remove a notification template translation
    /// </summary>
    /// <param name="notificationTemplateTranslationId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationMaintenanceSvc [label="INotificationMaintenance\nSvc"], INotificationTemplateTranslationRepository [label="INotification\nTemplateTranslation\nRepository"];
    /// Actor => INotificationMaintenanceSvc [label="RemoveNotificationTemplateTranslation()"];
    /// INotificationMaintenanceSvc => INotificationTemplateTranslationRepository [label="Delete()"];
    /// INotificationTemplateTranslationRepository >> INotificationMaintenanceSvc [label="\nreturn "];
    /// INotificationMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Delete("/" + RemoveNotificationTemplateTranslationPath)]
    Task RemoveNotificationTemplateTranslation(long notificationTemplateTranslationId);
    const string RemoveNotificationTemplateTranslationPath = "api/admin/Notifications/Template/Translation/{notificationTemplateTranslationId}";


}
