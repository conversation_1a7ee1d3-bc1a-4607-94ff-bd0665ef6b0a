using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for response body of SearchLobbyistEmployerOrLobbyingFirmByIdOrName API.
/// </summary>
public class LobbyistEmployerOrLobbyistFirmSearchResultDto
{
    /// <summary>
    /// Lobbyist Employer or Lobbyist Firm Id
    /// </summary>
    [Required]
    [JsonPropertyName("Id")]
    public required long Id { get; set; }

    /// <summary>
    /// Gets or sets the Lobbyist Employer or Lobbyist Firm name.
    /// </summary>
    [Required]
    [JsonPropertyName("Name")]
    public required string Name { get; set; }
}
