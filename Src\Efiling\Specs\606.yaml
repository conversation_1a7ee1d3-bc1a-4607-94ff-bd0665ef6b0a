openapi: 3.0.3
info:
  title: Notice of Termination (Form 606)
  description: |
    API for submitting Form 606, the Notice of Termination, required for lobbyists, lobbying firms, and lobbyist employers ceasing activities.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/NoticeOfTermination/{filerId}:
    post:
      summary: Submit Notice of Termination
      description: Submit the Notice of Termination (Form 606) as required by the Political Reform Act.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NoticeOfTermination'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    NoticeOfTermination:
      type: object
      properties:
        terminationDetails:
          $ref: '#/components/schemas/TerminationDetails'
        attestation:
          type: array
          items:
            $ref: './common-schemas.yaml#/components/schemas/Attestation'
          description: Up to 2 seperate attestations
      required:
        - terminationDetails
        - attestation

    TerminationDetails:
      type: object
      properties:
        terminatedAt:
          type: string
          format: date
          description: Effective date of termination.
        legislativeSession:
          type: string
          description: Legislative session covered by the termination (e.g., "2023-2024").
      required:
        - terminatedAt
