using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

public interface ICandidateIntentionDependencies
{
    IAuthorizationSvc AuthorizationSvc { get; }
    IDecisionsSvc DecisionsSvc { get; }
    INotificationSvc NotificationSvc { get; }
    IRegistrationRepository RegistrationRepository { get; }
    ICandidateRepository CandidateRepository { get; }
    IFilerLinkRepository FilerLinkRepository { get; }
    IUserMaintenanceSvc UserMaintenanceSvc { get; }
    ILinkageSvc LinkageSvc { get; }
}
