using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.Notifications.Models;

namespace SOS.CalAccess.Services.Business.Notifications;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// NotificationService is responsible for sending notifications to users and supports communication channels like EMail and SMS. 
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The screen or service invoking this layer is developed.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to handle all the functionality such as send, view, search, resolve and delete that are related to notifications.
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UN01</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// | Service                        | Operation                       | Description                                 |
/// | ------------------------------ | ------------------------------- | ------------------------------------------- |
/// | IFilerUserRepository           | GetUserIdsByFilerId()           | Gets all users associated to the filer. |
/// | IUserNotificationPreferenceRepository | FindNotificationPreferencesByUserIdAndFilerId() | Gets notification preferences for the user. |
/// | INotificationTemplateRepository | GetById()       | Gets Notification Template  |
/// | INotificationRepository        | Create()         | Creates a notifcation record |
/// | INotificationRepository        | GetById()         | Retrieves a notifcation record |
/// | INotificationRepository        | Update()         | Updates a notifcation record |
/// | INotificationRepository        | CountActiveUserNotificationsByUserId()         | Gets count of active notification records |
/// | INotificationRepository        | FindAllActivePriorityNotificationsByUserId()         | Gets list of all active high priority notifcation records  |
/// | INotificationRepository        | FindAllUnreadNotificationsByUserId()         | Gets list of all unread notifcation records  |
/// | INotificationRepository        | FindAllNotificationsByUserId()         | Gets list of all  notifcation records  |
/// | INotificationRepository        | FindAllActiveNotificationsByTemplateIdAndFilerId() | Gets list of all  notifcation records of the specified type for the filer |
/// | INotificationRepository        | SearchNotifications()         | Gets list of all  notifcation records that match search criteria  |
/// | EmailSvc                       | SendTemplatedEmail()                     | Sends Email message to the User          |
/// | EmailSvc                       | SendPlainEmail()                     | Sends Email message to the User           |
/// | SmsSvc                         | SendSms()                       | Send Sms message to the User.             |
#endregion

public interface INotificationSvc
{
    /// <summary>
    /// SendFilerNotification sends a notification to each user associated to the specified filer
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], IFilerUserRepository [label="IFilerUser\Repository"];
    /// Actor => INotificationSvc [label="SendFilerNotification()"];
    /// INotificationSvc => IFilerUserRepository [label="GetUserIdsByFilerId()"];
    /// IFilerUserRepository >> INotificationSvc [label="\nreturn List of UserIds"];
    /// INotificationSvc => INotificationSvc [label="SendUserNotification()"];
    /// INotificationSvc >> Actor [label="\nreturn"];
    /// \endmsc
    [Post("/" + SendFilerNotificationPath)]
    Task SendFilerNotification([Body] SendFilerNotificationRequest request);
    public const string SendFilerNotificationPath = "api/Notification/SendFilerNotification";

    /// <summary>
    /// SendUserNotification sends a notification to a single user using their notification preferences to determine if it is sent by email, sms, or both
    /// All notifications are also stored in the database so that they may appear in the in app notification inbox.
    /// Priority Notifications will also display on the users landing page until they are resolved.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], IUserNotificationPreferenceRepository [label="IUserNotification\nPreferenceRepository"], INotificationTemplateRepository [label="INotification\nTemplateRepository"], INotificationRepository [label="INotification\nRepository"], IEmailSvc [label="IEmail\nSvc"], ISmsMessageSvc [label="ISmsMessage\nSvc"];
    /// Actor => INotificationSvc [label="SendUserNotification()"];
    /// INotificationSvc => IUserNotificationPreferenceRepository [label="FindNotificationPreferencesByUserIdAndFilerId()"];
    /// IUserNotificationPreferenceRepository >> INotificationSvc [label="\nreturn UserNotificationPreference"];
    /// INotificationSvc => IUserNotificationPreferenceRepository [label="FindDefaultNotificationPreferencesByUserId()"];
    /// IUserNotificationPreferenceRepository >> INotificationSvc [label="\nreturn UserNotificationPreference"];
    /// INotificationSvc => INotificationTemplateRepository [label="GetById()"];
    /// INotificationTemplateRepository >> INotificationSvc [label="\nreturn NotificationTemplate"];
    /// INotificationSvc => INotificationRepository [label="Create()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn"];
    /// INotificationSvc note ISmsMessageSvc [label="ALT - If email delivery indicated and NotificationTemplate.EmailTemplateId != null", textbgcolor="#ffb000"];
    /// INotificationSvc => IEmailSvc [label="SendTemplatedEmail()"];
    /// IEmailSvc >> INotificationSvc [label="\nreturn"];
    /// INotificationSvc note ISmsMessageSvc [label="END ALT", textbgcolor="#ffb000"];
    /// INotificationSvc note ISmsMessageSvc [label="ALT - If email delivery indicated and NotificationTemplate.EmailTemplateId = null", textbgcolor="#ffb000"];
    /// INotificationSvc => IEmailSvc [label="SendPlainEmail()"];
    /// IEmailSvc >> INotificationSvc [label="\nreturn"];
    /// INotificationSvc note ISmsMessageSvc [label="END ALT", textbgcolor="#ffb000"];
    /// INotificationSvc note ISmsMessageSvc [label="ALT - If sms delivery indicated", textbgcolor="#ffb000"];
    /// INotificationSvc => ISmsMessageSvc [label="SendSmsMessage()"];
    /// ISmsMessageSvc >> INotificationSvc [label="\nreturn"];
    /// INotificationSvc note ISmsMessageSvc [label="END ALT", textbgcolor="#ffb000"];
    /// INotificationSvc >> Actor [label="\nreturn"];
    /// \endmsc
    [Post("/" + SendUserNotificationPath)]
    Task SendUserNotification([Body] SendUserNotificationRequest request);
    public const string SendUserNotificationPath = "api/Notification/SendUserNotification";

    /// <summary>
    /// CountActiveUserNotifications is called from the UI screen to show the number of active notifications of the user on the screen.
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="CountActiveUserNotifications()"];
    /// INotificationSvc => INotificationRepository [label="CountActiveUserNotificationsByUserId()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn int countOfActiveNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn int countOfActiveNotifications"];
    /// \endmsc
    [Get("/" + CountActiveUserNotificationsPath)]
    Task<int> CountActiveUserNotifications(long userId);
    public const string CountActiveUserNotificationsPath = "api/Notification/User/{userId}/ActiveCount";

    /// <summary>
    /// ListPriorityNotifications is called from the UI screen to show all the priority notifications for the user.
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="ListPriorityNotifications()"];
    /// INotificationSvc => INotificationRepository [label="FindAllActivePriorityNotificationsByUserId()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn ListOfNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn ListOfNotifications"];
    /// \endmsc
    [Get("/" + ListPriorityNotificationsPath)]
    Task<List<NotificationMessage>> ListPriorityNotifications(long userId);
    public const string ListPriorityNotificationsPath = "api/Notification/User/{userId}/Priority";

    /// <summary>
    /// ListUnreadNotifications is called from the UI screen to show all the Unread notifications for the user.
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="ListUnreadNotifications()"];
    /// INotificationSvc => INotificationRepository [label="FindAllUnreadNotificationsByUserId()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn ListOfNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn ListOfNotifications"];
    /// \endmsc
    [Get("/" + ListUnreadNotificationsPath)]
    Task<List<NotificationMessage>> ListUnreadNotifications(long userId);
    public const string ListUnreadNotificationsPath = "api/Notification/User/{userId}/Unread";

    /// <summary>
    /// ListAllNotifications is called from the UI screen to show all the notifications for the user.
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="ListAllNotifications()"];
    /// INotificationSvc => INotificationRepository [label="FindAllNotificationsByUserId"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn ListOfNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn ListOfNotifications"];
    /// \endmsc
    ///
    [Post("/" + ListAllNotificationsPath)]
    Task<PagedDataResponse<NotificationMessageDto>> ListAllNotifications([Body] PagedUserDataRequest request, long filerTypeId);
    public const string ListAllNotificationsPath = "api/Notification/User/All";

    /// <summary>
    /// SearchNotifications is called from the UI screen to show all the notifications for the user that matches the search criteria.
    /// </summary>
    /// <param name="searchCriteria"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="SearchNotifications()"];
    /// INotificationSvc => INotificationRepository [label="SearchNotifications()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn ListOfNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn ListOfNotifications"];
    /// \endmsc
    [Post("/" + SearchNotificationsPath)]
    Task<List<NotificationMessage>> SearchNotifications([Body] NotificationSearchCriteria searchCriteria);
    public const string SearchNotificationsPath = "api/Notification/User/Search";

    /// <summary>
    /// ViewNotification is called from the UI screen to show the notification that has been selected.
    /// </summary>
    /// <param name="notificationId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="ViewNotification()"];
    /// INotificationSvc => INotificationRepository [label="GetById()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn Notification"];
    /// INotificationSvc >> Actor [label="\nreturn Notification"];
    /// \endmsc
    [Get("/" + ViewNotificationPath)]
    Task<NotificationMessageDto?> ViewNotification(long notificationId);
    public const string ViewNotificationPath = "api/Notification/Detail/{notificationId}";

    /// <summary>
    /// ResolveNotification is called from a Decisions workflow or business service to indicate that the required action has been performed to resolve any active notifications of the specified type
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="ResolveNotification()"];
    /// INotificationSvc => INotificationRepository [label="FindAllActiveNotificationsByTemplateIdAndFilerId()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn "];
    /// INotificationSvc => INotificationRepository [label="Update()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn "];
    /// INotificationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + ResolveNotificationPath)]
    Task ResolveNotification([Body] ResolveNotificationRequest request);
    public const string ResolveNotificationPath = "api/Notification/Resolve";

    /// <summary>
    /// DeleteNotification is called from the UI screen to soft delete the notification that has been selected.
    /// </summary>
    /// <param name="notificationId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="DeleteNotification()"];
    /// INotificationSvc => INotificationRepository [label="Delete()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn "];
    /// INotificationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Delete("/" + DeleteNotificationPath)]
    Task<int> DeleteNotification(long notificationId);
    public const string DeleteNotificationPath = "api/Notification/Detail/{notificationId}";

    /// <summary>
    /// CountUnresolvedUserNotifications is called from the UI screen to show the number of unresolved notifications of the user on the screen.
    /// </summary>
    /// <param name="userName"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="CountUnresolvedeUserNotifications()"];
    /// INotificationSvc => INotificationRepository [label="CountUnresolvedeUserNotifications()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn int countOfUnresolvedNotifications"];
    /// INotificationSvc >> Actor [label="\nreturn int countOfUnresolvedNotifications"];
    /// \endmsc
    [Get(CountUnresolvedUserNotificationsPath)]
    Task<int> CountUnresolvedeUserNotifications(string userName);
    public const string CountUnresolvedUserNotificationsPath = "/api/Notification/User/{userName}/UnresolvedCount";

    /// <summary>
    /// MarkReadOrUnread is called from the UI screen to toggle the viewed state for a notification.
    /// </summary>
    /// <param name="notificationId"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, INotificationSvc [label="INotification\nSvc"], INotificationRepository;
    /// Actor => INotificationSvc [label="MarkReadOrUnread()"];
    /// INotificationSvc => INotificationRepository [label="UpdateProperty()"];
    /// INotificationRepository >> INotificationSvc [label="\nreturn "];
    /// INotificationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + MarkReadOrUnreadPath)]
    public Task MarkReadOrUnread(long notificationId);
    public const string MarkReadOrUnreadPath = "api/Notification/MarkReadOrUnread/{notificationId}";

}
