openapi: 3.0.3
info:
  title: Candidate Intention Statement (Form 501)
  description: |
    API for submitting Form 501, the Candidate Intention Statement.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Statement/CandidateIntention/{filerId}:
    post:
      summary: Submit the Candidate Intention Statement
      description: Submit the Candidate Intention Statement (Form 501).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CandidateIntentionStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    CandidateIntentionStatement:
      type: object
      properties:

        amendmentWithExplanation:
          $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
        candidateInformation:
          $ref: '#/components/schemas/CandidateInformation'
        stateCandidateExpenditureLimit:
          $ref: '#/components/schemas/ExpenditureLimitStatement'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - candidateInformation
        - attestation

    CandidateInformation:
      type: object
      properties:
        filerDetails:
          $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
        electionRaceId:
          type: integer
          format: int64
          description: election race Id.
        isNonPartisanOffice:
          type: boolean
        partyAffiliation:
          $ref: './common-schemas.yaml#/components/schemas/PartyAffiliation'
      required:
        - filerDetails
        - electionRaceId
      anyOf:
        - required: [isNonPartisanOffice]
        - required: [partyAffiliation]
        
    ExpenditureLimitStatement:
      type: object
      properties:
        expenditureLimitAccepted:
          type: boolean
          description: Indicates if the candidate accepts the voluntary expenditure limit.
        expenditureLimitAmendment:
          type: boolean
          description: Indicates if the candidate did not exceed the expenditure limit for the primary or special election.
        expenditureLimitAmendmentElectionDate:
          type: string
          format: date
          description: Date of primary or special election.
        expenditureExceeded:
          type: boolean
          description: Indicates if the candidate exceeded the expenditure limit for a specific election.
        contributedPersonalExcessFundsOn:
          type: string
          format: date
          description: Date expenditure limit exceeded.
      required:
        - expenditureLimitAccepted
