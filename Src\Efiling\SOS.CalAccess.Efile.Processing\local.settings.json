{"AccuMail": {"Host": "http://accumail01.sos.ca.gov", "Path": "/accumailgold_addressvalidation.asmx/ValidateAddress?"}, "Authorization": {"Enabled": true}, "EmailMessaging": {"FromEmailAddress": "<EMAIL>"}, "IsEncrypted": false, "SmsMessaging": {"FromPhoneNumber": "+18333705276"}, "Values": {"FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "QueueName": "efileapi-request-large", "ServiceBusConnection": "Endpoint=sb://sb-cal-dev.servicebus.usgovcloudapi.net/;SharedAccessKeyName=AppDevCredentials;SharedAccessKey=Jm7uR/LIwMgY3q4u2X0EC3KuMGLFhty7Y+ASbP8zCwU="}, "AzureValues": {"AzureWebJobsServiceBus": "Endpoint=sb://sb-cal-dev.servicebus.usgovcloudapi.net/;SharedAccessKeyName=AppDevCredentials;SharedAccessKey=Jm7uR/LIwMgY3q4u2X0EC3KuMGLFhty7Y+ASbP8zCwU=", "BlobStorageContainerName": "api-requests", "BlobStorageEndpoint": "https://azdevblobstorage.blob.core.usgovcloudapi.net", "BlobStorageConnectionString": "DefaultEndpointsProtocol=http;BlobEndpoint=https://azdevblobstorage.blob.core.usgovcloudapi.net;AccountName=azdevblobstorage;AccountKey=****************************************************************************************;EndpointSuffix=blob.core.usgovcloudapi.net;", "ServiceBusConnection": "Endpoint=sb://sb-cal-dev.servicebus.usgovcloudapi.net/;SharedAccessKeyName=AppDevCredentials;SharedAccessKey=Jm7uR/LIwMgY3q4u2X0EC3KuMGLFhty7Y+ASbP8zCwU=", "ServiceBusNamespace": "sb://sb-cal-dev.servicebus.usgovcloudapi.net", "IsProcessingRetryEnabledOnSystemError": false, "QueueName": "efileapi-request-large"}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=CARS;Trusted_Connection=True;TrustServerCertificate=true"}, "Decisions": {"Host": "http://devcaldcs01:8080/Primary/restapi/Flow/", "SessionId": "NS-01JFJZH87PSBE9D2H8GZK4VH9X"}}