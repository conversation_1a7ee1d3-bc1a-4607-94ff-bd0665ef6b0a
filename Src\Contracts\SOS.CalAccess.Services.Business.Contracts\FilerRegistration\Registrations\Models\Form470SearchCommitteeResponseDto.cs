

using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

public class Form470SearchCommitteeResponseDto
{
    public string CommitteeName { get; set; }

    public long Id { get; set; }

    public long FilerId { get; set; }

    public List<AddressDto> Address { get; set; }

    public User? Treasurer { get; set; }

    public Form470SearchCommitteeResponseDto() { }

    public Form470SearchCommitteeResponseDto(Committee committee)
    {
        CommitteeName = committee.Name;
        Id = committee.Id;
        FilerId = committee.FilerId ?? 0;
        Treasurer = committee.Filer?.Users?.FirstOrDefault()?.User != null
            ? new User
            {
                FirstName = committee.Filer.Users.FirstOrDefault()?.User?.FirstName ?? "",
                LastName = committee.Filer.Users.FirstOrDefault()?.User?.LastName ?? "",
                EmailAddress = committee.Filer.Users.FirstOrDefault()?.User?.EmailAddress ?? "",
                EntraOid = committee.Filer.Users.FirstOrDefault()?.User?.EntraOid ?? "",
            }
            : null;

        // Map Addresses from AddressList
        Address = committee.AddressList?.Addresses?
            .Select(a => new AddressDto
            {
                Street = a.Street,
                Street2 = a.Street2,
                City = a.City,
                State = a.State,
                Zip = a.Zip,
                Country = a.Country,
                Type = a.Type,
                Purpose = a.Purpose,
            }).ToList() ?? new List<AddressDto>();
    }
}
