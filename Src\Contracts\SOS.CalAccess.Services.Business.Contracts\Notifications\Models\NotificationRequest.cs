
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Services.Business.Notifications.Models;

public class NotificationRequest
{
    public List<NotificationTrigger>? Notifications { get; set; }

    public long? UserId { get; set; }

    public long? FilerId { get; set; }

    public Dictionary<string, string>? UserNotificationData { get; set; }

    public Dictionary<string, string>? FilerNotificationData { get; set; }
}

