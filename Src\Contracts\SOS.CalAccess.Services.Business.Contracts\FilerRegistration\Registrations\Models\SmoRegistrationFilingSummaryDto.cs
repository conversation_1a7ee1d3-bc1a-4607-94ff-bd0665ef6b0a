namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// DTO for summarizing SMO registration filing details.
/// </summary>
public class SmoRegistrationFilingSummaryDto
{
    /// <summary>
    /// Gets or sets the registration details.
    /// </summary>
    public required SmoRegistrationResponseDto Registration { get; set; }

    /// <summary>
    /// Gets or sets the filer name.
    /// </summary>
    public required string FilerName { get; set; }

    /// <summary>
    /// Gets or sets the filing status.
    /// </summary>
    public string? FilingStatus { get; set; }

    /// <summary>
    /// Gets or sets the list of SMO officers.
    /// </summary>
    public required IEnumerable<SmoOfficerGridDto> Officers { get; set; }

    /// <summary>
    /// Gets or sets the list of individual authorizers.
    /// </summary>
    public required IEnumerable<SmoOfficerGridDto> IndividualAuthorizers { get; set; }

    /// <summary>
    /// Gets or sets the list of pending items.
    /// </summary>
    public List<PendingItemDto> PendingItems { get; set; } = new List<PendingItemDto>();

    /// <summary>
    /// Gets or sets the attestation information.
    /// </summary>
    public SmoRegistrationAttestationResponseDto? Attestation { get; set; }

    /// <summary>
    /// Gets or sets the IsTreasurerAcknowledgement.
    /// </summary>
    public bool? IsTreasurerAcknowledgement { get; set; }

}
