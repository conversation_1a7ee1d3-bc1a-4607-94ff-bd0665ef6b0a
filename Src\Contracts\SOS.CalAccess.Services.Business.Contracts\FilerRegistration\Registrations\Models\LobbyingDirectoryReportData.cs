using System.ComponentModel.DataAnnotations;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
/// <summary>
/// This is the data that needs to be passed to Fluent 
/// </summary>
public class LobbyingDirectoryReportData
{
    /// <summary>
    /// Lobbyist Employers
    /// </summary>
    [Required]
    public required IList<LobbyistEmployerResponseDto> LobbyistEmployers { get; set; }


    /// <summary>
    /// Lobbyists  
    /// </summary>
    [Required]
    public required IList<Lobbyist> Lobbyists { get; set; }
    //Note for Developer - need a Lobbyist with their image URL and their firm or employer

    ///
    ///Employers of Lobbyists by category - what is the category 

}
