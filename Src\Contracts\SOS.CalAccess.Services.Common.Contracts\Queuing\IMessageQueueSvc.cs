namespace SOS.CalAccess.Services.Common.Queuing;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines methods for interacting with Azure Service Bus to support Asynchronous messaging.
/// </p>
/// <p>
/// The implmentation will wrap the SDK provided by the Azure.Messaging.ServiceBus package to provide consistent handling of authentication and authorization, configuration, retries, error handling, and other common concerns when interacting with Azure Service Bus.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend common application layer service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Provides capability to support Asynchronous messaging.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS14 - Queuing </li>
///</ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | Azure Service Bus              | SendMessageAsync               | Send a message to the queue  |
#endregion
public interface IMessageQueueSvc
{
    /// <summary>
    /// Create a ServiceBusSender instance, convert the message object to json, and send the message to the specified queue
    /// </summary>
    /// <param name="queueName"></param>
    /// <param name="messageContent"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IMessageQueueSvc, AzureServiceBus;
    /// Actor => IMessageQueueSvc [label="SendJsonMessage()"];
    /// IMessageQueueSvc => AzureServiceBus [label="SendMessageAsync"];
    /// AzureServiceBus >> IMessageQueueSvc [label="return"];
    /// IMessageQueueSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    public Task SendJsonMessage(QueueName queueName, object messageContent);
}
