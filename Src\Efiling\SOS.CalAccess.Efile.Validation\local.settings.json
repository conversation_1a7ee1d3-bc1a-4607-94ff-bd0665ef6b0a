{"AccuMail": {"Host": "http://accumail01.sos.ca.gov", "Path": "/accumailgold_addressvalidation.asmx/ValidateAddress?"}, "Authorization": {"Enabled": true}, "EmailMessaging": {"FromEmailAddress": "<EMAIL>"}, "IsEncrypted": false, "FileSize": {"Small": 100000, "Medium": 150000, "Large": 150001}, "Jwt": {"Audience": "https://graph.microsoft.com", "Issuer": "https://sts.windows.net/0252a9dd-e773-4879-87a9-7fb1b1215fc1/", "Ttl": 5, "Secret": "8qpLlWR~8u06_pDH2UGtxcIP4lScE3_z7~"}, "SmsMessaging": {"FromPhoneNumber": "+18333705276"}, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated"}, "AzureValues": {"ApiControllerEndpoint": "https://localhost:7280/api/ApiRequest", "AzureWebJobsServiceBus": "Endpoint=sb://sb-cal-dev.servicebus.usgovcloudapi.net/;SharedAccessKeyName=AppDevCredentials;SharedAccessKey=Jm7uR/LIwMgY3q4u2X0EC3KuMGLFhty7Y+ASbP8zCwU=", "BlobStorageConnectionString": "DefaultEndpointsProtocol=http;BlobEndpoint=https://azdevblobstorage.blob.core.usgovcloudapi.net;AccountName=azdevblobstorage;AccountKey=****************************************************************************************;EndpointSuffix=blob.core.usgovcloudapi.net;", "BlobStorageContainerName": "api-requests", "BlobStorageEndpoint": "https://azdevblobstorage.blob.core.usgovcloudapi.net", "ServiceBusNamespace": "sb://sb-cal-dev.servicebus.usgovcloudapi.net", "AppInsights_ConnectionString": "AppInsightsKey"}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=CARS;Trusted_Connection=True;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.ApplicationInsights": "Information"}, "ApplicationInsights": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}}}}