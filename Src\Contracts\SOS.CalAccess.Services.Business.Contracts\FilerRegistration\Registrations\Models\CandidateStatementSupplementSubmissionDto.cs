using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.Efile.Model;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Represents a single object for complete Candidate Intention Statement
/// (form 470) for submission through the Efile API.
/// </summary>
public class CandidateStatementSupplementSubmissionDto
{
    /// <summary>
    /// Candidate statement short form submission
    /// </summary>
    public Filing? CandidateStatementSupplement { get; set; }

    /// <summary>
    /// Filing period for the candidate statement short form
    /// </summary>
    public CandidateCampaignStatementSupplementAttestation? Attestation { get; set; }

    /// <summary>
    /// Amendment details for the candidate statement short form    
    /// </summary>
    public CandidateCampaignStatementSupplementAmendment? Amendment { get; set; }
    /// <summary>
    /// Submission Status
    /// </summary>
    public bool IsSubmission { get; set; }

    /// <summary>
    /// Api user id from config
    /// </summary>
    public long UserId { get; set; }
}
