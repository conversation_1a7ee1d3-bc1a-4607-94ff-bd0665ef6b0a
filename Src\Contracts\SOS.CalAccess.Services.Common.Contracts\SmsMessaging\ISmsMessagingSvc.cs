using Refit;
using SOS.CalAccess.Models.SmsMessaging;
using SOS.CalAccess.Services.Common.SmsMessaging.Model;

namespace SOS.CalAccess.Services.Common.SmsMessaging;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines methods for sending SMS messages using the Twilio API and tracking the status of sent messages.
/// </p>
/// <p>
/// Status updates will be received from a Twilio Event Webhook.  To use event webhooks the CARS solution system exposes a URL that accepts a POST request from Twilio. 
/// Twilio then sends event data as SendGrid processes each change to a message by calling the URL. This means status updates are generated in near real-time.
/// The URL Twilio uses for the webhook events is configured on the Twilio account.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service implementation handles all necessary configuration and authorization required to send a request to the Twilio API
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Provides to capability to communicate with users using SMS text messaging
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS5- Notifification- Email(SMS)  </li>
///</ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | ISmsMessageRepository          | FindById                       | Find phone number by id             |
/// | Twilio API                     | POST                           | sends SMS message |
#endregion
public interface ISmsMessagingSvc
{
    /// <summary>
    /// Send an SMS text message to the provided list of recipients using the Twilio API
    /// </summary>
    /// <param name="recipients"></param>
    /// <param name="filerId"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, ISmsMessagingSvc, ISmsMessageRepository, Twilio;
    /// Actor => ISmsMessagingSvc [label="SendSmsMessage()"];
    /// ISmsMessagingSvc => ISmsMessageRepository [label="Create()"];
    /// ISmsMessageRepository >> ISmsMessagingSvc [label="\nreturn "];
    /// ISmsMessagingSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post(SendSmsMessagePath)]
    public Task SendSmsMessage(SmsMessageRequest request);
    const string SendSmsMessagePath = "/api/SmsMessaging/sendsms";
    /// <summary>
    /// Update the status of the sms message based on data recieved from Twilio Event Webhook
    /// </summary>
    /// <param name="providerId"></param>
    /// <param name="status"></param>
    /// <param name="errorCode"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, ISmsMessagingSvc, ISmsMessageRepository;
    /// Actor => ISmsMessagingSvc [label="UpdateSmsStatus()"];
    /// ISmsMessagingSvc => ISmsMessageRepository [label="FindByProviderId()"];
    /// ISmsMessageRepository >> ISmsMessagingSvc [label="\nreturn "];
    /// ISmsMessagingSvc => ISmsMessageRepository [label="Update()"];
    /// ISmsMessageRepository >> ISmsMessagingSvc [label="\nreturn "];
    /// ISmsMessagingSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post(UpdateSmsStatusPath)]
    public Task UpdateSmsStatus(string providerId, string status, string errorCode, string errorMessage);
    const string UpdateSmsStatusPath = "/api/SmsMessaging/UpdateSmsStatus";
    /// <summary>
    /// Update status of the sms message after meesage is sent to Twilio
    /// </summary>
    /// <param name="updateSmsRequest"></param>
    /// <returns></returns>
    [Post(UpdateSmsMessageStatusPath)]
    Task UpdateSmsMessageStatus(UpdateSmsRequest updateSmsRequest);
    const string UpdateSmsMessageStatusPath = "/api/SmsMessaging/UpdateSmsMessageStatus";
}
