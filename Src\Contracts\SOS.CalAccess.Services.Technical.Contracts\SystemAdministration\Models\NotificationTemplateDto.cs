using System.ComponentModel.DataAnnotations;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Services.Technical.SystemAdministration.Models;
public class NotificationTemplateDto : IValidatableObject
{
    public long Id { get; set; }

    [Display(Name = "Notification Name")]
    [Required(ErrorMessage = "The Notification Name is required.")]
    [StringLength(128)]
    public string Name { get; set; }

    [Display(Name = "Description")]
    [Required(ErrorMessage = "The Description is required.")]
    [StringLength(512)]
    public string Description { get; set; }

    [Display(Name = "Priority Notification")]
    public bool IsPriorityMessage { get; set; }

    [Display(Name = "Action Required")]
    public bool IsActionRequired { get; set; }

    [Display(Name = "Notification Type")]
    public long NotificationTypeId { get; set; }
    public string? NotificationTypeName { get; set; }

    [Display(Name = "Filer Type")]
    public long? FilerTypeId { get; set; }
    public string? FilerTypeName { get; set; }

    public List<NotificationTemplateTranslationDto>? Translations { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (FilerTypeId == null && NotificationTypeId != NotificationType.SystemNotifications.Id)
        {
            yield return new ValidationResult(
                "The Filer Type is required for this Notification Type.",
                new[] { nameof(FilerTypeId) });
        }
    }
}

public class NotificationTemplateTranslationDto
{
    public long Id { set; get; }

    public long NotificationTemplateId { set; get; }

    [Required(ErrorMessage = "The Subject is required.")]
    [Display(Name = "Subject")]
    [StringLength(256)]
    public required string Subject { get; set; }

    [Required(ErrorMessage = "The Notification Text is required.")]
    [Display(Name = "Notification Text")]
    [StringLength(12000)]
    public required string Message { get; set; }

    [Required(ErrorMessage = "The SMS Text is required.")]
    [Display(Name = "SMS Text")]
    [StringLength(160)]
    public required string SmsMessage { get; set; }

    [Display(Name = "Email Template ID")]
    [StringLength(50)]
    public string? EmailTemplateId { get; set; }

}
