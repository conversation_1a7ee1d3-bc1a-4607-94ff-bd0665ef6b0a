using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

public class SmoRegistrationResponseDto
{
    public long Id { get; set; }
    public bool IsAmendment { get; set; }
    public long? FilerId { get; set; }
    public long? StatusId { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? County { get; set; }
    public string? ActivityLevel { get; set; }
    public bool? QualifiedCommittee { get; set; }
    public DateTime? DateQualified { get; set; }
    public long? CommitteeId { get; set; }
    public string? CommitteeName { get; set; }
    public string? CommitteeType { get; set; }
    public bool? CampaignCommittee { get; set; }
    public bool IsSameAsOrganizationAddress { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public string? CandidateAgency { get; set; }
    public List<AddressDto> Address { get; set; }
    public List<PhoneNumberDto> PhoneNumbers { get; set; }

    /// <summary>
    /// Gets or sets the flag that indicates the registration is terminating
    /// </summary>
    public bool IsTerminating { get; set; }

    /// <summary>
    /// Gets or sets the Date that indicates the registration is terminating
    /// </summary>
    public DateTime? TerminatedAt { get; set; }

    public SmoRegistrationResponseDto() // Parameterless constructor
    {
        Address = new List<AddressDto>();
        PhoneNumbers = new List<PhoneNumberDto>();
    }

    // Optional: Mapping constructor if you're doing manual mapping
    public SmoRegistrationResponseDto(SlateMailerOrganization response)
    {
        Id = response.Id;
        IsAmendment = response.Id != response.OriginalId;
        FilerId = response.FilerId;
        StatusId = response.StatusId;
        Name = response.Name;
        Email = response.Email;
        County = response.County;
        ActivityLevel = response.ActivityLevel;
        QualifiedCommittee = response.QualifiedCommittee;
        DateQualified = response.DateQualified;
        CommitteeId = response.CommitteeId;
        CommitteeName = response.Committee?.Name;
        CommitteeType = response.Committee?.CommitteeType!.Name;
        CampaignCommittee = response.CampaignCommittee;
        IsSameAsOrganizationAddress = response.IsSameAsCandidateAddress;
        SubmittedAt = response.SubmittedAt;
        CandidateAgency = response.CandidateAgency;
        IsTerminating = response.TerminatedAt != null;
        TerminatedAt = response.TerminatedAt;

        // Map Addresses from AddressList
        Address = response.AddressList?.Addresses?
            .Select(a => new AddressDto
            {
                Street = a.Street,
                Street2 = a.Street2,
                City = a.City,
                State = a.State,
                Zip = a.Zip,
                Country = a.Country,
                Type = a.Type,
                Purpose = a.Purpose,
            }).ToList() ?? new List<AddressDto>();

        // Map Phone Numbers from PhoneNumberList
        PhoneNumbers = response.PhoneNumberList?.PhoneNumbers.Select(x => new PhoneNumberDto(x)).ToList() ?? new List<PhoneNumberDto>();
    }
}
