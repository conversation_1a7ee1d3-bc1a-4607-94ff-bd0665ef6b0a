namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Slate Mailer Organization Detail Request.
/// </summary>
public sealed class SlateMailerOrganizationRequest
{
    /// <summary>
    /// Gets or sets Slate Mailer Organization Level.  i.e. State, County, City
    /// </summary>
    public string? ActivityLevel { get; set; }

    /// <summary>
    /// Is the organization qualified as a Slate Mailer Organization
    /// </summary>
    public bool? IsQualifiedCommittee { get; set; }

    /// <summary>
    /// Date qualified as Slate Mailer Organization
    /// </summary>
    public DateTime? DateQualified { get; set; }

    /// <summary>
    /// Is the SMO a Campaign Committee
    /// </summary>
    public bool? IsCampaignCommittee { get; set; }

    /// <summary>
    /// Campaign committee Id
    /// </summary>
    public long? CommitteeId { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

}

