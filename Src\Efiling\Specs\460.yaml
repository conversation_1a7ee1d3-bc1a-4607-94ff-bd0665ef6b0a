openapi: 3.0.3
info:
  title: Form 460 -  Recipient Committee Campaign Statement API
  description: API for submitting Form 460, Recipient Committee Campaign Statement
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/RecipientCommittee/{filerId}:
    post:
      summary: Submit a Recipient Committee Campaign Statement
      description: Submit Form 460 Recipient Committee Campaign Statement
      operationId: submitRecipientCommitteeCampaignStatement
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: integer

      requestBody:
        description: Submit Form 460 Recipient Committee Campaign Statement
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecipientCommitteeCampaignStatement'
        required: true

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    RecipientCommitteeCampaignStatement:
      type: object
      properties:
        reportingPeriod:
          allOf:
          - $ref: '#/components/schemas/ReportingPeriod'
          description: Cover Page - If this is the first report of the calendar year, the periodFrom date should be January 1. Otherwise, this date should be the day after the closing date of the most recently filed campaign statement. The periodTo date should be as identified on the campaign statement filing schedule.
        dateOfElection:
          type: string
          format: date
          description: Cover Page - When filing a preelection statement in connection with an election, provide the date of the election
        typeOfStatement:
          type: string
          # enum:
          #   - Preelection
          #   - Quarterly
          #   - Semi-annual
          #   - SpecialOddYearReport
          #   - Termination
          #   - Amendment
          description: Cover Page - The type of statement being filed. The filing schedule will identify the type of statement (e.g., preelection, semi-annual).
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
        treasurer:
           type: array
           items:
            $ref: '#/components/schemas/Treasurer'
        relatedCommittees:
          type: array
          items:
            $ref: '#/components/schemas/Committee'
          description: Cover Page Part 2 Section 5- Ballot measure committees do not complete this section.
        reportingPeriodSummary:
          allOf:
          - $ref: '#/components/schemas/RecipientCommitteeCampaignStatementSummary'
          description: Summary Page - Column A - Reflects the committee's activity during the current reporting period as reported on Schedules A through I. If there is no activity to report on a particular schedule, enter a zero. There should be no blank values
        yearToDateSummary:
          allOf:
          - $ref: '#/components/schemas/RecipientCommitteeCampaignStatementSummary'
          description: "Summary Page - Column B - Reflects the cumulative total since January 1 of the current calendar year. However, if the committee began raising funds in connection with the qualification of a measure that extends into two calendar years, the contributions and expenditures must be cumulated beginning January 1 of the year the committee began raising funds. Add the totals from Column B of the committee's last campaign statement (if any) to the corresponding amounts in Column A. If this is the first report being filed for a calendar year, only carry forward the amounts for loans and accrued expenses reported on Lines 2, 7, and 9 of Column B (if any) from the committee's last statement. (Note: The amounts reported on Lines 2, 7, and 9 of Column B should be the same as the total outstanding amounts disclosed in column (d) of Schedules B, F, and H, respectively, of the current report.) When loans (Schedules B and H) and accrued expenses (Schedule F) are paid, the figures to be carried from the schedules to Lines 2, 7, and 9 of Column A may be negative numbers. In this case, be sure to show them as negative figures on the Summary Page (e.g., with a minus sign (-) or in parentheses), and subtract them when totaling Columns A and B."
        cashStatement:
          allOf:
          - $ref: '#/components/schemas/CashStatement'
          description: Summary Page - Current Cash Statement - The Current Cash Statement section should accurately reflect the committee's cash position at the end of the reporting period. If deposits or expenditures have been made that have not cleared the account, the committee's bank balance may not match the ending cash balance. Beginning and ending cash balances should include the total amount of funds in the committee's campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc.
        unitemizedAmounts:
          allOf:
          - $ref: '#/components/schemas/UnitemizedAmounts'
          description: Unitemized amounts included in summary sections for schedule A, B, C, D, E, F, G, H, I
        totalLoanGuaranteesReceived:
          type: number
          format: double
          description: Summary Page/Schedule B Part 2 - The total of all loan guarantees, endorsements, or security received during the period.
        totalCashEquivalents:
          type: number
          format: double
          description: Summary Page - This figure includes investments that cannot be readily converted to cash as well as the balance due on all outstanding loans the committee has made to others. Do not include any amount that is invested in interest bearing accounts, certificates of deposit, money market accounts, or any other investments that can be readily converted to cash. This amount should be part of the ending cash figure reported on Line 16.
        totalOutstandingDebts:
          type: number
          format: double
          description: Summary Page - Report the total of all money owed by the committee. Using Column B, add Line 2 (loans received) and Line 9 (accrued expenses)
        candidateForPrimaryAndGeneralElectionSummary:
          allOf:
          - $ref: '#/components/schemas/PrimaryAndGeneralElectionSummary'
          description: Summary Page - Calendar year summary for candidates running in both the state primary and general elections. Ballot measure committees do not complete this section
        expenditureLimitSummaries:
          type: array
          items:
            $ref: '#/components/schemas/ExpenditureLimitSummary'
          description: Summary Page - Expenditure Limit summary for state candidates. Ballot measure committees do not complete this section
        contacts:
          type: array
          items:
            oneOf:
            - $ref: '#/components/schemas/CommitteeContact'
            - $ref: '#/components/schemas/IndividualContact'
            - $ref: '#/components/schemas/OrganizationContact'
          description: A list of all entities that have provided itemized contributions, received itemized expenditures, received or provided itemized loans, served as an intermediary for an itemized contribution, or were a source of itemized miscellaneous increases to cash.
        supportedCommittees:
          type: array
          items:
            $ref: '#/components/schemas/SupportedCommittee'
          description: Schedule D - List of other candidates, measures and committees that were the supported or opposed by contributions or independent expenditures made by the filing committee.
        scheduleASummaries:
          allOf:
          -  $ref: '#/components/schemas/ScheduleASummary'
          description: Summary sections for schedule A
        scheduleBSummaries:
          allOf:
          -  $ref: '#/components/schemas/ScheduleBSummary'
          description: Summary sections for schedule B
        scheduleCSummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleCSummary'
          description: Summary sections for schedule C
        scheduleDSummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleDSummary'
          description: Summary sections for schedule D
        scheduleESummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleESummary'
          description: Summary sections for schedule E
        scheduleFSummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleFSummary'
          description: Summary sections for schedule F
        scheduleHSummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleHSummary'
          description: Summary sections for schedule H
        scheduleISummaries:
          allOf:
          - $ref: '#/components/schemas/ScheduleISummary'
          description: Summary sections for schedule I
        attestation:
          type: array
          items:
            $ref: './common-schemas.yaml#/components/schemas/Attestation'
          description: Cover Page - The committee treasurer or the assistant treasurer listed on the committee's Statement of Organization, Form 410, must complete the verification. The statement is signed under penalty of perjury that the information is true and correct. If the Form 460 is not signed, it is not considered filed. If three or fewer officeholders/candidates or state ballot measure proponents control the committee, the officeholders/candidates must also complete the verification. If there are more than three officeholders/ candidates controlling the committee, one may complete the verification on behalf of the others. If the committee is sponsored and the sponsor is reporting contributions received through the sponsor or made by the sponsor, the responsible officer must also complete the verification.

    ReportingPeriod:
      type: object
      required:
        - periodFrom
        - periodTo
      properties:
        periodFrom:
          type: string
          format: date
          description: The start date of the reporting period.
        periodTo:
          type: string
          format: date
          description: The end date of the reporting period.

    ScheduleASummary:
      type: object
      properties:
        totalItemizedMonetaryContributions:
          type: number
          format: double
          description: Amount received this period – itemized monetary contributions(Include all Schedule A subtotals.).
        totalUnitemizedMonetaryContributionsLessThan100:
          type: number
          format: double
          description: Amount received this period – unitemized monetary contributions of less than $100.
        totalMonetaryContributions:
          type: number
          format: double
          description: Total monetary contributions received this period

    ScheduleBSummary:
      type: object
      properties:
        loansReceived:
          type: number
          format: double
          description: Amount received plus unitemized loans of less than $100.
        loansPaidOrForgiven:
          type: number
          format: double
          description: Amount paid or forgiven plus loans under 100 paid or forgiven and include loans paid by third party and itemized on Schedule A.
        scheduleBNetChange:
          type: number
          format: double
          description: Net change this period.

    ScheduleCSummary:
      type: object
      properties:
        itemizedNonMonetaryContributions:
          type: number
          format: double
          description: Amount received this period – itemized nonmonetary contributions(Include all Schedule A subtotals.).
        unitemizedMonetaryContributionsLessThan100:
          type: number
          format: double
          description: Amount received this period – unitemized monetary contributions of less than $100.
        totalNonMonetaryContributions:
          type: number
          format: double
          description: Total monetary contributions received this period.

    ScheduleDSummary:
      type: object
      properties:
        itemizedContributionsAndIndependentExpenditures:
          type: number
          format: double
          description: Itemized contributions and independent expenditures made this period.
        unitemizedContributionsAndIndependentExpenditures:
          type: number
          format: double
          description: UnItemized contributions and independent expenditures made this period under 100.
        TotalContributionsAndIndependentExpenditures:
          type: number
          format: double
          description: Total contributions and independent expenditures made this period.

    ScheduleESummary:
      type: object
      properties:
        itemizedPayment:
          type: number
          format: double
          description: Itemized payments made this period. (Include all Schedule E subtotals.)
        unitemizedPayment:
          type: number
          format: double
          description: Unitemized payments made this period of under $100
        totalInterestPaid:
          type: number
          format: double
          description: Total interest paid this period on loans.
        totalPaymentsPaid:
          type: number
          format: double
          description: Total payments made this period.

    ScheduleFSummary:
      type: object
      properties:
        totalAccruedExpensesIncurred:
          type: number
          format: double
          description: Total accrued expenses incurred this period
        totalAccruedExpensesPaid:
          type: number
          format: double
          description: Total accrued expenses paid this period
        scheduleFNetChange:
          type: number
          format: double
          description: Net change this period

    ScheduleHSummary:
      type: object
      properties:
        loansMade:
          type: number
          format: double
          description: Loans made this period plus unitemized loans of less than $100.
        paymentReceived:
          type: number
          format: double
          description: Payments received on loans plus unitemized payments of less than $100.
        scheduleHNetChange:
          type: number
          format: double
          description: Net change this period.

    ScheduleISummary:
      type: object
      properties:
        id:
          type: string
          format: uuid
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        description:
          type: string
        amount:
          type: number
          format: double
        itemizedIncreaseToCash:
          type: number
          format: double
          description: Itemized increases to cash this period.
        unitemizedIncreaseToCash:
          type: number
          format: double
          description: Unitemized increases to cash of under $100 this period.
        totalInterestReceived:
          type: number
          format: double
          description: Total of all interest received this period on loans made to others.
        totalMiscellaneousIncreaseToCash:
          type: number
          format: double
          description: Total miscellaneous increases to cash this period

    BasicCommitteeInfo:
      type: object
      properties:
        id:
          type: integer
          description: Unique id of the committee as assigned by Secretary of State
        name:
          type: string
          description: Name of committee as provided on the committee's most recently filed Statement of Organization, Form 410.
        committeeAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'

    Committee:
      allOf:
      - $ref: '#/components/schemas/BasicCommitteeInfo'
      - type: object
        properties:
          committeeType:
            type: string
            # enum:
            #   - OfficeholderCandidateCommittee
            #   - GeneralPurposeCommittee
            #   - BallotMeasureCommittee
            #   - PrimaryFormedCommittee
            description: Type of committee as provided on the committee's most recently filed Statement of Organization, Form 410.
          controlledCommittee:
            type: boolean
            description: Indicates if the committee is a controlled committee
          firstName:
            type: string
            description: Name of Candidate as provided on the committee's most recently filed Statement of Organization, Form 410.
          lastName:
            type: string
            description: Name of Candidate as provided on the committee's most recently filed Statement of Organization, Form 410.
          candidateAddress:
            $ref: './common-schemas.yaml#/components/schemas/Address'
          electionOfficeSought:
            $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
          electionDistrictNumber:
            type: string
            description: District number (if applicable).
          committeeAddress:
            $ref: './common-schemas.yaml#/components/schemas/Address'
          emailAddress:
            type: string
            format: email

    Treasurer:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: A unique id (in the context of the filer) for the treasurer.
        firstName:
          type: string
          description: First name of the treasurer
        lastName:
          type: string
          description: Last name of the treasurer
        mailingAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        phone:
          type: string
          description: Daytime phone number of the officer as digits only.
          pattern: '^\d+$'
          maxLength: 40
        fax:
          type: string
          description: Optional fax number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        emailAddress:
          type: string
          format: email
          description: Email address of treasurer

    # Not referenced
    # BallotMeasure:
    #   type: object
    #   properties:
    #     ballotMeasureName:
    #       type: string
    #       description: Name of ballot measure
    #     ballotMeasureNumber:
    #       type: string
    #       description: Number or Letter assigned to the ballot measure
    #     ballotMeasureJurisdiction:
    #       type: string
    #       enum:
    #         - City
    #         - County
    #         - State
    #       description: Jurisdiction of the ballot measure

    # Not referenced
    # BallotMeasureCommitteeInfo:
    #   allOf:
    #   - $ref: '#/components/schemas/BallotMeasure'
    #   - type: object
    #     properties:
    #       position:
    #         type: string
    #         enum:
    #           - Support
    #           - Oppose
    #         description: Whether the committee supports or opposes the measure.
    #       proponent:
    #         $ref: '#/components/schemas/BallotMeasureProponent'

    # Not referenced
    # BallotMeasureProponent:
    #   type: object
    #   properties:
    #     name:
    #       type: string
    #       description: Name of the controlling officeholder, candidate, or state measure proponent for the Ballot measure
    #     officeHeldOrSought:
    #       $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
    #     districtNumber:
    #       type: string
    #       description: District number (if applicable).

    # Not referenced
    # CandidateInfo:
    #   type: object
    #   properties:
    #     firstName:
    #       type: string
    #       description: Name of the officeholder or candidate
    #     lastName:
    #       type: string
    #       description: Name of the officeholder or candidate
    #     officeHeldOrSought:
    #       $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'

    # Not referenced
    # PrimarilyFormedCommitteeCandidateInfo:
    #   allOf:
    #   - $ref: '#/components/schemas/CandidateInfo'
    #   - type: object
    #     properties:
    #       position:
    #         type: string
    #         enum:
    #           - Support
    #           - Oppose
    #         description: Whether the committee supports or opposes the candidate.

    RecipientCommitteeCampaignStatementSummary:
      type: object
      properties:
        totalMonetaryContributions:
          type: number
          format: double
          description: Total Value of Monetary Contributions Received
        totalLoansReceived:
          type: number
          format: double
          description: Total Value of Loans Received
        totalCashContributions:
          type: number
          format: double
          description: Total Value of Cash Contributions Received (Sum of total monetary contributions and total loans received)
        totalNonMonetaryContributions:
          type: number
          format: double
          description: Total Value of Non-Monetary Contributions Received
        totalContributions:
          type: number
          format: double
          description: Total Value of All Contributions (Sum of Total cash contributions and total non monetary contributions)
        totalPaymentsMade:
          type: number
          format: double
          description: Total Value of Payments Made
        totalLoansMade:
          type: number
          format: double
          description: Total Value of Loans Made
        totalCashPayments:
          type: number
          format: double
          description: Total Value of Cash Payments (Sum of total payments made and total loans made)
        totalAccruedExpenses:
          type: number
          format: double
          description: Total Value of Accrued Expenses (Unpaid Bills)
        totalNonMonetaryAdjustments:
          type: number
          format: double
          description: Total Value of Non-monetary adjustments
        totalExpenditures:
          type: number
          format: double
          description: Total Value of expenditures made (Sum of total cash payments, total accrued expenses, and total non-monetary adjustments)

    CashStatement:
      type: object
      properties:
        beginningCashBalance:
          type: number
          format: double
          description: Beginning cash balance. Should include the total amount of funds in campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc.
        cashReceipts:
          type: number
          format: double
          description: Total cash contributions from reporting period summary
        miscellaneousIncreasesToCash:
          type: number
          format: double
          description: Total miscellaneous increases to cash during reporting period
        cashPayments:
          type: number
          format: double
          description: Total cash payments from reporting period summary
        endingCashBalance:
          type: number
          format: double
          description: Ending cash balance. Should include the total amount of funds in campaign checking and savings accounts, plus any investments that can be readily converted to cash, such as certificates of deposit, money market accounts, stocks and bonds, etc.

    UnitemizedAmounts:
      type: object
      properties:
        totalUnitemizedMonetaryContributions:
          type: number
          format: double
          description: Total value of all unitemized cash contributions (less than $100) received during reporting period
        totalUnitemizedNonMonetaryContributions:
          type: number
          format: double
          description: Total value of all unitemized non-monetary contributions (less than $100) received during reporting period
        totalUnitemizedMiscellaneousIncreases:
          type: number
          format: double
          description: Total value of all unitemized miscellaneous increases to cash (less than $100) received during reporting period
        totalUnitemizedLoansReceived:
          type: number
          format: double
          description: Total value of all unitemized loans (less than $100) received during reporting period
        totalUnitemizedLoansPaid:
          type: number
          format: double
          description: Total value of all unitemized loans paid during reporting period
        totalUnitemizedLoansMade:
          type: number
          format: double
          description: Total value of all unitemized loans (less than $100) made during reporting period
        totalUnitemizedLoanPaymentsReceived:
          type: number
          format: double
          description: Total value of all unitemized loan payments received during reporting period
        totalUnitemizedContributionsAndIndependentExpendituresSupportingCommittees:
          type: number
          format: double
          description: Total value of all unitemized contributions and independent expenditures (less than $100) made during reporting period
        totalUnitemizedExpenditures:
          type: number
          format: double
          description: Total value of all unitemized expenditures (less than $100) made during reporting period
        totalUnitemizedAccruedExpensesIncurred:
          type: number
          format: double
          description: Total value of all unitemized accrued expenses (less than $100) during reporting period
        totalUnitemizedAccruedExpensesPaid:
          type: number
          format: double
          description: Total value of all unitemized accrued expenses paid during reporting period

    ExpenditureLimitSummary:
      type: object
      properties:
        electionDate:
          type: string
          format: date
          description: Date of the election the candidate has accepted the voluntary expenditure ceiling for.
        totalExpenditures:
          type: number
          format: double
          description: Total amount of expenditures made through the end of the reporting period that are subject to the expenditure ceiling for the election. Report totals for the primary and general elections separately

    PrimaryAndGeneralElectionSummary:
      type: object
      description: Only for committees that are either Controlled by a candidate who is being voted on in both the state primary and general elections (does not apply to controlled ballot measure committees) or Primarily formed to support or oppose candidates being voted on in both the state primary and general elections. Complete this summary on the preelection and semi-annual statements for the general election, covering periods during the last six months of the year (July 1 - December 31).
      properties:
        totalContributionsFirstHalf:
          type: number
          format: double
          description: Total contributions received January 1st thru June 30th
        totalExpendituresFirstHalf:
          type: number
          format: double
          description: Total expenditures January 1st thru June 30th
        totalContributionsSecondHalfToDate:
          type: number
          format: double
          description: Total contributions received July 1st to Date
        totalExpendituresSecondHalfToDate:
          type: number
          format: double
          description: Total expenditures July 1st to Date

    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique Id (in the context of the filer) for the contact .
        contactType:
          type: string
          # enum:
          #   - Individual
          #   - RecipientCommittee
          #   - PoliticalParty
          #   - SmallContributorCommittee
          #   - CommercialLendingInstitution
          #   - Other
          description: Indicate whether the contact is an individual, a committee, or "other" (such as a business entity), or a political party. Use commercialLendingInstitution to identify a contact that is providing a loan in the normal course of business. Commercial lending institutions will appear as other on the form.
        addresses:
          type: array
          items:
            $ref: './common-schemas.yaml#/components/schemas/Address'
          description: List of addresses associated to the contact
        cumulativeContributionsYearToDate:
          type: number
          format: double
          description: Schedule A - Cumulative to Date Calendar Year - Generally will be sum of all contributions including nonmonetary contributions, loans, or loan guarantees from the same source from January 1 through December 31 unless the committee began raising funds in connection with the qualification of a measure that extends into two calendar years. In that case, the period over which contributions are cumulated begins January 1 of the year the committee began raising funds.
        cumulativeContributionsPerElection:
          type: array
          items:
            $ref: '#/components/schemas/ElectionTotal'
          description: Schedule A - Per Election to Date - State candidates must complete for each itemized contribution, disclosing the type of election, the year of the election, and the amount received from the contributor for that election. This column does not apply to ballot measure committees, unless otherwise instructed by a local ordinance.
        monetaryContributions:
          type: array
          items:
            $ref: '#/components/schemas/MonetaryContribution'
          description: Schedule A - List of monetary contributions of $100 or more made by the contact, except for loans (reported on Schedule B).
        nonmonetaryContributions:
          type: array
          items:
            $ref: '#/components/schemas/NonMonetaryContribution'
          description: Schedule C - List of nonmonetary contributions of $100 or more made by the contact. Nonmonetary contributions (also referred to as in-kind contributions) are goods or services provided to the committee for which it does not pay fair market value. The fair market value is the amount the committee would pay for the goods or services on the open market (whatever it would cost any member of the general public to obtain the same goods or services).
        loansIssued:
          type: array
          items:
            $ref: '#/components/schemas/Loan'
          description: Schedule B - Part 1 - list of loans of $100 or more the contact has issued or are outstanding.
        loansGuaranteed:
          type: array
          items:
            $ref: '#/components/schemas/LoanGuarantee'
          description: Schedule B - Part 2 - list of loans of $100 or more contact has guaranteed, co-signed, endorsed, or provided security for.
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
          description: Schedule E - list of payments to the contact of $100 or more or payments totaling $100 or more made during the period for a single product or service
        accruedExpenses:
          type: array
          items:
            $ref: '#/components/schemas/AccruedExpense'
          description: Schedule F
        subVendorPayments:
          type: array
          description: List of transaction ids that appear in schedule G that were paid by this contact
          items:
            type: string
            format: uuid
        miscellaneousIncreasesToCash:
          type: array
          items:
            $ref: '#/components/schemas/MiscellaneousIncrease'
          description: Schedule I

    CommitteeContact:
      allOf:
      - $ref: '#/components/schemas/Contact'
      - $ref: '#/components/schemas/BasicCommitteeInfo'
      - type: object
        properties:
          firstName:
            type: string
            description: For independent expenditure or major donor committees, the first name of the LLC's responsible officer as defined in Regulation 18402.2. For recipient committees, the first name of the principal officer as defined in Section 82047.6. For an LLC that has not qualified as a committee, the first name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified
          lastName:
            type: string
            description: For independent expenditure or major donor committees, the last name of the LLC's responsible officer as defined in Regulation 18402.2. For recipient committees, the last name of the principal officer as defined in Section 82047.6. For an LLC that has not qualified as a committee, the last name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified

    IndividualContact:
      allOf:
      - $ref: '#/components/schemas/Contact'
      - type: object
        properties:
          firstName:
            type: string
            description: The persons first name.
          lastName:
            type: string
            description: The persons last name.
          occupationEmployerInformationRequested:
            type: boolean
            description: If occupation and employer information has not yet been obtained for an individual contributor, set true and amend Schedule A when the information has been received.
          occupation:
            type: string
            description: If the contact is an individual contributor, provide the individual's occupation.
          employer:
            type: string
            description: If the contact is an individual contributor, provide the individual's employer. If the contributor is self-employed, provide the name of the business.

    OrganizationContact:
      allOf:
      - $ref: '#/components/schemas/Contact'
      - type: object
        properties:
          firstName:
            type: string
            description: For an LLC that has not qualified as a committee, the first name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified
          lastName:
            type: string
            description: For an LLC that has not qualified as a committee, the last name of the individual primarily responsible for approving the contribution. If more than one individual shares in the primary responsibility of approving a contribution, at least one such individual must be identified
          organizationName:
            type: string
            description: The name of the entity if the contact type is other or commercial lending institution.

    ElectionTotal:
      type: object
      properties:
        electionType:
          type: string
          # enum:
          #   - Primary
          #   - General
          #   - Special
          #   - Runoff
        electionYear:
          type: integer

    Transaction:
      type: object
      properties:
        transactionId:
          type: string
          format: uuid
          description: Unique id for the transaction in the context of the filer
        transactionDate:
          type: string
          format: date
          description: Date the transaction occurred
        amount:
          type: number
          format: double
          description: Amount of the transaction

    ContactTransaction:
      allOf:
      - $ref: '#/components/schemas/Transaction'
      - type: object
        properties:
          addressId:
            type: string
            format: uuid
            description: The id of the contact address that is associated with this transaction

    DescribedTransaction:
      allOf:
      - $ref: '#/components/schemas/Transaction'
      - type: object
        properties:
          description:
            type: string
            description: A description the transaction

    DescribedContactTransaction:
      allOf:
      - $ref: '#/components/schemas/ContactTransaction'
      - type: object
        properties:
          description:
            type: string
            description: A description the transaction

    MonetaryContribution:
      allOf:
      - $ref: '#/components/schemas/ContactTransaction'
      - type: object
        properties:
          intermediaries:
            type: array
            items:
              type: string
              format: uuid
              description: Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary.

    NonMonetaryContribution:
      allOf:
      - $ref: '#/components/schemas/DescribedContactTransaction'
      - type: object
        properties:
          intermediaries:
            type: array
            items:
              type: string
              format: uuid
              description: Unique Id of the contact that served as an intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary.

    Loan:
      type: object
      properties:
        transactionId:
          type: string
          format: uuid
          description: Unique id for the transaction in the context of the filer
        addressId:
          type: string
          format: uuid
          description: The id of the contact address that is associated with this transaction
        dateIncurred:
          type: string
          format: date
          description: The date the loan was incurred
        dateDue:
          type: string
          format: date
          description: The date the loan is due
        originalAmount:
          type: number
          format: double
          description: The initial amount of the loan
        interestRate:
          type: number
          format: double
          description: The annual interest rate of the loan
        beginningPeriodBalance:
          type: number
          format: double
          description: The outstanding balance of the loan at the beginning of the period. If the loan was received this period, this value should be blank
        amountInPeriod:
          type: number
          format: double
          description: The amount received from the lender during this reporting period. If the loan was received in a previous reporting period, leave blank.
        amountPaid:
          type: number
          format: double
          description: The amount of principal repaid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal.
        interestPaid:
          type: number
          format: double
          description: The amount of interest paid by the committee during this reporting period. Interest paid is reported separately from payments made on the loan principal. Interest payments are also transferred to the Schedule E Summary
        amountForgiven:
          type: number
          format: double
          description: Report any reduction of the loan during this reporting period that was not paid by the committee. When the lender forgives a loan or a third party makes a payment on a loan, also report the lender or third party on Schedule A
        endingPeriodBalance:
          type: number
          format: double
          description: The outstanding balance of the loan at the close of this reporting period
        intermediaries:
          type: array
          items:
            type: string
            format: uuid
            description: Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary.

    LoanGuarantee:
      type: object
      properties:
        transactionId:
          type: string
          format: uuid
          description: Unique id for the transaction in the context of the filer
        transactionDate:
          type: string
          format: date
          description: The date the loan was incurred
        addressId:
          type: string
          format: uuid
          description: The id of the contact address that is associated with this transaction
        loanId:
          type: string
          format: uuid
          description: Unique transactionId for the loan that was guaranteed
        amountGuaranteedThisPeriod:
          type: number
          format: double
          description: The amount guaranteed this period, if applicable. For lines of credit, enter the full amount established or secured by the guarantor during the period.
        balanceOutstanding:
          type: number
          format: double
          description: The outstanding balance for which the guarantor is liable at the close of this reporting period
        intermediaries:
          type: array
          items:
            type: string
            format: uuid
            description: Id of the contact that served as the intermediary for the contribution. Do not include this contribution in the list of contributions for the intermediary.

    MiscellaneousIncrease:
      allOf:
      - $ref: '#/components/schemas/DescribedContactTransaction'

    ExpenseTransaction:
      allOf:
      - $ref: '#/components/schemas/DescribedContactTransaction'
      - type: object
        properties:
          expenseType:
            $ref: '#/components/schemas/ExpenseType'

    Payment:
      allOf:
      - $ref: '#/components/schemas/ExpenseTransaction'
      - type: object
        properties:
          subvendorPayment:
            type: boolean
            description: True if this payment was made by an agent or independent contractor on behalf of the committee. The transaction id must be included in the subvendor payments property of the paying party
          paymentFor:
            type: string
            format: uuid
            description: Provide the transaction id of the accrued expense if the payment is associated to a previously accrued expense

    AccruedExpense:
      allOf:
      - $ref: '#/components/schemas/ExpenseTransaction'
      - type: object
        properties:
          beginningPeriodBalance:
            type: number
            format: double
            description: Amount still owed on an expense incurred during a prior period at the beginning of the current reporting period
          amountIncurred:
            type: number
            format: double
            description: Amount incurred during the current period that is outstanding at the end of the period
          estimatedAmount:
            type: boolean
            description: Flag to indicate that the amount is an estimate. If the exact amount of a debt or obligation, provide an estimate. Once the exact amount is known, amend the estimated amount or note the correct amount on the next campaign statement
          amountAdjusted:
            type: number
            format: double
            description: Difference between the estimated amount reported on previous campaign statement and actual amount incurred
          amountPaid:
            type: number
            format: double
            description: Amount paid this period. Also report on Schedule E.
          endingPeriodBalance:
            type: number
            format: double
            description: Amount still owed on an expense at the end of the current reporting period

    ExpenseType:
      type: string
      # enum:
      #   - CampaignParaphernalia
      #   - CampaignConsultants
      #   - Contribution
      #   - CivicDonation
      #   - FilingFees
      #   - FundraisingEvent
      #   - IndependentExpenditure
      #   - LegalDefense
      #   - LiteratureAndMailings
      #   - MemberCommunications
      #   - MeetingsAndAppearances
      #   - OfficeExpenses
      #   - PetitionCirculating
      #   - PhoneBank
      #   - PollingSurveyResearch
      #   - PostageDeliveryServices
      #   - ProfessionalServices
      #   - PrintAds
      #   - Radio
      #   - ReturnedContribution
      #   - CampaignSalary
      #   - Television
      #   - CandidateTravelLodgingMeals
      #   - StaffTravelLodgingMeals
      #   - TransferBetweenCommittees
      #   - VoterRegistration
      #   - Internet

    SupportedCommittee:
      type: object
      properties:
        committee:
          allOf:
          - $ref: '#/components/schemas/Committee'
          description: For each contribution or independent expenditure of $100 or more to support or oppose a candidate, ballot measure, or general purpose committee (e.g., political party, PAC), For candidates, disclose the Name of candidate, the office sought or held, and the district, if any. For Ballot Measures, disclose the Number or letter and jurisdiction of ballot measure. If a number or letter has not been assigned, include the measure's title. For General Purpose Committees disclose the Name of the committee.
        cumulativeContributionsYearToDate:
          type: number
          format: double
          description: Schedule D - Report the cumulative amount contributed to or expended to support or oppose each itemized committee since January 1 of the current calendar year.
        cumulativeContributionsPerElection:
          type: array
          items:
            $ref: '#/components/schemas/ElectionTotal'
          description: "Schedule D - This section is for reporting payments to state candidates. This section is generally not applicable for ballot measure committees. Note: A ballot measure committee controlled by a state candidate or officeholder may not make a contribution to a controlled committee of a candidate for elective office. The committee may contribute leftover funds when the committee is preparing to terminate to a segregated account of a political party committee so long as the funds are not used for candidate contributions or for communications that expressly advocate for or against a candidate. (See Regulation 18521.5.)"
        contributions:
          type: array
          items:
            $ref: '#/components/schemas/SupportedCommitteeContribution'
          description: List of contributions or independent expenditures made this period relative to the committee.

    SupportedCommitteeContribution:
      description: Report the date the contribution or independent expenditure was made. A monetary contribution is made on the date it is mailed, delivered, or otherwise transmitted to the committee or an agent of the committee. A nonmonetary contribution is made on the earlier of, the date funds were expended for the goods or services, the date the committee or agent of the committee obtained possession or control of the goods or services, or the date the committee otherwise received the benefit of the expenditure. An independent expenditure is made on the earlier of the date the payment is made or the date the committee making the payment receives consideration in exchange for the expenditure(s) (e.g., when the advertisement appears). For purposes of reporting independent expenditures on Schedule D, the date an independent expenditure is made is the date the communication is mailed, broadcast, or otherwise disseminated to the public. If payments are made in one reporting period for a communication that is disseminated to the public during a subsequent reporting period, report the payments on Schedule E for the period in which the payments they were made and complete Schedule D for the period in which the communication is disseminated. A payment for a communication that is never disseminated to the public is not an independent expenditure and need not be reported on Schedule D. However, the payment must still be reported on Schedule E. Because payments must be described when they are reported on Schedules E or F, a description is not required for payments reported on Schedules E or F that are nonmonetary contributions or independent expenditures. However, if no payment was made, describe the goods or services and disclose the fair market value of the contribution. For example, if goods on hand are contributed to a candidate or another committee (e.g., office supplies), a description must be included.
      allOf:
      - $ref: '#/components/schemas/DescribedTransaction'
      - type: object
        properties:
          supportedCommitteeContributionType:
            type: string
            # enum:
            #  - Monetary
            #  - Nonmonetary
            #  - IndependentExpenditure
            description: Indicate whether the payment was a monetary contribution, nonmonetary contribution, or independent expenditure.

  securitySchemes:
    calaccess_auth:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://tbd/oauth/authorize
          scopes:
            efile:write: submit campaign forms
            efile:read: request campaign forms
    api_key:
      type: apiKey
      name: api_key
      in: header
