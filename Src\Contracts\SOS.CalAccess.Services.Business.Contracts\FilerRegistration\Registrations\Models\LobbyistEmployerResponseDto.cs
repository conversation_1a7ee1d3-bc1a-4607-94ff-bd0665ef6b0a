using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Lobbyists;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Response DTO for Lobbyist Employer registrations.
/// </summary>
public class LobbyistEmployerResponseDto
{
    public LobbyistEmployerResponseDto() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="LobbyistEmployerResponseDto"/> class.
    /// </summary>
    /// <param name="lobbyistEmployer">The lobbyist employer registration entity.</param>
    public LobbyistEmployerResponseDto(LobbyistEmployer lobbyistEmployer)
    {
        Id = lobbyistEmployer.Id;
        Name = lobbyistEmployer.Name;
        Email = lobbyistEmployer.Email;
        StatusId = lobbyistEmployer.StatusId;
        FilerId = lobbyistEmployer.FilerId;
        EmployerName = lobbyistEmployer.EmployerName;
        EmployerType = lobbyistEmployer.EmployerType;
        Version = lobbyistEmployer.Version ?? 0;
        BusinessActivity = lobbyistEmployer.BusinessActivity;
        BusinessDescription = lobbyistEmployer.BusinessDescription;
        InterestType = lobbyistEmployer.InterestType;
        IndustryDescription = lobbyistEmployer.IndustryDescription;
        IndustryPortion = lobbyistEmployer.IndustryPortion;
        NumberOfMembers = lobbyistEmployer.NumberOfMembers;
        DateQualified = lobbyistEmployer.DateQualified;
        LegislativeSessionId = lobbyistEmployer.LegislativeSessionId;
        AddressListId = lobbyistEmployer.AddressListId.GetValueOrDefault();
        PhoneNumberListId = lobbyistEmployer.PhoneNumberListId.GetValueOrDefault();
        IsLobbyingCoalition = lobbyistEmployer.IsLobbyingCoalition;
        Agencies = lobbyistEmployer.RegistrationAgencies.Select(x => new RegistrationAgencyDto(x)).ToList();
        StateLegislatureLobbying = lobbyistEmployer.StateLegislatureLobbying;

        if (lobbyistEmployer.AddressList?.Addresses != null)
        {
            Addresses = lobbyistEmployer.AddressList.Addresses.Select(a => new AddressDtoModel(a)).ToList();
        }

        if (lobbyistEmployer.PhoneNumberList?.PhoneNumbers != null)
        {
            PhoneNumbers = lobbyistEmployer.PhoneNumberList.PhoneNumbers.Select(p => new PhoneNumberDto(p)).ToList();
        }

        if (lobbyistEmployer.MemberNames != null)
        {
            MemberNames = lobbyistEmployer.MemberNames.Select(m => new LobbyingEmployerGroupMemberDto(m)).ToList();
        }
    }

    /// <summary>
    /// Gets the unique identifier.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets the registration name.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets the email address.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets the status identifier.
    /// </summary>
    public long StatusId { get; set; }

    /// <summary>
    /// Gets the filer identifier.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets the version number.
    /// </summary>
    public int? Version { get; set; }

    /// <summary>
    /// Gets the employer name.
    /// </summary>
    public string? EmployerName { get; set; }

    /// <summary>
    /// Gets the employer type.
    /// </summary>
    public string? EmployerType { get; set; }

    /// <summary>
    /// Gets the business activity.
    /// </summary>
    public string? BusinessActivity { get; set; }

    /// <summary>
    /// Gets the business description.
    /// </summary>
    public string? BusinessDescription { get; set; }

    /// <summary>
    /// Gets the interest type.
    /// </summary>
    public string? InterestType { get; set; }

    /// <summary>
    /// Gets the industry group classification.
    /// </summary>
    public string? IndustryGroupClassification { get; set; }

    /// <summary>
    /// Gets the industry description.
    /// </summary>
    public string? IndustryDescription { get; set; }

    /// <summary>
    /// Gets the industry portion.
    /// </summary>
    public string? IndustryPortion { get; set; }

    /// <summary>
    /// Gets the number of members.
    /// </summary>
    public int? NumberOfMembers { get; set; }

    /// <summary>
    /// Gets a value indicating whether the lobbyist will lobby the state legislature.
    /// </summary>
    public bool? StateLegislatureLobbying { get; set; }

    /// <summary>
    /// Gets the date qualified.
    /// </summary>
    public DateTime? DateQualified { get; set; }

    /// <summary>
    /// Gets the legislative session ID.
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets a value indicating whether the registration is for a lobbying coalition
    /// </summary>
    public bool? IsLobbyingCoalition { get; set; }

    /// <summary>
    /// Gets the address list ID.
    /// </summary>
    public long AddressListId { get; set; }

    /// <summary>
    /// Gets the phone number list ID.
    /// </summary>
    public long PhoneNumberListId { get; set; }

    /// <summary>
    /// Gets the addresses associated with this registration.
    /// </summary>
    public List<AddressDtoModel> Addresses { get; set; } = new();

    /// <summary>
    /// Gets the phone numbers associated with this registration.
    /// </summary>
    public List<PhoneNumberDto> PhoneNumbers { get; set; } = new();

    /// <summary>
    /// Gets the members of this lobbying employer group.
    /// </summary>
    public List<LobbyingEmployerGroupMemberDto> MemberNames { get; set; } = new();

    /// <summary>
    /// Gets or sets the exact list of agencies
    /// </summary>
    public List<RegistrationAgencyDto>? Agencies { get; set; }
}

public class AddressDtoModel
{
    public AddressDtoModel() { }

    public AddressDtoModel(Address address)
    {
        Street = address.Street;
        Street2 = address.Street2;
        City = address.City;
        State = address.State;
        Country = address.Country;
        Zip = address.Zip;
        Type = address.Type;
        Purpose = address.Purpose;
    }

    public string? Street { get; set; }
    public string? Street2 { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? Zip { get; set; }
    public string? Type { get; set; }
    public string? Purpose { get; set; }
}

public class LobbyingEmployerGroupMemberDto
{
    public LobbyingEmployerGroupMemberDto(LobbyingEmployerGroupMember member)
    {
        Name = member.Name;
    }

    public string Name { get; set; }
}
