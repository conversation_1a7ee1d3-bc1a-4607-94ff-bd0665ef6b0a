openapi: 3.0.3
info:
  title: Paid Spokesperson Report (Form 511)
  description: |
    API for submitting Form 511, the Paid Spokesperson Report, as required for committees that make specific expenditures for paid spokespersons.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/PaidSpokespersonReport/{filerId}:
    post:
      summary: Submit the Paid Spokesperson Report
      description: Submit the Paid Spokesperson Report (Form 511).
      operationId: submitPaidSpokespersonReport
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaidSpokespersonReport'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    PaidSpokespersonReport:
      type: object
      properties:
        filerID:
          type: string
          description: Filer ID
        amendment:
         $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
        paymentsMade:
          type: array
          items:
            $ref: '#/components/schemas/PaymentMade'
          description: List of ballot measures supported or opposed.
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - filerID
        - paymentsMade
        - attestation

    PaymentMade:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date payment
        firstName:
          type: string
          description: Name and address of Spokesperson
        lastName:
          type: string
          description: Last Name and address of Spokesperson
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        ballotMeasureName:
          type: string
          description: Name of the ballot measure.
        ballotMeasureNumber:
          type: string
          description: Ballot number or letter.
        jurisdiction:
          type: string
          description: Jurisdiction of the ballot measure (e.g., state, city, county).
        position:
          type: string
          enum:
            - Support
            - Oppose
          description: Indicates whether the filer supports or opposes the measure.
        amount:
          type: number
          format: double
          description: Amount of payment.
      required:
        - firstName
        - lastName
        - ballotMeasureName
        - jurisdiction
        - ballotMeasureNumber
        - position

  securitySchemes:
    calaccess_auth:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://tbd/oauth/authorize
          scopes:
            efile:write: submit campaign forms
            efile:read: request campaign forms
    api_key:
      type: apiKey
      name: api_key
      in: header
