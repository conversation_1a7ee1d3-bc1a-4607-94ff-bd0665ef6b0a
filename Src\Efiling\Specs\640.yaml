openapi: 3.0.3
info:
  title: California Form 640 API
  description: API to manage and report other payments to influence legislative or administrative action, as described in California Form 640.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Disclosure/OtherPaymentsToInfluence/{filerId}:
    post:
      summary: Submit Payment Details
      description: Submit payment details for influencing legislative or administrative action. If 'continuationRequired' is true, include itemized continuation sheet details.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtherPaymentsToInfluence'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    OtherPaymentsToInfluence:
      type: object
      properties:
        periodFrom:
          type: string
          format: date
          description: Starting date of the report period covered
        periodTo:
          type: string
          format: date
          description: Ending date of the report period covered.
        totalOverheadExpenses:
          type: number
          description: Lump sum of payments for office overhead and operating expenses.
        totalCoalitionPayments:
          type: number
          description: Lump sum of payments to lobbying coalitions.
        totalSmallPayments:
          type: number
          description: Total payments under $250 or $2,500 for lobbying activities, excluding overhead.
        totalLargePayments:
          type: number
          description: Payments above $250 or $2,500 for lobbying activities, excluding overhead, itemized by payee.
        grandTotal:
          type: number
          description: Grand total of all other payments to influence legislative or administrative action.
        Contact:
          type: object
          properties:
            id:
              type: string
              format: uuid
              description: Unique Id for the individual, committee, business, political party, intermediary, vendor, or other entity that has provided contributions, received expenditures, received or provided loans, served as an intemediary, or is a source of itemized miscellaneous increases to cash.
            name:
              type: string
              description: Name of person or organization
            addresses:
              type: array
              items:
                $ref: './common-schemas.yaml#/components/schemas/Address'
              description: List of addresses associated to the contact
            cumulativeContributionsYearToDate:
              type: number
              format: double
              description: Cumulative Contributions Year To Date
            itemizedPayments:
              type: array
              items:
                $ref: '#/components/schemas/OtherPaymentToInfluenceTransaction'
              required:
                - code
                - payeeName
                - amount
      required:
        - filerName
        - periodFrom
        - periodTo
        - totalOverheadExpenses
        - totalCoalitionPayments
        - totalSmallPayments
        - totalLargePayments
        - grandTotal

    OtherPaymentToInfluenceTransaction:
      type: object
      properties:
        code:
          type: string
          # enum:
          #   - Salary (S)
          #   - Lobbyist expenses (E)
          #   - Legislative services (L)
          #   - Consultants & govt relations (C)
          #   - Public affairs (P)
          #   - Advertising (A)
          #   - Research (R)
          #   - Lobbying events (V)
          #   - Other (O)
          description: Payment code, e.g., [S], [E], etc.
        otherPaymentText:
          type: string
          description: Text for other payment code
        transactionId:
          type: string
          format: uuid
          description: Unique identifier for the payment
        addressId:
          type: string
          format: uuid
          description: Id of the address of the contact at the time the payment was made
        amount:
          type: number
          format: double
          description: Amount of the payments for this quarter
