openapi: 3.0.3
info:
  title: FPPC Form 605 API
  description: API Schema for Amendment to Registration - Lobbying Firm, Lobbyist Employer, Lobbying Coalition
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Registration/Amendment/{filerId}:
    post:
      summary: Submit Form 605 data
      description: Submit Form 605 data
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                registrationType:
                  type: object
                  properties:
                    lobbyingFirmRegistration:
                      type: boolean
                      description: LobbyingFirmRegistration
                    lobbyistEmployerRegistration:
                      type: boolean
                      description: LobbyistEmployerRegistration
                    lobbyingCoalitionRegistration:
                      type: boolean
                      description: LobbyingCoalitionRegistration
                legislativeSession:
                  type: string
                  description: The years for the legislative session for this registration.
                filerInfo:
                 $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
                changes:
                  type: object
                  properties:
                    addingLobbyist:
                      type: object
                      properties:
                        addLobbyist:
                         type: boolean
                         description: Attach a completed Form 604 and a recent photograph of the lobbyist is required
                        nameOfLobbyist:
                          type: string
                          description: Name of Lobbyist
                        effectiveDate:
                          type: string
                          description: Date
                        filerId:
                          type: string
                          description: Lobbiyst filerid
                        lobbyistCertification:
                          $ref: './604.yaml#/components/schemas/LobbyistCertificationStatement'
                    lobbyingFirmDeletingLobbyistEmployer:
                      type: object
                      properties:
                       FirmDeletingLobbyistEmployer:
                        type: boolean
                        description: Lobbying Firm Deleting Lobbyist Employer.
                       nameOfEmployer:
                        type: string
                        description: Name of employer
                       effectiveDate:
                        type: string
                        description: Date
                       LobbyingFirmActivityAuthorization:
                        $ref: './602.yaml#/components/schemas/LobbyingFirmActivityAuthorization'
                    lobbyingFirmAddingLobbyistEmployer:
                      type: object
                      properties:
                       FirmAddingLobbyistEmployer:
                        type: boolean
                        description: Lobbying Firm Adding Lobbyist Employer, includes subcontractor clients.
                       nameOfLobbyistEmployer:
                        type: string
                        description: Name of Lobbyist Employer
                       effectiveDate:
                        type: string
                        description: Date
                    registeredLobbyistEmployerDeletingLobbyingFirm:
                      type: object
                      properties:
                       LobbyistEmployerDeletingLobbyingFirm:
                        type: boolean
                        description: Registered Lobbyist Employer Deleting Lobbying Firm.
                       name:
                        type: string
                        description: Name of Firm
                       effectiveDate:
                        type: string
                        description: Date
                    registeredLobbyistEmployerAddingLobbyingFirm:
                      type: object
                      properties:
                       LobbyistEmployerAddingLobbyingFirm:
                        type: boolean
                        description: RegisteredLobbyist Employer Adding Lobbying Firm.
                       name:
                        type: string
                        description: Name of Lobbying Firm
                       effectiveDate:
                        type: string
                        description: Date
                    deletingLobbyist:
                     type: object
                     properties:
                      deletingLobbyist:
                       type: boolean
                       description: Delete the Lobbyist.
                      nameOfLobbyist:
                       type: string
                       description: Name of Lobbyist.
                      effectiveDate:
                       type: string
                       description: Date
                      terminationNoticeAttached:
                       type: boolean
                       description: Form 606 is attached as the lobbyist is ceasing activities as a lobbyist or not lobbyist is no longer employed.
                      ceasingActivities:
                        $ref: './606.yaml#/components/schemas/NoticeOfTermination'
                    other:
                     type: object
                     properties:
                      other:
                       type: boolean
                       description: Describe in detail and attachments are required
                      effectiveDate:
                       type: string
                       description: Date
                      otherText:
                       type: string
                       description: Textfield for other to inclde all details.
                directClients:
                  type: array
                  items:
                    $ref: '#/components/schemas/LobbyingDetails'
                  description: Direct client information (Section A)
                subcontractingDetails:
                  type: object
                  description: Subcontracting information (Section B)
                  properties:
                    subcontractingFirm:
                      $ref: '#/components/schemas/LobbyingDetails'
                    client:
                      $ref: '#/components/schemas/LobbyingDetails'
                    telephoneNumber:
                      type: string
                      pattern: '^\d+$'
                      maxLength: 40
                filingType:
                  $ref: './common-schemas.yaml#/components/schemas/FilingType'
                attestation:
                  $ref: './common-schemas.yaml#/components/schemas/Attestation'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingDetails:
      type: object
      description: Common fields for lobbying information
      required:
        - name
        - address
      properties:
        name:
          type: string
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        agenciesToBeLobbied:
          type: string
          description: Agencies to be lobbied
        descriptionOfInterests:
          type: string
          description: Description of lobbying interests
        periodOfContract:
          type: string
          format: date-range
