using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling lobbying firm registration operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the registration data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this service is to facilitate the retrieval and management of lobbying firm registrations.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IRegistrationRepository        | FindLobbyingFirmById           | Retrieves a lobbying firm registration by ID. |
#endregion
public interface ILobbyingFirmRegistrationSvc
{
    /// <summary>
    /// Retrieves the Lobbying Firm registration for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the Lobbying Firm registration.</param>
    /// <returns>Returns the Lobbying Firm registration details if found; otherwise, null.</returns>
    [Get("/" + GetLobbyingFirmPath)]
    Task<LobbyingFirmResponseDto?> GetLobbyingFirm(long id);
    const string GetLobbyingFirmPath = "api/Registration/LobbyingFirm/{id}";
}
