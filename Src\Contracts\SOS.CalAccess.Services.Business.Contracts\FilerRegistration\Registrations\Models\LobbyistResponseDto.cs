using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Data transfer object for lobbyist response.
/// </summary>
public class LobbyistResponseDto
{
    /// <summary>
    /// Initializes a new instance of the <see cref="LobbyistResponseDto"/> class.
    /// </summary>
    public LobbyistResponseDto() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="LobbyistResponseDto"/> class with a specified lobbyist.
    /// </summary>
    /// <param name="lobbyist">The lobbyist to initialize the DTO with.</param>
    public LobbyistResponseDto(Lobbyist lobbyist)
    {
        Id = lobbyist.Id;
        Name = lobbyist.Name;
        Email = lobbyist.Email ?? string.Empty;
        StatusId = lobbyist.StatusId;
        FilerId = lobbyist.FilerId;
        EmployerName = lobbyist.EmployerName;
        StateLegislatureLobbying = lobbyist.StateLegislatureLobbying;
        DateQualified = lobbyist.DateQualified;
        AddressListId = lobbyist.AddressListId ?? 0;
        PhoneNumberListId = lobbyist.PhoneNumberListId ?? 0;
        Version = lobbyist.Version ?? 0;

        if (lobbyist.AddressList?.Addresses != null)
        {
            Addresses = [.. lobbyist.AddressList.Addresses.Select(a => new AddressDtoModel(a))];
        }

        if (lobbyist.PhoneNumberList?.PhoneNumbers != null)
        {
            PhoneNumbers = [.. lobbyist.PhoneNumberList.PhoneNumbers.Select(p => new PhoneNumberDto(p))];
        }

        SelfRegister = lobbyist.SelfRegister;
        FirstName = lobbyist.FirstName;
        MiddleName = lobbyist.MiddleName;
        LastName = lobbyist.LastName;
        LobbyistEmployerOrLobbyingFirmId = lobbyist.Filer?.FilerLinks?.FirstOrDefault(x => x.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id || x.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id)?.LinkedEntityId ?? null;
        LegislativeSessionId = lobbyist.LegislativeSessionId;
        DateOfQualification = lobbyist.DateQualified;
        IsPlacementAgent = lobbyist.PlacementAgent;
        CompletedEthicsCourseWithinPastYear = lobbyist.EthicsCourseCompletedWithinPastYear;
        CompletedEthicsCourse = lobbyist.EthicsCourseCompleted;
        CompletedCourseDate = lobbyist.EthicsCourseCompletionDate;
        IsNewCertification = lobbyist.IsNewCertification;
        LobbyOnlySpecifiedAgencies = lobbyist.LobbyOnlySpecifiedAgencies;
        Agencies = [.. lobbyist.RegistrationAgencies.Select(x => new RegistrationAgencyDto(x))];
        IsLobbyingStateLegislature = lobbyist.StateLegislatureLobbying;
        IsSameAsCandidateAddress = lobbyist.IsSameAsCandidateAddress;
        WithdrawnAt = lobbyist.WithdrawnAt;
        TerminatedAt = lobbyist.TerminatedAt;
        //TD: Set photo based on common photo upload implementation by common team
    }

    /// <summary>
    /// Gets the unique identifier of the lobbyist.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets the name of the lobbyist.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets the email address of the lobbyist.
    /// </summary>
    public string? Email { get; set; } = string.Empty;

    /// <summary>
    /// Gets the status identifier of the lobbyist.
    /// </summary>
    public long StatusId { get; set; }

    /// <summary>
    /// Gets the filer identifier of the lobbyist.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets the version number of the lobbyist.
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// Gets the employer name of the lobbyist.
    /// </summary>
    public string? EmployerName { get; set; } = string.Empty;

    /// <summary>
    /// Gets a value indicating whether the lobbyist is lobbying the state legislature.
    /// </summary>
    public bool? StateLegislatureLobbying { get; set; }

    /// <summary>
    /// Gets the date the lobbyist qualified.
    /// </summary>
    public DateTime? DateQualified { get; set; }

    /// <summary>
    /// Gets the address list identifier of the lobbyist.
    /// </summary>
    public long AddressListId { get; set; }

    /// <summary>
    /// Gets the phone number list identifier of the lobbyist.
    /// </summary>
    public long PhoneNumberListId { get; set; }

    /// <summary>
    /// Gets the list of addresses associated with the lobbyist.
    /// </summary>
    public List<AddressDtoModel> Addresses { get; set; } = new();

    /// <summary>
    /// Gets the list of phone numbers associated with the lobbyist.
    /// </summary>
    public List<PhoneNumberDto> PhoneNumbers { get; set; } = new();

    /// <summary>
    /// Gets or sets the value determining whether the registration is for themselves or for a lobbyist
    /// </summary>
    public bool? SelfRegister { get; set; }

    /// <summary>
    /// Gets or sets First Name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets Middle Name
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets Last Name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets or sets the Id of the selected lobbyist employer or lobbying firm
    /// </summary>
    public long? LobbyistEmployerOrLobbyingFirmId { get; set; }

    /// <summary>
    /// Gets or sets the name of the selected lobbyist employer or lobbying firm
    /// </summary>
    public string? LobbyistEmployerOrLobbyingFirmName { get; set; }

    /// <summary>
    /// Gets or sets the legislative session id
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets or sets the date of qualification
    /// </summary>
    public DateTime? DateOfQualification { get; set; }

    /// <summary>
    /// Gets or sets the value whether the lobbyist is a placement agent
    /// </summary>
    public bool? IsPlacementAgent { get; set; }


    /// <summary>
    /// Gets or sets the value whether the ethics course was completed within the past year or not
    /// </summary>
    public bool? CompletedEthicsCourseWithinPastYear { get; set; }

    /// <summary>
    /// Gets or sets the value whether the ethics course was completed
    /// </summary>
    public bool? CompletedEthicsCourse { get; set; }

    /// <summary>
    /// Gets or sets the current status of the ethics certification
    /// </summary>
    public bool? IsNewCertification { get; set; }

    /// <summary>
    /// Gets or sets the date of the completed ethics course
    /// </summary>
    public DateTime? CompletedCourseDate { get; set; }

    /// <summary>
    /// Gets or sets the status of what agencies are to be lobbied
    /// </summary>
    public bool? LobbyOnlySpecifiedAgencies { get; set; }

    /// <summary>
    /// Gets or sets the exact list of agencies
    /// </summary>
    public List<RegistrationAgencyDto>? Agencies { get; set; }

    /// <summary>
    /// Gets or sets whether the state legislature is being lobbied
    /// </summary>
    public bool? IsLobbyingStateLegislature { get; set; }

    /// <summary>
    /// Gets or sets the value for the attached photo
    /// </summary>
    public string? Photo { get; set; }

    /// <summary>
    /// Gets or sets whether the diligence verification statement was acknowledged for submission
    /// </summary>
    public bool? DiligenceVerificationStatement { get; set; }

    /// <summary>
    /// Gets or sets the IsSameAsCandidateAddress
    /// </summary>
    public bool IsSameAsCandidateAddress { get; set; }

    /// <summary>
    /// Gets or sets the date the lobbyist withdrawn at.
    /// </summary>
    public DateTime? WithdrawnAt { get; set; }

    /// <summary>
    /// Gets or sets the date the lobbyist terminated at.
    /// </summary>
    public DateTime? TerminatedAt { get; set; }

    /// <summary>
    /// Gets or sets the effective date of changes
    /// </summary>
    public DateTime? EffectiveDateOfChanges { get; set; }

    public string? Type { get; set; } = string.Empty;
}
