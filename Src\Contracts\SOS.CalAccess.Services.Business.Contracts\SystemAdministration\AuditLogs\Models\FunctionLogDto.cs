

namespace SOS.CalAccess.Services.Business.SystemAdministration.AuditLogs.Models;
/// <summary>
/// Represents a log entry for a specific function execution, including process details and timing.
/// </summary>
public class FunctionLogDto
{
    /// <summary>
    /// Unique identifier of the function log entry.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Name or description of the process being logged.
    /// </summary>
    public string? Process { get; set; }

    /// <summary>
    /// The timestamp when the function execution started.
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// The timestamp when the function execution ended.
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// Summary or notes about the function execution, like number of invoices created
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// Status code representing the outcome of the function execution.
    /// </summary>
    public long Status { get; set; }
}
