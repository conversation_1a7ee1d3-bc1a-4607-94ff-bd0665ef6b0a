openapi: 3.0.3
info:
  title: Form 410 - Statement of Organization API
  description: API for submitting and managing Form 410 - Statement of Organization for recipient committees.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Registration/RecipientCommittee/{filerId}:
    post:
      summary: Submit Form 410 - Statement of Organization
      description: Submit a Form 410 report for initial registration, amendments, or termination of a recipient committee.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                statementType:
                  $ref: '#/components/schemas/StatementType'
                committeeInformation:
                  $ref: '#/components/schemas/CommitteeInformation'
                treasurerAndOfficers:
                  $ref: '#/components/schemas/TreasurerAndOfficers'
                typeOfCommittee:
                  $ref: '#/components/schemas/TypeOfCommittee'
                financialInstitutionDetails:
                  $ref: '#/components/schemas/FinancialInstitutionDetails'
                filingType:
                  $ref: './common-schemas.yaml#/components/schemas/FilingType'
                attestation:
                  type: array
                  items:
                    $ref: './common-schemas.yaml#/components/schemas/Attestation'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:

    StatementType:
      type: object
      properties:
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        notYetQualified:
          type: boolean
          description: Indicates if the committee is not yet qualified.
        dateQualified:
          type: string
          format: date
          description: Date when the committee met the qualification threshold (if applicable).
        terminatedAt:
          type: string
          format: date
          description: Date when the committee was terminated (if applicable).

    CommitteeInformation:
      type: object
      properties:
        id:
          type: string
          description: State-assigned Committee ID.
        name:
          type: string
          description: Full name of the committee.
        committeeType:
          type: string
          # enum:
          #   - Ballot Measure Committee Controlled by State Candidate
          #   - Candidate Election Committee
          #   - Controlled Committee
          #   - General Purpose Committee
          #   - Legal Defense Committee
          #   - New Committee
          #   - Primarily Formed Committee
          #   - Recall Committee
          #   - Sponsored Committee
          #   - Small Contributor Committee
          description: The committee type (Controlled Committee, Sponsored Committee, etc.)
        streetAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        mailingAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        county:
          type: string
          description: County where the committee is domiciled.
        jurisdictionActive:
          type: string
          description: Jurisdiction where the committee is active (e.g., state, city, or county).
        email:
          type: string
          format: email
          description: Email address of the committee.
        committeeFax:
          type: string
          description: Fax number of the committee as digits only (optional).
          pattern: '^\d+$'
          maxLength: 40
        phoneNumber:
          type: string
          description: Phone number of the committee as digits only.
          pattern: '^\d+$'
          maxLength: 40

    TreasurerAndOfficers:
      type: object
      properties:
        treasurer:
          $ref: '#/components/schemas/OfficerDetails'
        assistantTreasurer:
          $ref: '#/components/schemas/OfficerDetails'
        principalOfficers:
          type: array
          items:
            $ref: '#/components/schemas/OfficerDetails'

    TypeOfCommittee:
      type: object
      properties:

        controlledCommittee:
          type: array
          description: List of a committees controlled by a candidate or officeholder.
          items:
            type: object
            properties:
              name:
                type: string
                description: The name of the jointly controlled committee
              id:
                type: string
                format: uuid
                description: The identification number of the jointly controlled committee
              candidate:
                type: object
                properties:
                  firstName:
                    type: string
                    description: First name of the candidate.
                  lastName:
                    type: string
                    description: Last name of the candidate.
              electionOfficeSought:
                $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
              city:
                type: string
                description: City (if applicable).
              county:
                type: string
                description: County (if applicable).
              year:
                type: integer
                description: Year of the election.
              partyAffiliation:
                type: object
                properties:
                  partyPartisanship:
                    type: string
                    enum:
                      - Partisan
                      - Non-Partisan
                    description: Indicates if the candidate is running as partisan or non-partisan.
                  partyAffiliation:
                    $ref: './common-schemas.yaml#/components/schemas/PartyAffiliation'
              officeholderOrStateMeasureProponent:
                type: boolean
                description: Indicates if the committee is for an officeholder or state measure proponent.

        primarilyFormedCommittee:
          type: object
          description: List of primarily formed committees.
          properties:
            candidates:
              type: array
              items:
                $ref: './common-schemas.yaml#/components/schemas/Candidate'
            ballotMeasures:
              type: array
              items:
                $ref: './common-schemas.yaml#/components/schemas/Measure'

        generalPurposeCommittee:
          type: object
          properties:
            jurisdictionType:
              type: string
              enum:
                - City
                - County
                - State
            activityDescription:
              type: string
              description: Description of the general-purpose committee activity.

        sponsoredCommittee:
          type: array
          description: List of sponsored committees.
          items:
            type: object
            properties:
              firstName:
                type: string
                description: First name of the sponsor.
              lastName:
                type: string
                description: Last name of the sponsor.
              industryOrGroupDescription:
                type: string
                description: Industry or field of the sponsor.
              sponsorAddress:
                $ref: './common-schemas.yaml#/components/schemas/Address'
              phoneNumber:
                type: string
                description: Phone number of the committee as digits only.
                pattern: '^\d+$'
                maxLength: 40

        smallContributorCommittee:
          type: object
          properties:
            dateQualified:
              type: string
              format: date
              description: Date the small contributor committee qualified.

    FinancialInstitutionDetails:
      type: object
      properties:
        financialInstitutionName:
          type: string
          description: Name of the financial institution.
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        financialInstitutionPhone:
          type: string
          description: Phone number of the committee as digits only.
          pattern: '^\d+$'
          maxLength: 40
        accountNumber:
          type: string
          description: Account number associated with the committee (if applicable).
        bankRecordAuthorizedPeople:
          type: array
          items:
            type: object
            properties:
              firstName:
                type: string
                description: First name of the authorized person.
              lastName:
                type: string
                description: Last name of the authorized person.

    OfficerDetails:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the officer.
        lastName:
          type: string
          description: Last name of the officer.
        email:
          type: string
          format: email
          description: Email address of the officer.
        phoneNumber:
          type: string
          description: Contact phone number of the officer.
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
