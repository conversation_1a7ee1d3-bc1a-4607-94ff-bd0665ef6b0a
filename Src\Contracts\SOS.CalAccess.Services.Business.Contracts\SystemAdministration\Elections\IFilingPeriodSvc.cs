using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.Elections;
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the operations for managing filing periods, including creation, updating, deletion,
/// retrieval, and filtering by report type.
/// </p>
///
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that filing period data is validated at the service level before interacting with the repository.
/// </p>
///
/// <h4>Business Function</h4>
/// <p>
/// The core responsibility of this service is to facilitate CRUD and search operations related to filing periods,
/// converting between domain models and DTOs as needed.
/// </p>
///
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA-01: Configure System</li>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists the repository operations invoked by this service.
/// </p>
///
/// | Service                     | Operation              | Description                                               |
/// |----------------------------|------------------------|-----------------------------------------------------------|
/// | IFilingPeriodRepositorySvc | Create                 | Persists a new filing period entity.                      |
/// | IFilingPeriodRepositorySvc | Update                 | Updates an existing filing period entity.                 |
/// | IFilingPeriodRepositorySvc | Delete                 | Deletes a filing period based on ID.                      |
/// | IFilingPeriodRepositorySvc | SearchByReportType     | Finds filing periods by associated report type.           |
/// | IFilingPeriodRepositorySvc | GetAll                 | Retrieves all filing periods from the system.             |

public interface IFilingPeriodSvc
{
    /// <summary>
    /// Creates a filing period.
    /// </summary>
    /// <param name="filingPeriod">Filing period DTO</param>
    /// <returns>Filing period DTO</returns>
    /// \msc
    /// Actor, IFilingPeriodSvc [label="IFilingPeriodSvc"], IFilingPeriodRepositorySvc;
    /// Actor => IFilingPeriodSvc [label="CreateFilingPeriod()"];
    /// IFilingPeriodSvc => IFilingPeriodRepositorySvc [label="Create()"];
    /// IFilingPeriodRepositorySvc >> IFilingPeriodSvc [label="\nreturn FilingPeriod"];
    /// IFilingPeriodSvc >> Actor [label="\nreturn FilingPeriodDto"];
    /// \endmsc
    Task<FilingPeriodDto> CreateFilingPeriod(FilingPeriodDto filingPeriod);


    /// <summary>
    /// Updates a filing period.
    /// </summary>
    /// <param name="filingPeriod">Filing period DTO</param>
    /// <returns>Filing period DTO</returns>
    ///
    /// \msc
    /// Actor, IFilingPeriodSvc [label="IFilingPeriodSvc"], IFilingPeriodRepositorySvc;
    /// Actor => IFilingPeriodSvc [label="UpdateFilingPeriod()"];
    /// IFilingPeriodSvc => IFilingPeriodRepositorySvc [label="Update()"];
    /// IFilingPeriodRepositorySvc >> IFilingPeriodSvc [label="\nreturn FilingPeriod"];
    /// IFilingPeriodSvc >> Actor [label="\nreturn FilingPeriodDto"];
    /// \endmsc
    Task<FilingPeriodDto> UpdateFilingPeriod(FilingPeriodDto filingPeriod);


    /// <summary>
    /// Deletes a filing period by ID.
    /// </summary>
    /// <param name="filingPeriodId">ID of the filing period to delete</param>
    /// <returns>True if deletion was successful, otherwise false</returns>
    /// \msc
    /// Actor, IFilingPeriodSvc [label="IFilingPeriodSvc"], IFilingPeriodRepositorySvc;
    /// Actor => IFilingPeriodSvc [label="DeleteFilingPeriod()"];
    /// IFilingPeriodSvc => IFilingPeriodRepositorySvc [label="Delete()"];
    /// IFilingPeriodRepositorySvc >> IFilingPeriodSvc [label="\nreturn bool"];
    /// IFilingPeriodSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    Task<bool> DeleteFilingPeriod(long filingPeriodId);



    /// <summary>
    /// Searches filing periods based on the specified report type.
    /// </summary>
    /// <param name="reportType">Report type identifier</param>
    /// <returns>A collection of filing period DTOs</returns>
    /// \msc
    /// Actor, IFilingPeriodSvc [label="IFilingPeriodSvc"], IFilingPeriodRepositorySvc;
    /// Actor => IFilingPeriodSvc [label="SearchFilingPeriodByReportType()"];
    /// IFilingPeriodSvc => IFilingPeriodRepositorySvc [label="SearchByReportType()"];
    /// IFilingPeriodRepositorySvc >> IFilingPeriodSvc [label="\nreturn IEnumerable<FilingPeriod>"];
    /// IFilingPeriodSvc >> Actor [label="\nreturn IEnumerable<FilingPeriodDto>"];
    /// \endmsc
    Task<IEnumerable<FilingPeriodDto>> SearchFilingPeriodByReportType(long reportType);


    /// <summary>
    /// Retrieves all filing periods.
    /// </summary>
    /// <returns>A collection of filing period DTOs</returns>
    ///
    /// \msc
    /// Actor, IFilingPeriodSvc [label="IFilingPeriodSvc"], IFilingPeriodRepositorySvc;
    /// Actor => IFilingPeriodSvc [label="GetFilingPeriods()"];
    /// IFilingPeriodSvc => IFilingPeriodRepositorySvc [label="GetAll()"];
    /// IFilingPeriodRepositorySvc >> IFilingPeriodSvc [label="\nreturn IEnumerable<FilingPeriod>"];
    /// IFilingPeriodSvc >> Actor [label="\nreturn IEnumerable<FilingPeriodDto>"];
    /// \endmsc
    Task<IEnumerable<FilingPeriodDto>> GetFilingPeriods();


}
