using System.Collections.Immutable;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Mapping;

/// <summary>
/// Provides mapping utilities for <see cref="FilingStatus"/>.
/// </summary>
public static class FilingStatusMapping
{
    /// <summary>
    /// A mapping of filing status names to their corresponding <see cref="FilingStatus"/> instances.
    /// </summary>
    public static readonly ImmutableDictionary<string, FilingStatus> FilingStatusByName = new Dictionary<string, FilingStatus>
    {
        [FilingStatus.Draft.Name] = FilingStatus.Draft,
        [FilingStatus.Submitted.Name] = FilingStatus.Submitted,
        [FilingStatus.Accepted.Name] = FilingStatus.Accepted,
        [FilingStatus.Cancelled.Name] = FilingStatus.Cancelled,
        [FilingStatus.Incomplete.Name] = FilingStatus.Incomplete,
        [FilingStatus.Pending.Name] = FilingStatus.Pending
    }.ToImmutableDictionary();

    /// <summary>
    /// Retrieves the ID of a <see cref="FilingStatus"/> by its name.
    /// </summary>
    /// <param name="name">The name of the status.</param>
    /// <returns>The corresponding ID.</returns>
    /// <exception cref="ArgumentException">Thrown if the name is invalid.</exception>
    public static long GetIdByName(string name)
    {
        if (FilingStatusByName.TryGetValue(name, out var status))
        {
            return status.Id;
        }

        throw new ArgumentException($"Invalid filing status name: {name}", nameof(name));
    }
}
