openapi: 3.0.3
info:
  title: Report of Lobbyist Employer and Lobbying Coalition (Form 635)
  description: |
    API for submitting Form 635, the Report of Lobbyist Employer and Lobbying Coalition, used to disclose lobbying activities, payments, and campaign contributions.
  version: 0.1.0
externalDocs:
  description: Find out more about Swagger
  url: http://swagger.io
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Report/LobbyistEmployer/{filerId}:
    post:
      summary: Submit the Report of Lobbyist Employer and Lobbying Coalition
      description: Submit the Report of Lobbyist Employer and Lobbying Coalition (Form 635).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportOfLobbyistEmployerLobbyingCoalition'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

  /Lobbying/Report/LobbyingCoalition/{filerId}:
    post:
      summary: Submit the Report of Lobbyist Employer and Lobbying Coalition
      description: Submit the Report of Lobbyist Employer and Lobbying Coalition (Form 635).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportOfLobbyistEmployerLobbyingCoalition'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    ReportOfLobbyistEmployerLobbyingCoalition:
      type: object
      properties:
        periodFrom:
          type: string
          format: date
          description: The start date of the reporting period.
        periodTo:
          type: string
          format: date
          description: The end date of the reporting period.
        cumulativePeriodBeginning:
          type: string
          format: date
          description: The cumulative start date, typically January 1 of the biennial legislative session.

        # PART I - LEGISLATIVE OR STATE AGENCY ADMINISTRATIVE ACTIONS ACTIVELY LOBBIED DURING THE PERIOD
        legislativeActionsLobbied:
          type: array
          items:
            $ref: '#/components/schemas/ActionLobbied'
          description: List of legislative or state agency administrative actions actively lobbied during the period.

        # SUMMARY OF PAYMENTS THIS PERIOD
        summaryOfPayments:
          $ref: '#/components/schemas/SummaryOfPayments'

        # PART II - PARTNERS, OWNERS, AND EMPLOYEES WHOSE "LOBBYIST REPORTS" (FORM 615) ARE ATTACHED TO THISREPORT
        principals:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                description: "Name of the principal."
              title:
                type: string
                description: "Name of the principal."
          required:
            - name
            - title

        # PART III - PAYMENTS MADE IN CONNECTION WITH LOBBYING ACTIVITIES

        # A. PAYMENTS TO IN-HOUSE EMPLOYEE LOBBYISTS
        paymentsToInHouseEmployeeLobbyists:
          type: object
          properties:
            amountThisPeriod:
              type: number
              format: double
              description: Payment amount this period.
            cumulativeTotalToDate:
              type: number
              format: double
              description: Cumulative total payments to date.
          required:
            - amountThisPeriod
            - cumulativeTotalToDate

        # B. PAYMENTS TO LOBBYING FIRMS (Including Individual Contract Lobbyists)
        paymentsToLobbyingFirms:
          type: object
          properties:
            lobbyingFirmPayments:
              type: array
              items:
                $ref: '#/components/schemas/LobbyingFirmPayment'
            totalThisPeriod:
              type: number
              format: double
              description: Total payments this period.
          required:
            - lobbyingFirmPayments
            - totalThisPeriod

        # C. ACTIVITY EXPENSES
        activityExpenses:
          type: array
          items:
            $ref: '#/components/schemas/ActivityExpense'
          description: List of activity expenses made during the reporting period.

        # D. OTHER PAYMENTS TO INFLUENCE LEGISLATIVE OR ADMINISTRATIVE ACTION
        otherPayments:
          type: object
          properties:
            paymentsToLobbyingCoalitions:
              type: number
              format: double
              description: Payments to lobbying coalitions.
            otherPayments:
              type: number
              format: double
              description: Other payments.
            totalPayments:
              type: number
              format: double
              description: Total payments.
          required:
            - paymentsToLobbyingCoalitions
            - otherPayments
            - totalPayments

        # E. PAYMENTS IN CONNECTION WITH ADMINISTRATIVE TESTIMONY IN RATEMAKING PROCEEDINGSBEFORE THE CALIFORNIA PUBLIC UTILITIES COMMISSION
        rateMakingPayments:
          type: number
          format: double
          description: Payments in connection with administrative testimony in ratemaking proceedings before the California Public Utilities Commission.

        # PART IV - CAMPAIGN CONTRIBUTIONS MADE
        campaignContributionsMade:
          type: object
          properties:
            majorDonorCommittee:
              type: string
              description: Name of major donor or recipient committee which has filed a campaign disclosure statement.
            committeeId:
              type: string
              description: Identification number if recipient committee
            campaignContributions:
              type: array
              items:
                $ref: '#/components/schemas/CampaignContribution'
              description: List of campaign contributions made during the period.
          required:
            - majorDonorCommittee
            - committeeId
            - campaignContributions

        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - filerDetails
        - legislativeActionsLobbied
        - summaryOfPayments
        - verification

    ActionLobbied:
      type: object
      properties:
        actionType:
          type: string
        legislativeBillNumber:
          type: string
        stateAgency:
          type: string
        administrativeIdNumber:
          type: string
        description:
          type: string
      required:
        - actionType
        - legislativeBillNumber
        - stateAgency
        - administrativeIdNumber
        - description

    LobbyingFirmPayment:
      type: object
      properties:
        name:
          type: string
          description: Name of lobbying firm/independent contractor.
        address:
          allOf:
            - $ref: './common-schemas.yaml#/components/schemas/Address'
          description: Address of lobbying firm/independent contractor.
        feesAndRetainers:
          type: number
          format: double
          description: Fees and Retainers
        reimbursementsOfExpenses:
          type: number
          format: double
          description: Reimbursements of Expenses
        advancesOrOtherPayments:
          type: number
          format: double
          description: Advances or Other Payments
        totalThisPeriod:
          type: number
          format: double
          description: Total This Period
        cumulativeTotalToDate:
          type: number
          format: double
          description: Cumulative Total to Date
      required:
        - name
        - address
        - feesAndRetainers
        - reimbursementsOfExpenses
        - advancesOrOtherPayments
        - totalThisPeriod
        - cumulativeTotalToDate

    SummaryOfPayments:
      type: object
      properties:
        paymentsToInHouseLobbyists:
          type: number
          format: double
          description: Total payments to in-house employee lobbyists for the period.
        paymentsToLobbyingFirms:
          type: number
          format: double
          description: Total payments to lobbying firms for the period.
        activityExpenses:
          type: number
          format: double
          description: Total activity expenses for the period.
        otherPaymentsToInfluence:
          type: number
          format: double
          description: Total other payments to influence legislative or administrative actions.
        totalPayments:
          type: number
          format: double
          description: Grand total of all payments made during the period.
        totalPucActivityPayments:
          type: number
          format: double
          description: Total Payments in Connection with PUC Activities.
        noCampaignContributionsMade:
          type: boolean
      required:
        - paymentsToInHouseLobbyists
        - paymentsToLobbyingFirms
        - activityExpenses
        - otherPaymentsToInfluence
        - totalPayments
        - totalPucActivityPayments

    ActivityExpense:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of the contribution.
        name:
          type: string
          description: Name of the Payee.
        address:
          allOf:
            - $ref: './common-schemas.yaml#/components/schemas/Address'
          description: Address of Payee
        reportablePersons:
          type: array
          items:
            $ref: '#/components/schemas/ReportablePerson'
          description: List of Name and Official Position of Reportable Persons and Amount Benefiting Each.
        consideration:
          type: string
          description: Description of consideration.
        totalAmount:
          type: number
          format: double
          description: Total Amount of Activity
      required:
        - date
        - name
        - address
        - reportablePersons
        - consideration
        - totalAmount

    ReportablePerson:
      type: object
      properties:
        name:
          type: string
          description: Name of reportable person.
        officialPosition:
          type: string
          description: Official position of reportable person.
        amount:
          type: number
          format: double
          description: Amount benefitting reportable person.
      required:
        - name
        - officialPosition
        - amount

    CampaignContribution:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 'Unique Id for campaign contribution.'
        date:
          type: string
          format: date
          description: Date of the contribution.
        recipientName:
          type: string
          description: Name of the recipient of the contribution.
        recipientCommitteeID:
          type: string
          description: Committee ID of the recipient, if applicable.
        amount:
          type: number
          format: double
          description: Amount of the contribution.
      required:
        - id
        - date
        - recipientName
        - recipientCommitteeID
        - amount
