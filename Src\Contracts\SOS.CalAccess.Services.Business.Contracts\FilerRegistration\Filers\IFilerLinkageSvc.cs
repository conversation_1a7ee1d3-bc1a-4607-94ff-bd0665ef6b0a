namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers;

#region Design Notes
/// <summary>
/// Interface for the Filer Linkage Service.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Filer Linkage Service. The service is responsible for managing the linkage between users and filers, including inviting users, requesting access, and approving access.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating filer user data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the lifecycle of user access to filers, including inviting users, requesting access, and approving access.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-07: Request Linkage to a Registered User or Filing Entity</li>
/// <li>FR-08: Dispose Linkage Request</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IFilerUserRepository           | Create                         | Creates a new filer user     |
/// | IFilerUserRepository           | Update                         | Updates an existing filer user |
#endregion

/// <summary>
/// Interface for the Filer Linkage Service.
/// </summary>
public interface IFilerLinkageSvc
{
    /// <summary>
    /// Invites a user to access a filer.
    /// </summary>
    /// <param name="userId">The ID of the user to invite.</param>
    /// <param name="filerId">The ID of the filer.</param>
    ///
    /// \msc
    /// Actor, IFilerLinkageSvc [label="IFiler \n Linkage \n Svc"], IFilerUserRepository [label="IFilerUser \n Repository"];
    /// Actor => IFilerLinkageSvc [label="InviteUserAccessToFiler()"];
    /// IFilerLinkageSvc => IFilerUserRepository [label="Create()"];
    /// IFilerUserRepository >> IFilerLinkageSvc [label="return"];
    /// IFilerLinkageSvc >> Actor [label="return"];
    /// \endmsc
    public void InviteUserAccessToFiler(long userId, long filerId);

    /// <summary>
    /// Requests access to a filer for a user.
    /// </summary>
    /// <param name="userId">The ID of the user requesting access.</param>
    /// <param name="filerId">The ID of the filer.</param>
    ///
    /// \msc
    /// Actor, IFilerLinkageSvc [label="IFiler \n Linkage \n Svc"], IFilerUserRepository [label="IFilerUser \n Repository"];
    /// Actor => IFilerLinkageSvc [label="RequestUserAccessToFiler()"];
    /// IFilerLinkageSvc => IFilerUserRepository [label="Create()"];
    /// IFilerUserRepository >> IFilerLinkageSvc [label="return"];
    /// IFilerLinkageSvc >> Actor [label="return"];
    /// \endmsc
    public void RequestUserAccessToFiler(long userId, long filerId);

    /// <summary>
    /// Approves a user's access to a filer.
    /// </summary>
    /// <param name="accessRequestId">The ID of the access request to approve.</param>
    ///
    /// \msc
    /// Actor, IFilerLinkageSvc [label="IFiler \n Linkage \n Svc"], IFilerUserRepository [label="IFilerUser \n Repository"];
    /// Actor => IFilerLinkageSvc [label="ApproveUserAccess()"];
    /// IFilerLinkageSvc => IFilerUserRepository [label="Update()"];
    /// IFilerUserRepository >> IFilerLinkageSvc [label="return"];
    /// IFilerLinkageSvc >> Actor [label="return"];
    /// \endmsc
    public void ApproveUserAccess(long accessRequestId);

    /// <summary>
    /// Rejects a user's access to a filer.
    /// </summary>
    /// <param name="accessRequestId">The ID of the access request to reject.</param>
    ///
    /// \msc
    /// Actor, IFilerLinkageSvc [label="IFiler \n Linkage \n Svc"], IFilerUserRepository [label="IFilerUser \n Repository"];
    /// Actor => IFilerLinkageSvc [label="RejectUserAccess()"];
    /// IFilerLinkageSvc => IFilerUserRepository [label="Update()"];
    /// IFilerUserRepository >> IFilerLinkageSvc [label="return"];
    /// IFilerLinkageSvc >> Actor [label="return"];
    /// \endmsc
    public void RejectUserAccess(long accessRequestId);

    /// <summary>
    /// Terminates a user's access to a filer.
    /// </summary>
    /// <param name="userId">The ID of the user whose access is to be terminated.</param>
    /// <param name="filerId">The ID of the filer.</param>
    ///
    /// \msc
    /// Actor, IFilerLinkageSvc [label="IFiler \n Linkage \n Svc"], IFilerUserRepository [label="IFilerUser \n Repository"];
    /// Actor => IFilerLinkageSvc [label="TerminateUserAccess()"];
    /// IFilerLinkageSvc => IFilerUserRepository [label="Update()"];
    /// IFilerUserRepository >> IFilerLinkageSvc [label="return"];
    /// IFilerLinkageSvc >> Actor [label="return"];
    /// \endmsc
    public void TerminateUserAccess(long userId, long filerId);
}
