using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Refit;
using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

[Route("notifications")]
public class NotificationPreferenceController(
    IUserNotificationPreferenceSvc notificationPreferenceSvc,
    IToastService toastService,
    IStringLocalizer<SharedResources> localizer) : Controller
{
    private readonly IStringLocalizer<SharedResources> _localizer = localizer;
    private readonly IToastService _toastService = toastService;

    [HttpGet("")]
    public async Task<IActionResult> Edit()
    {
        long userId = await notificationPreferenceSvc.GetInitiatingUserId() ?? 0;
        var roles = await notificationPreferenceSvc.GetFilersAndRolesForUser(userId);
        var preferences = await notificationPreferenceSvc.ViewUserNotificationPreferences(userId);
        var model = new NotificationPreferencesViewModel(roles, preferences, userId);
        return View(model);
    }

    [HttpPost("")]
    public async Task<IActionResult> Save(NotificationPreferencesViewModel model)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.SelectMany(entry => entry.Value!.Errors).ToList();
            foreach (var error in errors)
            {
                this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = error.ErrorMessage });
            }
            return View(model);
        }

        try
        {
            await notificationPreferenceSvc.UpdateUserNotificationPreferences(model.NotificationPreferences);
            _toastService.Success(_localizer["FilerPortal.NotificationPrefs.Edit.SaveSuccess"], showCloseButton: false, timeOut: 3000);
            return RedirectToAction(nameof(Edit));
        }
        catch (ApiException ex)
        {
            this.AddGlobalAlertToViewModel(model, new GlobalAlert { Type = AlertType.Danger.ToString(), Message = ex.Message });
            return View(model);
        }
    }
}
