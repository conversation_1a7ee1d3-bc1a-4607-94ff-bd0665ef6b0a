using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// ControlledCommitteeResponseDto.
/// </summary>
public record ControlledCommitteeResponseDto
{
    /// <summary>
    /// Gets or sets Controlled Committee FilerId
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or sets Controlled Committee Name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets Controlled Committee Phone Number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets Controlled Committee Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets Controlled Committee Addresses
    /// </summary>
    public List<AddressDto>? Address { get; set; } = new List<AddressDto>();

    public ControlledCommitteeResponseDto() { }

    public ControlledCommitteeResponseDto(CandidateControlledCommittee input)
    {
        Id = input.FilerId;
        Name = input.Name;
        Email = input.Email;
        var phone = input.PhoneNumberList?.PhoneNumbers?.Find(p => p.Type == "Phone");
        PhoneNumber = $"{phone?.Number}";
        // Map Addresses from AddressList
        Address = input.AddressList?.Addresses?.Select(a => new AddressDto(a)).ToList();
    }
}
