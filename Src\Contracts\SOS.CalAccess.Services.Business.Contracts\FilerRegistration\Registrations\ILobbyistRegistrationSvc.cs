using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
/// <summary>
/// Interface for lobbyist registration service.
/// </summary>
public interface ILobbyistRegistrationSvc
{
    /// <summary>
    /// Creates a new lobbyist registration.
    /// </summary>
    /// <param name="request">The request object containing lobbyist registration details.</param>
    /// <returns>Returns a registration response containing status and details of the created statement.</returns>
    [Post(CreateLobbyistRegistrationPage03Path)]
    Task<RegistrationResponseDto> CreateLobbyistRegistrationPage03(LobbyistRegistrationRequestDto request);
    const string CreateLobbyistRegistrationPage03Path = "/api/Registration/CreateLobbyistRegistrationPage03";

    /// <summary>
    /// Gets the lobbyist registration by filer ID.
    /// </summary>
    /// <param name="id">The ID of the filer.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the lobbyist response DTO.</returns>
    [Get(GetLobbyistByFilerPath)]
    Task<LobbyistResponseDto?> GetLobbyistByFilerId(long id);
    const string GetLobbyistByFilerPath = "/api/Registration/Lobbyist/Filer/{id}";

    /// <summary>
    /// Retrieves the lobbyist registration for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the lobbyist registration.</param>
    /// <returns>Returns the lobbyist registration details if found; otherwise, null.</returns>
    [Get(GetLobbyistRegistrationPath)]
    Task<LobbyistResponseDto?> GetLobbyistRegistration(long id);
    const string GetLobbyistRegistrationPath = "/api/Registration/Lobbyist/{id}";

    /// <summary>
    /// Searches for lobbyist employer or lobbying firm by Id or Name.
    /// </summary>
    /// <param name="q">The search query string containing the lobbyist employer or lobbying firm's name.</param>
    /// <returns>Returns a list of lobbyist employers or lobbying firms matching the search criteria.</returns>
    [Get(SearchLobbyistEmployerOrLobbyingFirmByIdOrNamePath)]
    Task<IEnumerable<LobbyistEmployerOrLobbyistFirmSearchResultDto>> SearchLobbyistEmployerOrLobbyingFirmByIdOrName(string q);
    const string SearchLobbyistEmployerOrLobbyingFirmByIdOrNamePath = "/api/LobbyistEmployerOrLobbyingFirm/Search";

    /// <summary>
    /// Updates an existing lobbyist registration.
    /// </summary>
    /// <param name="id">The unique identifier of the lobbyist registration.</param>
    /// <param name="request">The request object containing updated details.</param>
    /// <returns>Returns a registration response containing the status of the update operation.</returns>
    [Put(UpdateLobbyistRegistrationPath)]
    Task<RegistrationResponseDto> UpdateLobbyistRegistration(long id, [Body] LobbyistRegistrationRequestDto request);
    const string UpdateLobbyistRegistrationPath = "/api/Registration/LobbyistRegistration/{id}";

    /// <summary>
    /// Cancel a draft lobbyist registration
    /// </summary>
    /// <param name="id">Unique identifier for lobbyist registration record</param>
    /// <returns></returns>
    [Post(CancelLobbyistRegistrationPath)]
    Task CancelLobbyistRegistration(long id);
    const string CancelLobbyistRegistrationPath = "/api/Registration/Lobbyist/Cancel/{id}";

    /// <summary>
    /// Submits the lobbyist registration for e-filing.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>

    [Post(SubmitLobbyistRegistrationForEfilePath)]
    Task<RegistrationResponseDto> SubmitLobbyistRegistrationForEfile([Body] LobbyistRegistrationSubmissionDto request);
    const string SubmitLobbyistRegistrationForEfilePath = "/api/Registration/SubmitLobbyistRegistration";

    /// <summary>
    /// Submits a lobbyist registration for approval.
    /// </summary>
    /// <param name="id">The unique identifier of the lobbyist registration.</param>
    /// <returns>Returns a registration response indicating whether the submission was successful.</returns>
    [Post(SubmitLobbyistRegistrationPath)]
    Task<RegistrationResponseDto> SubmitLobbyistRegistration(long id);
    const string SubmitLobbyistRegistrationPath = "/api/Registration/Lobbyist/{id}/Submit";

    /// <summary>
    /// Initialize a Lobbyist Registration Amendment record
    /// </summary>
    /// <param name="id">id</param>
    [Get(CreateLobbyistAmendmentRegistrationAsyncPath)]
    Task<LobbyistResponseDto> CreateLobbyistAmendmentRegistrationAsync(long id);
    const string CreateLobbyistAmendmentRegistrationAsyncPath = "/api/Registration/Lobbyist/Amend/{id}";

    /// <summary>
    /// Retrieves the  registration for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the lobbyist registration.</param>
    /// <returns>Returns the lobbyist registration details if found; otherwise, null.</returns>
    [Get(GetRegistrationByIdPath)]
    Task<LobbyistResponseDto?> GetRegistrationById(long id);
    const string GetRegistrationByIdPath = "/api/Registration/LobbyistRegistration/{id}";

    /// <summary>
    /// Withdraw lobbyist registration
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Put(WithdrawLobbyistRegistrationPath)]
    Task<RegistrationResponseDto> WithdrawLobbyistRegistration(long id, [Body] WithdrawLobbyistRegistrationRequestDto request);
    const string WithdrawLobbyistRegistrationPath = "/api/Registration/Lobbyist/Withdraw/{id}";

    /// <summary>
    /// Withdraw lobbyist registration
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Put(SendWithdrawLobbyistRegistrationPath)]
    Task<RegistrationResponseDto> SendLobbyistRegistrationWithdrawal(long id, [Body] WithdrawLobbyistRegistrationRequestDto request);
    const string SendWithdrawLobbyistRegistrationPath = "/api/Registration/Lobbyist/SendWithdraw/{id}";

    /// <summary>
    /// Send lobbyist registration termination
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Put(SendLobbyistRegistrationTerminationPath)]
    Task<RegistrationResponseDto> SendLobbyistRegistrationTermination(long id, [Body] LobbyistRegistrationTerminationRequestDto request);
    const string SendLobbyistRegistrationTerminationPath = "/api/Registration/Lobbyist/SendTermination/{id}";

    /// <summary>
    /// Withdraw lobbyist registration
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Put(SaveLobbyistRegistrationTerminationPath)]
    Task<RegistrationResponseDto> SaveLobbyistRegistrationTermination(long id, [Body] LobbyistRegistrationTerminationRequestDto request);
    const string SaveLobbyistRegistrationTerminationPath = "/api/Registration/Lobbyist/termination/{id}";
}
