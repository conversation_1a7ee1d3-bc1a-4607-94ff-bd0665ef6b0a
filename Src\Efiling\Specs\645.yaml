openapi: 3.0.3
info:
  title: Form 645 - Report of Persons Spending $5,000 or More
  description: |
    API for submitting Form 645, required for persons who spend $5,000 or more in a calendar quarter to influence legislative or administrative actions.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Report/5000DollarFiler/{filerId}:
    post:
      summary: Submit Form 645
      description: Submit the Report of Persons Spending $5,000 or More to Influence Legislative or Administrative Action.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/5000DollarFiler'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    5000DollarFiler:
      type: object
      properties:
        reportingPeriod:
          $ref: '#/components/schemas/ReportingPeriod'
        legislativeActionsLobbied:
          type: array
          items:
            type: string
          description: List of legislative bills or state agency administrative actions actively lobbied during the period.
        paymentsSummary:
          $ref: '#/components/schemas/PaymentsSummary'
        activityExpenses:
          type: array
          items:
            $ref: '#/components/schemas/ActivityExpense'
          description: List of activity expenses made during the reporting period.
        paymentToLobbyingCoalitions:
           $ref: './630.yaml#/components/schemas/PaymentsMade'
        totalPaymentToLobbyingCoalitions:
          type: number
          format: double
          description: Payments to Lobbying Coalitions.
        otherPayments:
           $ref: './640.yaml#/components/schemas/OtherPaymentsToInfluence'
        totalOtherPayments:
          type: number
          format: double
          description: Other payments.
        ratemakingPayments:
          type: number
          format: double
          description: Payments in connection with administrative testimony in ratemaking proceedings before the california public utilities commission.
        totalSectionB:
          type: number
          format: double
          description: Total Section B payments (OTHER PAYMENTS TO INFLUEN LEGISLATIVE OR ADMINISTRATIVE ACTION).
        donorOrCommittee:
          type: string
          description: Name of major donor or recipient committee which has filed a campaign disclosure statement.
        committeeId:
          type: string
          description: Identification number if recipient committee.
        campaignContributions:
          type: array
          items:
            $ref: '#/components/schemas/CampaignContribution'
          description: List of campaign contributions made during the reporting period.
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - filerDetails
        - reportingPeriod
        - legislativeActionsLobbied
        - paymentsSummary
        - attestation
        - campaignContributions
    ReportingPeriod:
      type: object
      properties:
        startDate:
          type: string
          format: date
          description: Start date of the reporting period.
        endDate:
          type: string
          format: date
          description: End date of the reporting period.
        cumulativePeriodStart:
          type: string
          format: date
          description: Start date of the cumulative period.
      required:
        - startDate
        - endDate
        - cumulativePeriodStart

    PaymentsSummary:
      type: object
      properties:
        activityExpenses:
          type: number
          format: double
          description: Total activity expenses incurred during the reporting period.
        paymentsToInfluence:
          type: number
          format: double
          description: Total payments to influence legislative or administrative actions during the reporting period.
        paymentsToPUCActivities:
          type: number
          format: double
          description: Payments in connection with administrative testimony in ratemaking proceedings before the Public Utilities Commission.
        totalPayments:
          type: number
          format: double
          description: Total payments made during the reporting period.
        noCampaignContributionsMade:
          type: boolean
          description: Mark yes if no campaign contributions made this period.
      required:
        - activityExpenses
        - paymentsToInfluence
        - paymentsToPUCActivities
        - totalPayments


    ActivityExpense:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of the contribution.
        firstName:
          type: string
          description: First Name of the recipient of the Payee.
        lastName:
          type: string
          description: Last Name of the recipient of the Payee.
        address:
          allOf:
           - $ref: './common-schemas.yaml#/components/schemas/Address'
          description: Address of Payee
        reportablePersons:
          type: array
          items:
            $ref: '#/components/schemas/ReportablePerson'
          description: List of Name and Official Position of Reportable Persons and Amount Benefiting Each.
        consideration:
          type: string
          # enum:
          #   - Breakfast
          #   - Dinner
          #   - Drinks
          #   - Event ticket
          #   - Flowers
          #   - Food & beverages
          #   - Reception
          #   - Travel expenses
          #   - Other
          description: Description of services provided.
        totalAmountOfActivity:
          type: number
          format: double
          description: Total Amount of Activity
        totalAmount:
          type: number
          format: double
          description: Total Amount
      required:
        - date
        - firstName
        - lastName
        - address
        - reportablePersons
        - consideration
        - totalAmount

    ReportablePerson:
      type: object
      properties:
        name:
          type: string
          description: Name of reportable person.
        officialPosition:
          type: string
          # enum:
          #   - Governor
          #   - Lieutenant Governor
          #   - Attorney General
          #   - Insurance Commissioner
          #   - Controller
          #   - Secretary of State
          #   - Treasurer
          #   - Superintendent of Public Instruction
          #   - Assembly Member
          #   - Senator
          #   - Assembly Staff Member
          #   - Senate Staff Member
          #   - Other
          description: Official position of reportable person.
        otherOfficialPosition:
          type: string
          description: Text for other official position
        amount:
          type: number
          format: double
          description: Amount benefitting reportable person.

    CampaignContribution:
      type: object
      properties:
        date:
          type: string
          format: date
          description: Date of the contribution.
        recipientName:
          type: string
          description: Name of the recipient of the contribution.
        recipientID:
          type: string
          description: Committee ID of the recipient, if applicable.
        amount:
          type: number
          format: double
          description: Amount of the contribution.
      required:
        - date
        - recipientName
        - amount
