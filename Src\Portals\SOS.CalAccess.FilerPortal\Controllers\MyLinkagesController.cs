using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.MyLinkagesCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.MyLinkages;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;
public class MyLinkagesController(
     IStringLocalizer<SharedResources> localizer,
     IToastService toastService,
     IMyLinkagesCtlSvc myLinkagesCtlSvc
    ) : Controller
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    #region Dashboard
    /// <summary>
    /// Loads dashboard page
    /// </summary>
    /// <returns>Dashboard View</returns>
    [HttpGet]
    public async Task<ActionResult> Index()
    {
        var model = await myLinkagesCtlSvc.GetDashboardViewData();
        return View(model);
    }

    /// <summary>
    /// Deletes an existing linkage
    /// Non-action API endpoint that is initiated by the Current Linkages small grid action 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete]
    public async Task<IActionResult> TerminateLinkage([Required] long id)
    {
        var result = await myLinkagesCtlSvc.TerminateLinkage(id);

        if (ModelState.IsValid)
        {
            if (result.IsOk())
            {
                toastService.Success(localizer[ResourceConstants.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateSuccessToast]);
                return Ok();
            }
            toastService.Error(localizer[ResourceConstants.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateFailureToast]);
            return NoContent();
        }
        return NotFound();
    }
    #endregion

    #region Send Linkage Request
    [HttpGet]
    public async Task<IActionResult> SendLinkageRequest()
    {
        var result = await myLinkagesCtlSvc.GetLinkageFilerTypeOptions();
        if (result.IsOk())
        {
            var data = result.Unwrap();
            var viewModel = new SendLinkageRequestViewModel
            {
                FilerTypeOptions = data.FilerTypeOptions
            };
            return View(viewModel);
        }
        // TD: Handle error case
        return NotFound();
    }

    [HttpPost]
    public async Task<IActionResult> SendLinkageRequest(SendLinkageRequestViewModel model)
    {
        if (ModelState.IsValid)
        {
            var result = await myLinkagesCtlSvc.SendLinkageRequestToFiler(model, ModelState);
            if (result.IsOk())
            {
                toastService.Success(localizer[ResourceConstants.MyLinkages.SendLinkageRequest.SendSuccessToast]);
                return RedirectToAction(nameof(Index));
            }
            else
            {
                toastService.Error(localizer[ResourceConstants.MyLinkages.SendLinkageRequest.SendFailureToast]);
            }
        }

        var optionsResult = await myLinkagesCtlSvc.GetLinkageFilerTypeOptions(model.FilerTypeId);
        if (optionsResult.IsOk())
        {
            var data = optionsResult.Unwrap();
            model.FilerTypeOptions = data.FilerTypeOptions;
            model.FilerRoleOptions = data.FilerRoleOptions;
            return View(model);
        }
        // TD: Handle error case
        return NotFound();
    }

    [HttpGet]
    public async Task<IActionResult> GetFilerRoleOptions([FromQuery] long filerTypeId)
    {
        if (ModelState.IsValid)
        {
            var result = await myLinkagesCtlSvc.GetFilerRoleOptions(filerTypeId);
            if (result.IsOk())
            {
                return Json(result.Unwrap());
            }
        }
        // TD: Handle error case
        return NotFound();
    }

    [HttpGet]
    public async Task<IActionResult> SearchFilerByNameOrId([FromQuery] string query, [FromQuery] long filerTypeId)
    {
        if (ModelState.IsValid)
        {
            var result = await myLinkagesCtlSvc.SearchFilerByNameOrId(query, filerTypeId);
            if (result.IsOk())
            {
                return Json(result.Unwrap());
            }
        }
        return BadRequest();
    }
    #endregion

    #region Accept/Reject Linkage request
    [HttpPost]
    public async Task<IActionResult> ReviewLinkageRequest(long id, string action)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var result = await myLinkagesCtlSvc.ReviewLinkageRequest(id, action);
        if (result.IsOk())
        {
            if (action.Equals("accept", StringComparison.OrdinalIgnoreCase))
            {
                toastService.Success(localizer[ResourceConstants.FilerAuthorizedUsers.PendingUserLinkages.AcceptSuccessToast]);
            }
            else if (action.Equals("reject", StringComparison.OrdinalIgnoreCase))
            {
                toastService.Success(localizer[ResourceConstants.FilerAuthorizedUsers.PendingUserLinkages.RejectSuccessToast]);
            }
            return RedirectToAction(nameof(Index));
        }
        toastService.Error(localizer[ResourceConstants.MyLinkages.ReviewRequest.NotFound]);
        return BadRequest();
    }
    #endregion

    #region Fetch Endpoints
    [HttpPost]
    public async Task<JsonResult> GetLinkageRequestForReview([FromBody] ReviewLinkageRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new
            {
                Error = ModelState.Values.SelectMany(v => v.Errors).Select(e => localizer[e.ErrorMessage].Value).First()
            });
        }

        var result = await myLinkagesCtlSvc.GetLinkageRequestForReview(request.InvitationCode);
        if (result.IsOk())
        {
            var data = result.Unwrap();
            if (data.IsValid)
            {
                return Json(data);
            }
            else
            {
                return Json(new
                {
                    Error = localizer[ResourceConstants.MyLinkages.ReviewRequest.NotFound].Value
                });
            }
        }

        return Json(new
        {
            Error = localizer[ResourceConstants.MyLinkages.ReviewRequest.LookupError].Value
        });
    }
    #endregion

    #region Private
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.AccountManagementTitle].Value;
    }
    #endregion
}
