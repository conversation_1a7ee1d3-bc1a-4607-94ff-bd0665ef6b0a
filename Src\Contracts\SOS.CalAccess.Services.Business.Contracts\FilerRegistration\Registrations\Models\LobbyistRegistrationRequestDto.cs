namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// SmoContactRequest.
/// </summary>
public sealed record LobbyistRegistrationRequestDto : IHasBusinessMailingAddresses
{
    /// <summary>
    /// Gets or sets the Id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or sets the value determining whether the registration is for themselves or for a lobbyist
    /// </summary>
    public bool? SelfRegister { get; set; }

    /// <summary>
    /// Gets or sets First Name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets Middle Name
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets Last Name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public PhoneNumberDto? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the fax number
    /// </summary>
    public PhoneNumberDto? FaxNumber { get; set; }

    /// <summary>
    /// Gets or sets Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets filer Id
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets or sets the Id of the selected lobbyist employer or lobbying firm
    /// </summary>
    public long? LobbyistEmployerOrLobbyingFirmId { get; set; }

    /// <summary>
    /// Gets or sets the business address
    /// </summary>
    public AddressDto? BusinessAddress { get; set; }

    /// <summary>
    /// Gets or sets the mailing address
    /// </summary>
    public AddressDto? MailingAddress { get; set; }

    /// <summary>
    /// Gets or sets the legislative session id
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets or sets the date of qualification
    /// </summary>
    public DateTime? DateOfQualification { get; set; }

    /// <summary>
    /// Gets or sets the value whether the lobbyist is a placement agent
    /// </summary>
    public bool? IsPlacementAgent { get; set; }


    /// <summary>
    /// Gets or sets the value whether the ethics course was completed within the past year or not
    /// </summary>
    public bool? CompletedEthicsCourseWithinPastYear { get; set; }

    /// <summary>
    /// Gets or sets the value whether the ethics course was completed
    /// </summary>
    public bool? CompletedEthicsCourse { get; set; }

    /// <summary>
    /// Gets or sets the current status of the ethics certification
    /// </summary>
    public bool? IsNewCertification { get; set; }

    /// <summary>
    /// Gets or sets the date of the completed ethics course
    /// </summary>
    public DateTime? CompletedCourseDate { get; set; }

    /// <summary>
    /// Gets or sets the status of what agencies are to be lobbied
    /// </summary>
    public bool? LobbyOnlySpecifiedAgencies { get; set; }

    /// <summary>
    /// Gets or sets the exact list of agencies
    /// </summary>
    public List<RegistrationAgencyDto>? Agencies { get; set; }

    /// <summary>
    /// Gets or sets whether the state legislature is being lobbied
    /// </summary>
    public bool? IsLobbyingStateLegislature { get; set; }

    /// <summary>
    /// Gets or sets the value for the attached photo
    /// </summary>
    public string? Photo { get; set; }

    /// <summary>
    /// Gets or sets whether the diligence verification statement was acknowledged for submission
    /// </summary>
    public bool? DiligenceVerificationStatement { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

    /// <summary>
    /// Gets or sets the IsSameAsCandidateAddress
    /// </summary>
    public bool IsSameAsCandidateAddress { get; set; }

    /// <summary>
    /// Gets or sets Withdrawal At
    /// </summary>
    public DateTime? WithdrawalAt { get; set; }

    /// <summary>
    /// Gets or sets the effective date of changes
    /// </summary>
    public DateTime? EffectiveDateOfChanges { get; set; }

    public bool IgnoreDecisionRule { get; set; }
}
