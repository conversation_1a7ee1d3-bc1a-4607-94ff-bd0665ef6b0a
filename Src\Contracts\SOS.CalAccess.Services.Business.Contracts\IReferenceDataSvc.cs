using Refit;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

namespace SOS.CalAccess.Services.Business;


#region Design Notes
/// <summary>
/// Defines service for accessing reference data values.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Reference Data Service.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing reference data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IFilerTypeRepository     | GetAll            | Get a filer types     |
/// | INotificationTypeRepository     | GetAll            | Get a notification types     |
#endregion
public interface IReferenceDataSvc
{
    /// <summary>
    /// Get List of all filer types
    /// </summary>
    /// <returns></returns>
    [Get(GetAllFilerTypesPath)]
    Task<IEnumerable<FilerType>> GetAllFilerTypes();
    const string GetAllFilerTypesPath = "/api/ReferenceData/FilerTypes";

    /// <summary>
    /// Get List of all filer roles
    /// </summary>
    /// <returns></returns>
    [Get(GetAllFilerRolesPath)]
    Task<IEnumerable<FilerRoleDto>> GetAllFilerRoles();
    const string GetAllFilerRolesPath = "/api/ReferenceData/FilerRoles";

    /// <summary>
    /// Get List of all notification types
    /// </summary>
    /// <returns></returns>
    [Get(GetAllNotificationTypesPath)]
    Task<IEnumerable<NotificationType>> GetAllNotificationTypes();
    const string GetAllNotificationTypesPath = "/api/ReferenceData/NotificationTypes";

    /// <summary>
    /// Get office by officeId
    /// </summary>
    /// <param name="officeId"></param>
    /// <returns></returns>
    [Get(GetOfficePath)]
    Task<Office?> GetOffice(long officeId);
    const string GetOfficePath = "/api/ReferenceData/Offices/{officeId}";

    /// <summary>
    /// Get all political parties including non-partisan
    /// </summary>
    /// <returns></returns>
    [Get(GetAllPoliticalPartiesPath)]
    Task<IEnumerable<PoliticalParty>> GetAllPoliticalParties();
    const string GetAllPoliticalPartiesPath = "/api/ReferenceData/PoliticalParties";


    /// <summary>
    /// List permissions
    /// </summary>
    /// <returns></returns>
    [Get(ListPermissionsPath)]
    public Task<IEnumerable<MaintainPermissionsDto>> ListPermissions();
    const string ListPermissionsPath = "/api/ReferenceData/ListPermissions";

    /// <summary>
    /// Get List of all official position types
    /// </summary>
    /// <returns></returns>
    [Get(GetAllOfficialPositionsPath)]
    Task<IEnumerable<OfficialPosition>> GetAllOfficialPositions();
    const string GetAllOfficialPositionsPath = "/api/ReferenceData/OfficialPositions";

    /// <summary>
    /// Get List of all agency types
    /// </summary>
    /// <returns></returns>
    [Get(GetAllAgenciesPath)]
    Task<IEnumerable<Agency>> GetAllAgencies();
    const string GetAllAgenciesPath = "/api/ReferenceData/Agencies";

    /// <summary>
    ///  Search all agencies
    /// </summary>
    /// <returns></returns>
    [Get(SearchAllAgenciesPath)]
    Task<IEnumerable<Agency>> SearchAllAgencies([Query] string query);
    const string SearchAllAgenciesPath = "/api/ReferenceData/Agencies/Search";

    /// <summary>
    /// Get List of all agency types
    /// </summary>
    /// <returns></returns>
    [Get(GetAllPaymentCodesPath)]
    Task<IEnumerable<PaymentCodeRefResponse>> GetAllPaymentCodes();
    const string GetAllPaymentCodesPath = "/api/ReferenceData/PaymentCodes";

    /// <summary>
    /// Get List of all distribution methods for lobbying advertisement
    /// </summary>
    /// <returns></returns>
    [Get(GetAllAdvertisementDistributionMethodsPath)]
    Task<IEnumerable<AdvertisementDistributionMethodRefResponse>> GetAllAdvertisementDistributionMethods();
    const string GetAllAdvertisementDistributionMethodsPath = "/api/ReferenceData/AdvertisementDistributionMethods";

    /// <summary>
    ///  Search all bills
    /// </summary>
    /// <returns></returns>
    [Get(SearchAllBillsPath)]
    Task<IEnumerable<Bill>> SearchAllBills([Query] string query);
    const string SearchAllBillsPath = "/api/ReferenceData/Bills/Search";

    /// <summary>
    ///  Get all country codes
    /// </summary>
    /// <returns></returns>
    [Get(GetAllCountryCodesPath)]
    Task<IEnumerable<Country>> GetAllCountryCodes();
    const string GetAllCountryCodesPath = "/api/ReferenceData/CountryCodes";

    /// <summary>
    ///  Get country by id
    /// </summary>
    /// <returns></returns>
    [Get(GetCountryByIdPath)]
    Task<Country?> GetCountryById(long id);
    const string GetCountryByIdPath = "/api/ReferenceData/CountryCodes/{id}";

    /// Get All expenditure codes
    /// </summary>
    /// <returns>A collection of expenditure code</returns>
    [Get("/" + GetAllExpenditureCodesAsyncPath)]
    Task<IEnumerable<ExpenditureCode>> GetAllExpenditureCodesAsync();
    const string GetAllExpenditureCodesAsyncPath = "api/ReferenceData/ExpenditureCodes";

    /// Get All nature and interest types
    /// </summary>
    /// <returns>A collection of nature and interest types</returns>
    [Get("/" + GetAllNatureAndInterestTypesAsyncPath)]
    Task<IEnumerable<NatureAndInterestType>> GetAllNatureAndInterestTypesAsync();
    const string GetAllNatureAndInterestTypesAsyncPath = "api/ReferenceData/NatureAndInterestTypes";

    /// Get All industry group classification types
    /// </summary>
    /// <returns>A collection industry group classification types</returns>
    [Get("/" + GetAllIndustryGroupClassificationTypesAsyncPath)]
    Task<IEnumerable<IndustryGroupClassificationType>> GetAllIndustryGroupClassificationTypesAsync();
    const string GetAllIndustryGroupClassificationTypesAsyncPath = "api/ReferenceData/IndustryGroupClassificationTypes";
}
