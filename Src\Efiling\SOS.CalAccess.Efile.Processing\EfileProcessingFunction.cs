using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text;
using Azure;
using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Common.DataValidation;
using SOS.CalAccess.Services.Common.FileSystem;

namespace SOS.CalAccess.Efile.Processing;

/// <h4>Design Description</h4>
/// <p>
/// Service Bus Message triggered Azure Function to process incoming requests from third party software
/// vendors to file registration and disclosure forms.
/// </p>
/// <p>
/// Processing Steps
/// 1. Update the status of the API request to reflect that the message is being processed.  
/// 2. Retreive the request from Azure Blob Storage, parse it, and store the results in the database.
/// 3. Iniate the Decisions workflow to process the filing.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The Decisions workflow will update the status of the filing based on the results of the business rules.
/// </p>
/// <p>
/// The Decisions workflow will send notification to users after the filing has been processed.
/// </p>
/// <p>
/// Third Party vendors will be able to check the status of an api request using the submission id received
/// when the request was submitted.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Allow third party software vendors to file registration and disclosure forms
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>AP-01</li>
/// <li>AP-02</li>
/// <li>AP-03</li>
/// <li>AP-04</li>
/// <li>AP-05</li>
/// <li>AP-06</li>
/// <li>AP-07</li>
/// <li>AP-08</li>
/// <li>AP-09</li>
/// <li>AP-10</li>
/// <li>AP-11</li>
/// <li>AP-12</li>
/// <li>AP-13</li>
/// <li>AP-14</li>
/// <li>AP-15</li>
/// <li>AP-16</li>
/// <li>AP-17</li>
/// <li>AP-18</li>
/// <li>AP-19</li>
/// <li>AP-20</li>
/// <li>AP-21</li>
/// <li>AP-22</li>
/// <li>AP-23</li>
/// <li>AP-24</li>
/// <li>AP-25</li>
/// <li>AP-26</li>
/// <li>AP-27</li>
/// <li>AP-28</li>
/// <li>AP-29</li>
/// <li>AP-30</li>
/// <li>AP-31</li>
/// <li>AP-32</li>
/// <li>AP-33</li>
/// <li>AP-34</li>
/// <li>AP-35</li>
/// <li>AP-36</li>
/// <li>AP-37</li>
/// <li>AP-38</li>
/// <li>AP-39</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | -------------------------------------------------------------------------------------------------------------------------------------- |
/// | Service                        | Operation                               | Description                                                 |
/// | -------------------------------------------------------------------------------------------------------------------------------------- |
/// | IFileSvc                       | CreateFileStream()                      | Create a Stream to write to a new file                      |
/// | IApiRequestRepository          | GetById() | Retrieve api request record |                                                             |
/// | IApiRequestRepository          | Update() | Update api request record    |                                                             |
/// | IFilingRepository              | BulkLoadFiling()                        | Create new Filing and all related contacts and Transactions |
/// | IFilingSvc                     | SubmitFiling()                          | Submit Filing for business rule processing                  |
/// | ______________________________________________________________________________________________________________________________________ |
[System.Diagnostics.CodeAnalysis.SuppressMessage("SonarQube", "S107", Justification = "Temporarily suppress for this case")]
public partial class EfileProcessingFunction
{
    #region Properties

    /// <summary>
    /// References the ApiRequst db object that's related to the message we're processing.
    /// </summary>
    private ApiRequest? _apiRequest;

    /// <summary>
    /// Used for Blob Storage operations
    /// </summary>
    private readonly IFileSvc _fileSvc;

    /// <summary>
    /// Service for processing (validating & saving) the form submission
    /// </summary>
    private readonly ICandidateIntentionRegistrationSvc _registrationSvc;

    /// <summary>
    /// Service for processing (validating & saving) the form submission
    /// </summary>
    private readonly ISmoRegistrationSvc _smoRegistrationSvc;

    /// <summary>
    /// Reference to settings in either settings.json or local.settings.json, depending
    /// on the runtime environment.
    /// </summary>
    private readonly IConfiguration _configuration;

    /// <summary>
    /// Used for db operations on ApiRequest records
    /// </summary>
    private readonly IApiRequestSvc _apiRequestSvc;

    /// <summary>
    /// Event/error logging utility
    /// </summary>
    private readonly ILogger<EfileProcessingFunction> _logger;

    /// <summary>
    /// Captured errors from Decisions
    /// </summary>
    private readonly List<ApiError> _errors = new();

    /// <summary>
    /// The Service Bus message that we're processing
    /// </summary>
    private ServiceBusReceivedMessage? _message;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private ServiceBusMessageActions? _messageActions;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private readonly IEfileSubmissionSvc _efileSubmissionSvc;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private readonly IForm470Svc _form470Svc;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private readonly IForm470SSvc _form470SSvc;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private readonly IFilingSvc _filingSvc;

    private readonly ILobbyistRegistrationSvc _lobbyistRegistrationSvc;

    /// <summary>
    /// Message when unable to parse the file from blob storage
    /// </summary>
    private const string BlobParseMessage = "Unable to parse the file from blob storage";

    /// <summary>
    /// Message when form submission fails
    /// </summary>
    private const string FormSubmissionFailedMessage = "Form submission failed";

    /// <summary>
    /// Message when form submission fails
    /// </summary>
    private const string FilingIdNotFoundMessage = "Filing Id not found in Decisions response";

    //  ''
    /// <summary>
    /// Message when there is a null response from the registration service
    /// </summary>
    private const string RegistrationServiceNullResponseMessage = "Received a null response from the registration service";

    /// <summary>
    /// Configuration keys that must exist and be non-empty at runtime.
    /// If any are missing, dependency injection and application startup will fail.
    /// </summary>
    private static readonly string[] _requiredSettings =
    {
        "AzureValues:BlobStorageContainerName",
        "AzureValues:IsProcessingRetryEnabledOnSystemError",
        "AzureValues:QueueName"
    };

    #endregion

    /// <summary>
    /// Validate/configure settings and set up services.
    /// </summary>
    /// <param name="logger">Used only for debugging. This logs at Information/Error levels only.</param>
    /// <param name="configuration">
    /// Optional. Can be loaded directly by D.I., a constructor in unit tests. Falls back
    /// to Initialize Configuration if this param is not provided.
    /// </param>
    /// <param name="apiRequestSvc">Service for db operations on ApiRequest objects</param>
    /// <param name="candidateIntentionRegistrationSvc">Service for operations on a form 501 submission</param>
    /// <exception cref="ArgumentNullException">Thrown if any required dependency is null</exception>
    public EfileProcessingFunction(
        ILogger<EfileProcessingFunction> logger,
        IConfiguration configuration,
        IApiRequestSvc apiRequestSvc,
        ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
        IFileSvc fileSvc,
        IEfileSubmissionSvc efileSubmissionSvc,
        ISmoRegistrationSvc smoRegistrationSvc,
        IForm470Svc form470Svc,
        IFilingSvc filingSvc,
        IForm470SSvc form470SSvc,
        ILobbyistRegistrationSvc lobbyistRegistrationSvc)
    {
        _configuration = configuration ?? InitializeConfiguration();
        ValidateSettings();
        _apiRequestSvc = apiRequestSvc ?? throw new ArgumentNullException(nameof(apiRequestSvc));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _registrationSvc = candidateIntentionRegistrationSvc ?? throw new ArgumentNullException(nameof(candidateIntentionRegistrationSvc));
        _fileSvc = fileSvc ?? throw new ArgumentNullException(nameof(fileSvc));
        _efileSubmissionSvc = efileSubmissionSvc ?? throw new ArgumentNullException(nameof(efileSubmissionSvc));
        _smoRegistrationSvc = smoRegistrationSvc ?? throw new ArgumentNullException(nameof(smoRegistrationSvc));
        _form470Svc = form470Svc ?? throw new ArgumentNullException(nameof(form470Svc));
        _form470SSvc = form470SSvc ?? throw new ArgumentNullException(nameof(form470SSvc));
        _filingSvc = filingSvc ?? throw new ArgumentNullException(nameof(filingSvc));
        _lobbyistRegistrationSvc = lobbyistRegistrationSvc ?? throw new ArgumentNullException(nameof(lobbyistRegistrationSvc));
    }

    /// <summary>
    /// Service Bus Message triggered Azure Function to process incoming requests from third party software vendors to file registration and disclosure forms.
    /// </summary>
    /// <param name="message"></param>
    /// <param name="messageActions"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// AzureServiceBus [label="Azure\nServiceBus"], EfileProcessingFunction [label="EfileProcessing\nFunction"], IApiRequestRepository [label="IApiRequest\nRepository"], IFileSvc, IFilingRepository [label="IFiling\nRepository"], IFilingSvc;
    /// AzureServiceBus => EfileProcessingFunction [label="Run()"];
    /// EfileProcessingFunction => IApiRequestRepository [label="GetById()"];
    /// IApiRequestRepository => EfileProcessingFunction [label="return"];
    /// EfileProcessingFunction => IApiRequestRepository [label="Update()"];
    /// IApiRequestRepository => EfileProcessingFunction [label="return"];
    /// EfileProcessingFunction => IFileSvc [label="ReadFileStream()"];
    /// IFileSvc => EfileProcessingFunction [label="return"];
    /// EfileProcessingFunction => IFilingRepository [label="BulkLoadFiling()"];
    /// IFilingRepository => EfileProcessingFunction [label="return"];
    /// EfileProcessingFunction => IFilingSvc [label="SubmitFiling()"];
    /// IFilingSvc => EfileProcessingFunction [label="return"];
    /// EfileProcessingFunction >> AzureServiceBus [label="CompleteMessageAsync"];
    /// \endmsc
    [Function(nameof(EfileProcessingFunction))]
    public async Task Run(
        [ServiceBusTrigger("%QueueName%", Connection = "ServiceBusConnection")]
        ServiceBusReceivedMessage message,
        ServiceBusMessageActions messageActions)
    {
        ArgumentNullException.ThrowIfNull(message);
        ArgumentNullException.ThrowIfNull(messageActions);

        _logger.LogInformation("EfileProcessingFunction() called");
        _message = message;
        _messageActions = messageActions;

        try
        {
            // Get ApiRequest and set to "Processing"
            _apiRequest = GetApiRequestFromMessageBody();
            _apiRequest = await _apiRequestSvc.ProcessRequest(_apiRequest!.Id);
            string formName = string.Empty;
            // Get original request from Blob Storage and convert into DTO object for submission
            string fileContents = await GetFileContentsFromBlobStorage();
            if (!string.IsNullOrEmpty(_apiRequest?.BlobFilename))
            {
                int index = _apiRequest.BlobFilename.IndexOf("_Guid", StringComparison.Ordinal);
                if (index > -1)
                {
                    formName = _apiRequest.BlobFilename[..index];
                }
            }

            if (formName == "Campaign-Statement-CandidateIntention")
            {
                EfileCandidateIntentionStatement jsonData = JsonConvert.DeserializeObject<EfileCandidateIntentionStatement>(fileContents)
                   ?? throw new InvalidOperationException(BlobParseMessage);
                CandidateIntentionStatementDto submission = _efileSubmissionSvc.CreateSubmissionDto(jsonData, _apiRequest!);

                // Perform Decisions validation and form submission in a single call
                RegistrationResponseDto registrationResponse = await SubmitCandidateIntentionStatementForm(submission);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);

                await AcceptAndCompleteRequest(filingId);
            }
            else if (formName.Equals(FormNameToIdMapping.CampaignRegistrationSlateMailer, StringComparison.OrdinalIgnoreCase) || formName.Equals(FormNameToIdMapping.CampaignTerminationSlateMailer, StringComparison.OrdinalIgnoreCase))
            {
                EfileSlateMailerOrganization jsonData = JsonConvert.DeserializeObject<EfileSlateMailerOrganization>(fileContents)
                                     ?? throw new InvalidOperationException(BlobParseMessage);
                bool isTermination = formName.Equals(FormNameToIdMapping.CampaignTerminationSlateMailer, StringComparison.OrdinalIgnoreCase);
                EfileSlateMailerOrganizationDto submission = _efileSubmissionSvc.CreateSubmissionDtoSMO(jsonData, _apiRequest!, isTermination);
                // Perform Decisions validation and form submission in a single call
                RegistrationResponseDto registrationResponse = await SubmitSlateMailerOrganizationForm(submission, isTermination);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);

                await AcceptAndCompleteRequest(filingId);
            }
            else if (formName.Equals(FormNameToIdMapping.CampaignDisclosureCandidateCampaignStatementShort, StringComparison.OrdinalIgnoreCase))
            {
                var candidateStmtShort = JsonConvert.DeserializeObject<EfileCandidateCampaignStatementShort>(fileContents)
                   ?? throw new InvalidOperationException(BlobParseMessage);
                var submission = _efileSubmissionSvc.CreateCandidateStatementShortSubmissionDto(candidateStmtShort, _apiRequest!);

                // Perform Decisions validation and form submission in a single call
                ValidatedForm470ResponseDto registrationResponse = await SubmitCandidateStatementShortForm(submission);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);

                await AcceptAndCompleteRequest(filingId);
            }
            else if (formName.Equals(FormNameToIdMapping.CampaignDisclosureCandidateCampaignStatementSupplement, StringComparison.OrdinalIgnoreCase))
            {
                var candidateStmtSupplement = JsonConvert.DeserializeObject<EfileCandidateCampaignStatementSupplement>(fileContents)
                   ?? throw new InvalidOperationException(BlobParseMessage);
                var submission = _efileSubmissionSvc.CreateCandidateStatementSupplementSubmissionDto(candidateStmtSupplement, _apiRequest!);

                // Perform Decisions validation and form submission in a single call
                ValidatedForm470SResponseDto registrationResponse = await SubmitCandidateStatementSupplementForm(submission);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);

                await AcceptAndCompleteRequest(filingId);
            }
            else if (formName == "Lobbying-Disclosure-Lobbyist")
            {
                EfileLobbyistReport jsonData = JsonConvert.DeserializeObject<EfileLobbyistReport>(fileContents)
                                     ?? throw new InvalidOperationException(BlobParseMessage);
                LobbyistReportDto submission = await _efileSubmissionSvc.CreateSubmissionDtoLR(jsonData, _apiRequest!);
                RegistrationResponseDto registrationResponse = await SubmitLobbyistReportForm(submission);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);
                await AcceptAndCompleteRequest(filingId);
            }
            else if (formName.Equals(FormNameToIdMapping.LobbyingCertificationLobbyist, StringComparison.OrdinalIgnoreCase))
            {
                var lobbyistRegistration = JsonConvert.DeserializeObject<EfileLobbyingCertificationLobbyist>(fileContents)
                   ?? throw new InvalidOperationException(BlobParseMessage);
                var submission = _efileSubmissionSvc.CreateLobbyistRegistrationSubmissionDto(lobbyistRegistration, _apiRequest!);

                // Perform Decisions validation and form submission in a single call
                RegistrationResponseDto registrationResponse = await SubmitLobbyistRegistrationForm(submission);
                string filingId = registrationResponse!.Id!.Value.ToString(CultureInfo.InvariantCulture);

                await AcceptAndCompleteRequest(filingId);
            }
            else
            {
                throw new InvalidOperationException($"Form name {formName} not recognized");
            }
        }
        catch (Exception ex)
        {
            await HandleFailure(ex);
        }
    }

    #region Private Methods

    /// <summary>
    /// Mark the ApiRequest as Accepted and remove from the processing queue
    /// </summary>
    /// <param name="filingId">The supplied filingId is saved to the ApiRequest record</param>
    private async Task AcceptAndCompleteRequest(string filingId)
    {
        await _apiRequestSvc.AcceptRequest(_apiRequest!.Id, filingId);
        await _messageActions!.CompleteMessageAsync(_message!);
        _logger.LogInformation("EfileProcessingFunction() finished successfully");
    }

    /// <summary>
    /// Retrieve the ApiRequest object from the body of the Service Bus Message. 
    /// </summary>
    /// <exception cref="InvalidOperationException">Exception thrown if ApiRequest is empty or invalid</exception>
    private ApiRequest GetApiRequestFromMessageBody()
    {
        string messageBody = Encoding.UTF8.GetString(_message!.Body);
        ApiRequest apiRequest = JsonConvert.DeserializeObject<ApiRequest>(messageBody)
            ?? throw new InvalidOperationException("Unable to obtain ApiRequest from the Service Bus message");

        if (string.IsNullOrWhiteSpace(apiRequest.BlobFilename))
        {
            throw new InvalidOperationException("Can't proceed with processing. BlobFilename is empty or missing.");
        }

        if (apiRequest.ReceivedAt == default)
        {
            throw new InvalidOperationException("Can't proceed with processing. ReceivedAt has not been set.");
        }

        return apiRequest;
    }

    /// <summary>
    /// Reads the full contents of a file from Azure Blob Storage into a string
    /// </summary>
    /// <returns>All file contents as a string</returns>
    /// <exception cref="RequestFailedException">If file is empty</exception>
    private async Task<string> GetFileContentsFromBlobStorage()
    {
        string containerName = _configuration["AzureValues:BlobStorageContainerName"]!;
        using Stream readStream = await _fileSvc.ReadFile(containerName, _apiRequest!.BlobFilename, CancellationToken.None);
        using var reader = new StreamReader(readStream, Encoding.UTF8);
        string fileContents = await reader.ReadToEndAsync();
        if (string.IsNullOrWhiteSpace(fileContents))
        {
            throw new RequestFailedException("File from Blob Storage was empty");
        }

        return fileContents;
    }

    /// <summary>
    /// Retry processing if failed due to system error. Mark message as complete if
    /// processing failed due to business rules (Decisions). 
    /// </summary>
    /// <param name="ex">If exception type is ValidationException this indicates that it was an error returned from Decisions</param>
    private async Task HandleFailure(Exception ex)
    {
        if (ex is ValidationException)
        {
            if (_apiRequest != null)
            {
                await _apiRequestSvc.RejectRequestForBusinessRuleViolation(_apiRequest.Id, _errors);
            }

            _logger.LogError(ex, "EfileProcessingFunction() finished with validation error(s)");
            await _messageActions!.CompleteMessageAsync(_message!);
        }
        else
        {
            if (_apiRequest != null)
            {
                try
                {
                    await _apiRequestSvc.RejectRequestForSystemError(_apiRequest.Id, _errors);
                }
                catch (InvalidOperationException invalidOpEx)
                     when (invalidOpEx.Message.Contains("API Request", StringComparison.InvariantCulture)
                          && invalidOpEx.Message.Contains("is null", StringComparison.InvariantCulture))
                {

                    // Intentionally ignored: message indicates "API Request {id} is null."
                }
            }

            _logger.LogError(ex, "EfileProcessingFunction() finished with system error(s)");
            await (_configuration.GetValue<bool>("AzureValues:IsProcessingRetryEnabledOnSystemError")
                ? _messageActions!.AbandonMessageAsync(_message!)
                : _messageActions!.CompleteMessageAsync(_message!));
        }
    }


    /// <summary>
    /// Initialize the _configuration object by connecting to the correct settings file, depending
    /// on whether running locally or on Azure.
    /// </summary>
    private static IConfiguration InitializeConfiguration()
    {
        string environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Development";
        string settingsFilename = environment.Equals("Development", StringComparison.OrdinalIgnoreCase)
            ? "local.settings.json"
            : "settings.json";

        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(settingsFilename)
            .Build();
    }

    /// <summary>
    /// Call the Candidate Intention Registration Service to submit the form, then return the response
    /// </summary>
    /// <param name="submission">Form 501 DTO</param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException">Thrown if response from registration service is null</exception>
    /// <exception cref="ValidationException">Thown if the response contains any validation errors</exception>
    private async Task<RegistrationResponseDto> SubmitSlateMailerOrganizationForm(EfileSlateMailerOrganizationDto submission, bool isTermination)
    {
        RegistrationResponseDto registrationResponse = isTermination ? await _smoRegistrationSvc.SmoTerminationForEfile(submission) ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage) : await _smoRegistrationSvc.SubmitSmoRegistrationForEfile(submission)?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);
        if (registrationResponse.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(registrationResponse.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (registrationResponse.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return registrationResponse;
    }

    private async Task<RegistrationResponseDto> SubmitCandidateIntentionStatementForm(CandidateIntentionStatementDto submission)
    {
        RegistrationResponseDto registrationResponse = await _registrationSvc.SubmitCandidateIntentionStatementForEfile(submission)
                ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);

        if (registrationResponse.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(registrationResponse.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (registrationResponse.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return registrationResponse;
    }

    private async Task<ValidatedForm470ResponseDto> SubmitCandidateStatementShortForm(CandidateStatementShortSubmissionDto submission)
    {
        ValidatedForm470ResponseDto registrationResponse = await _form470Svc.SubmitCandidateStatementShortForEfile(submission)
                ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);

        if (registrationResponse.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(registrationResponse.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (registrationResponse.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return registrationResponse;
    }

    /// <summary>
    /// Call the Lobbyist Report Service to submit the form, then return the response
    /// </summary>
    /// <param name="submission">Form 501 DTO</param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException">Thrown if response from registration service is null</exception>
    /// <exception cref="ValidationException">Thown if the response contains any validation errors</exception>
    private async Task<RegistrationResponseDto> SubmitLobbyistReportForm(LobbyistReportDto submission)
    {
        RegistrationResponseDto registrationResponse = await _filingSvc.SubmitLobbyistReportForEfile(submission)
                ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);

        if (registrationResponse.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(registrationResponse.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (registrationResponse.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return registrationResponse;
    }

    private async Task<ValidatedForm470SResponseDto> SubmitCandidateStatementSupplementForm(CandidateStatementSupplementSubmissionDto submission)
    {
        ValidatedForm470SResponseDto response = await _form470SSvc.SubmitCandidateSupplementForEfile(submission)
                ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);

        if (response.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(response.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (response.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return response;
    }

    private async Task<RegistrationResponseDto> SubmitLobbyistRegistrationForm(LobbyistRegistrationSubmissionDto submission)
    {
        RegistrationResponseDto response = await _lobbyistRegistrationSvc.SubmitLobbyistRegistrationForEfile(submission)
                ?? throw new InvalidOperationException(RegistrationServiceNullResponseMessage);

        if (response.ValidationErrors?.Count > 0)
        {
            _errors.AddRange(response.ValidationErrors?.ToApiErrors() ?? new List<ApiError>());
            throw new ValidationException(FormSubmissionFailedMessage);
        }

        if (response.Id == null)
        {
            throw new InvalidOperationException(FilingIdNotFoundMessage);
        }

        return response;
    }
    /// <summary>
    /// Check if any requried setting is missing or empty. Only reports the first one found.
    /// </summary>
    /// <exception cref="InvalidOperationException">Identifies which one is missing.</exception>
    private void ValidateSettings()
    {
        string? firstMissingSetting = _requiredSettings
            .FirstOrDefault(key => string.IsNullOrWhiteSpace(_configuration[key]));

        if (firstMissingSetting != null)
        {
            throw new InvalidOperationException($"Required setting \"{firstMissingSetting}\" is missing or empty.");
        }
    }

    #endregion

}
