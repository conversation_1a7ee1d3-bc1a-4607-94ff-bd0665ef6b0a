namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// LobbyistEmployerStateAgenciesRequest.
/// </summary>
public sealed record LobbyistEmployerStateAgenciesRequest
{
    /// <summary>
    /// Gets or sets the exact list of agencies
    /// </summary>
    public List<RegistrationAgencyDto>? Agencies { get; set; }

    /// <summary>
    /// Gets or sets whether the state legislature is being lobbied
    /// </summary>
    public bool? IsLobbyingStateLegislature { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }
}
