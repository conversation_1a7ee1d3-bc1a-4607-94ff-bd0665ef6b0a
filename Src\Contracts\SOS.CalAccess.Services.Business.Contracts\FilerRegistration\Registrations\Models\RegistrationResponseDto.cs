using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Response record for generic registration data.
/// </summary>
public record RegistrationResponseDto
{
    public long? Id { get; set; }
    public bool Valid { get; set; }
    public List<WorkFlowError> ValidationErrors { get; set; }
    public long? StatusId { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public long? FilerId { get; set; }

    public List<NotificationTrigger>? Notifications { get; set; }

    public AddressValidationResult? AddressValidationResult { get; set; }
    public RegistrationResponseDto() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="RegistrationItemResponse"/> class.
    /// </summary>
    /// <param name="registration">The <see cref="Registration"/> instance to use as source for data.</param>
    public RegistrationResponseDto(Registration registration, bool valid, List<WorkFlowError> validationErrors)
    {
        Id = registration.Id;
        Valid = valid;
        ValidationErrors = validationErrors;
        StatusId = registration.StatusId;
        ApprovedAt = registration.ApprovedAt;
        FilerId = registration.FilerId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="RegistrationItemResponse"/> class.
    /// </summary>
    /// <param name="registration">The <see cref="Registration"/> instance to use as source for data.</param>
    public RegistrationResponseDto(long? id, bool valid, List<WorkFlowError>? validationErrors, long? statusId, List<NotificationTrigger>? notifications, DateTime? approvedAt = default, long? filerId = default)
    {
        Id = id;
        Valid = valid;
        ValidationErrors = validationErrors ?? new List<WorkFlowError>();
        StatusId = statusId;
        ApprovedAt = approvedAt;
        FilerId = filerId;
        Notifications = notifications;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="RegistrationItemResponse"/> class.
    /// </summary>
    /// <param name="registration">The <see cref="Registration"/> instance to use as source for data.</param>
    public RegistrationResponseDto(long? id, bool valid, List<WorkFlowError>? validationErrors, long? statusId, DateTime? approvedAt = default, long? filerId = default)
    {
        Id = id;
        Valid = valid;
        ValidationErrors = validationErrors ?? new List<WorkFlowError>();
        StatusId = statusId;
        ApprovedAt = approvedAt;
        FilerId = filerId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="RegistrationItemResponse"/> class.
    /// </summary>
    /// <param name="registration">The <see cref="Registration"/> instance to use as source for data.</param>
    public RegistrationResponseDto(long? id, bool valid, List<WorkFlowError>? validationErrors, long? statusId, AddressValidationResult addressValidationResult)
    {
        Id = id;
        Valid = valid;
        ValidationErrors = validationErrors ?? new List<WorkFlowError>();
        StatusId = statusId;
        AddressValidationResult = addressValidationResult;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="RegistrationItemResponse"/> class.
    /// </summary>
    /// <param name="registration">The <see cref="Registration"/> instance to use as source for data.</param>
    public RegistrationResponseDto(long? id, bool valid, List<WorkFlowError>? validationErrors)
    {
        Id = id;
        Valid = valid;
        ValidationErrors = validationErrors ?? new List<WorkFlowError>();
    }
}

public record AddressValidationResult
{
    public List<AddressResult> AddressResults { get; set; } = new();
}

public class AddressResult
{
    public AddressDto? Address { get; set; }

    public AddressValidationStatus Status { get; set; }

    public AddressType Type { get; set; }
}

public enum AddressType
{
    Unknown = 0,
    Candidate = 1,
    Mailing = 2
}
