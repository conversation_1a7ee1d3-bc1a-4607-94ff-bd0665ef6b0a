openapi: 3.0.3
info:
  title: Form 604 - Lobbyist Certification Statement API
  description: API for submitting and managing Form 604 Lobbyist Certification Statements.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Certification/Lobbyist:
    post:
      summary: Submit Form 604 Lobbyist Certification Statement
      description: Submit a Form 604 report for lobbyist certification, renewal, or amendment.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyistCertificationStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyistCertificationStatement:
      type: object
      properties:
        legislativeSessionId:
          type: number
          format: int64
          description: The Legislative Session Id.
          required: true
        lobbyistDetails:
          $ref: '#/components/schemas/LobbyistDetails'
          required: true
        businessAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
          required: true
        mailingAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
          required: true
        ethicsOrientation:
          $ref: '#/components/schemas/EthicsOrientation'
          required: true
        agenciesLobbied:
          $ref: '#/components/schemas/AgenciesLobbied'
          required: true
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'

    LobbyistDetails:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the lobbyist.          
        lastName:
          type: string
          description: Last name of the lobbyist.          
        middleInitial:
          type: string
          description: Middle initial of the lobbyist (optional).
        dateQualified:
          type: string
          format: date
          description: Date the lobbyist qualified as a lobbyist.          
        isLobbyistPlacementAgent:
          type: boolean
          description: Indicates if the lobbyist is a placement agent.          
        telephoneNumber:
          $ref: './common-schemas.yaml#/components/schemas/PhoneNumber'
        faxNumber:
          $ref: './common-schemas.yaml#/components/schemas/PhoneNumber'
        email:
          type: string
          format: email
          description: Email address of the lobbyist.         
        lobbyistEmployerOrLobbyingFirmId:
          type: string
          description: Name of the employer or lobbying firm.
        required:
        - firstName
        - lastName
        - dateQualified
        - isLobbyistPlacementAgent
        - telephoneNumber
        - email

    EthicsOrientation:
      type: object
      properties:
        isCourseTaken:
          type: boolean
          description: Indicates if the course was not taken within the past 12 months.
        courseCompletedDate:
          type: string
          format: date
          description: Date of ethics orientation course completion (if applicable).

    AgenciesLobbied:
      type: object
      properties:
        willLobbyFromRegistration:
          type: boolean
          description: Indicates if the lobbyist will lobby the agencies listed in the Lobbyist Employer or Lobbying Firm Registration Statement (Form 601/603).
        stateLegislature:
          type: boolean
          description: Indicates if the lobbyist will lobby the State Legislature.
        stateAgencies:
          type: array
          items:
            type: number
            format: int64
          description: List of specific state agencies the lobbyist will lobby.
