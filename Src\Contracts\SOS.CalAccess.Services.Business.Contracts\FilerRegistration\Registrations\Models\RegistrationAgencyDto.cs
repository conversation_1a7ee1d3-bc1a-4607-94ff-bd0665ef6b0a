// <copyright file="RegistrationAgency.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// SmoContactRequest.
/// </summary>
public sealed record RegistrationAgencyDto
{
    /// <summary>
    /// Gets or sets the Id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or sets the registration id to which the registration agency is linked.
    /// </summary>
    public long? RegistrationId { get; set; }

    /// <summary>
    /// Gets or sets the agency id to which the registration agency is linked.
    /// </summary>
    public long? AgencyId { get; set; }

    /// <summary>
    /// Gets or sets the agency name to which the registration agency is linked.
    /// </summary>
    public string? AgencyName { get; set; }

    public RegistrationAgencyDto() { }

    public RegistrationAgencyDto(RegistrationAgency entity)
    {
        Id = entity.Id;
        RegistrationId = entity.RegistrationId;
        AgencyId = entity.AgencyId;
        AgencyName = entity.Agency?.Name ?? string.Empty;
    }
}
