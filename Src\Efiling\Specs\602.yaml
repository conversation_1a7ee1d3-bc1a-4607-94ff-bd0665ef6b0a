openapi: 3.0.3
info:
  title: Form 602 - Lobbying Firm Activity Authorization
  description: |
    API for submitting Form 602, the Lobbying Firm Activity Authorization statement.
    This form is required for lobbying firms and their employers to disclose activities and authorizations.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/ActivityAuthorization/LobbyingFirm/{filerId}:
    post:
      summary: Submit Form 602
      description: Submit the Lobbying Firm Activity Authorization (Form 602).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingFirmActivityAuthorization'
      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingFirmActivityAuthorization:
      type: object
      properties:
        lobbyingType:
          type: string
          # enum:
          #   - Lobbyist Employer
          #   - Lobbying Coalition
        filerDetails:
          $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
        periodOfContract:
          type: string
          description: The period of contract term.
        electiveDate:
          type: string
          format: date
          description: The elective date.
        legislativeSession:
          type: string
          description: Legislative session year(s).
        lobbyingFirm:
          $ref: '#/components/schemas/LobbyingFirm'
        subcontractedClients:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractedClient'
          description: List of subcontracted clients.
        natureAndInterests:
          $ref: '#/components/schemas/NatureAndInterests'
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - filerDetails
        - lobbyingFirm
        - natureAndInterests
        - attestation

    LobbyingFirm:
      type: object
      properties:
        name:
          type: string
          description: Name of the lobbying firm authorized to act on behalf of the filer.
        businessAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
      required:
        - name
        - businessAddress

    SubcontractedClient:
      type: object
      properties:
        clientName:
          type: string
          description: Name of the subcontracted client.
      required:
         - clientName

    NatureAndInterests:
      type: array
      items:
        oneOf:
          - type: object
            title: Individual
            properties:
              individual:
                type: array
                items:
                  $ref: '#/components/schemas/Individual'
              industryGroupClassification:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryGroupClassification'

          - type: object
            title: BusinessEntity
            properties:
              businessEntity:
                type: array
                items:
                  $ref: '#/components/schemas/BusinessEntity'
              industryGroupClassification:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryGroupClassification'

          - type: object
            title: IndustryTradeOrProfessionalAssociation
            properties:
              industryTradeOrProfessionalAssociation:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryTradeOrProfessionalAssociation'
              industryGroupClassification:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryGroupClassification'

          - type: object
            title: Other
            properties:
              other:
                type: array
                items:
                  $ref: '#/components/schemas/Other'
              industryGroupClassification:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryGroupClassification'

    Individual:
      type: object
      properties:
        natureAndInterestEmployerName:
          type: string
        employerAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        businessActivity:
          type: string
          description: Description of business activity in which you or your employer are engaged.

    BusinessEntity:
      type: object
      properties:
        businessDescription:
          type: string
          description: Description of business activity in which engaged.

    IndustryTradeOrProfessionalAssociation:
      type: object
      properties:
        industryDescription:
          type: string
          description: Description of industry, trade or profession represented.
        industryPortion:
          type: string
          description: Specific description of any portion or faction of the industry, trade, or profession which the association exclusively or primarily represents.
        numberOfMembers:
          type: string

    Other:
      type: object
      properties:
        statementOfNatureAndPurposes:
          type: string
          description: Statement of nature and purposes.
        commonInterest:
          type: string
          description: Description of any trade, profession, or other group with a common economic interest which is principally represented or from which membership or financial support is principally derived.

    IndustryGroupClassification:
      type: object
      properties:
        industryGroupClassifications:
          type: string
          description: Industry group sub-categories classification and Industry group classification.
        industryGroupOtherText:
          type: string
          description: The description of the industry group when other is selected.
