using Microsoft.Extensions.Logging;

namespace SOS.CalAccess.Services.Common.Auditing;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The majority of application auditing will be managed using temporal tables in the CARS database, offering a reliable method for capturing and maintaining historical data over time. 
/// Temporal tables automatically populate a history table with valid from and to timestamps every time a record is inserted, updated, or deleted.
/// Being implemented at the database level also ensures that the events are captured regardless of how they are initiated, such as from within the app or as a scripted data fix.
/// </p>
/// <p>
/// These tables will be integrated into the Datawarehouse, enabling scalability and efficient reporting. 
/// System Administrators will have the capability to search, sort, and filter these logs through Power BI, taking advantage of its powerful filtering and visualization features 
/// for intuitive log analysis and monitoring. This design provides a structured, scalable, and user-friendly solution for comprehensive auditing.
/// </p>
/// <p>
/// The IAuditSvc provides methods to generate audit log records when an auditable event occurrs that needs to be captured outside of the auditing performed by use of temporal tables.
/// For example if it dettermined to be necessary to create an audit record when a user views a specific type of information such as full bank account numbers.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to a backend common service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to record auditable events in the database
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SS4 - Auditing & Logging</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// |  IAuditEventRepository         |  Create                        |     Insert an AuditEvent record to the database   |
#endregion
public interface IAuditSvc
{
    /// <summary>
    /// Record an auditable event, serializing the provided context to JSON
    /// </summary>
    /// <param name="action"></param>
    /// <param name="userId"></param>
    /// <param name="filerId"></param>
    /// <param name="context"></param>
    /// 
    /// \msc
    /// Actor, IAuditSvc, IAuditEventRepository;
    /// Actor => IAuditSvc [label="RecordEvent()"];
    /// IAuditSvc => IAuditEventRepository [label="Create()"];
    /// IAuditEventRepository => IAuditSvc [label="return"];
    /// IAuditSvc >> Actor [label="return"];
    /// \endmsc
    public void RecordEvent(EventId action, string userId, string filerId, object context);
}
