using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Represents a single object for complete Candidate Intention Statement
/// (form 501) for submission through the Efile API.
/// </summary>
public class CandidateIntentionStatementDto
{
    /// <summary>
    /// Information about the candidacy that this candidate is registering for
    /// </summary>
    public ElectionRace? Race { get; set; }

    /// <summary>
    /// Candidate and election info from the top of the form
    /// </summary>
    public CandidateIntentionStatement? Registration { get; set; }

    /// <summary>
    /// Api user id from config
    /// </summary>
    public long UserId { get; set; }

}
