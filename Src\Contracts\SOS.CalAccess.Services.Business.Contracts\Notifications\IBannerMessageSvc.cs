using Refit;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Services.Business.Notifications;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Service to support display of banner messages in CalAccess.  
/// Banner messages can be targeted to users based on the filer types that the user is associated to or displayed for all users. 
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Show banner messages (general updates, site announcements) for a user
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UN-01 Create System Generated User Notification</li>  
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service
/// </p>
/// | Service                   | Operation                   | Description                                |
/// | ------------------------- | --------------------------- | ------------------------------------------ |
/// | IBannerMessageRepository  | FindBannerMessagesByUserId | Retrieve Banner notifications for a User  |
#endregion

public interface IBannerMessageSvc
{
    /// <summary>
    /// Get banner messages for a user. 
    /// </summary>
    /// <paramref name="userId"/>
    /// <returns>Task<IEnumerable<BannerMessage>></returns>
    /// 
    /// \msc
    /// Actor, IBannerMessageSvc, IBannerMessageRepository;
    /// Actor => IBannerMessageSvc [label="GetBannerMessages() "];
    /// IBannerMessageSvc => IBannerMessageRepository [label="FindBannerMessagesByUserId()"];
    /// IBannerMessageRepository >> IBannerMessageSvc [label="\nreturn List<BannerMessage>"];
    /// IBannerMessageSvc >> Actor [label="\nreturn IEnumerable<BannerMessage>"];
    /// \endmsc
    [Get(GetBannerMessagesByUserIdPath)]
    public Task<IEnumerable<BannerMessage>> GetBannerMessagesByUserId(long userId);
    public const string GetBannerMessagesByUserIdPath = "/api/Banners/GetBannerMessagesByUserId/{userId}";
}
