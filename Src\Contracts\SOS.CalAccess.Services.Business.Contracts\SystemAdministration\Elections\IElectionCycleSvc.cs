
using SOS.CalAccess.Services.Business.SystemAdministration.Elections.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.Elections;
#region DesignNotes    
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for managing Election Cycle operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the election cycle data provided is valid, conforms to business rules, and the year is passed as a string for compatibility.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The primary function of this service is to facilitate creation, retrieval (by ID, year, and all), update, and deletion of election cycles.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA-01: Configure System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this interface.
/// </p>
/// | Service                         | Operation               | Description                                       |
/// |----------------------------------|--------------------------|---------------------------------------------------|
/// | IElectionCycleRepositorySvc     | Create                   | Persists a new Election Cycle entity.             |
/// | IElectionCycleRepositorySvc     | Update                   | Updates an existing Election Cycle entity.        |
/// | IElectionCycleRepositorySvc     | Delete                   | Deletes an Election Cycle by ID.                  |
/// | IElectionCycleRepositorySvc     | GetById                  | Retrieves a specific Election Cycle by ID.        |
/// | IElectionCycleRepositorySvc     | GetAll                   | Retrieves all Election Cycle entities.            |
/// | IElectionCycleRepositorySvc     | GetElectionByYear        | Retrieves an Election Cycle by the specified year.|
#endregion
public interface IElectionCycleSvc
{
    /// <summary>
    /// Create an Election Cycle
    /// </summary>
    /// <param name="electionCycle">Election Cycle Dto</param>
    /// <returns>Election Cycle Dto</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="CreateElectionCycle()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="Create()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn ElectionCycle"];
    /// IElectionCycleSvc >> Actor [label="\nreturn ElectionCycleDto"];
    /// \endmsc
    Task<ElectionCycleDto> CreateElectionCycle(ElectionCycleDto electionCycle);


    /// <summary>
    /// Update an Election Cycle
    /// </summary>
    /// <param name="electionCycle">Election Cycle Dto</param>
    /// <returns>Election Cycle Dto</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="UpdateElectionCycle()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="Update()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn ElectionCycle"];
    /// IElectionCycleSvc >> Actor [label="\nreturn ElectionCycleDto"];
    /// \endmsc
    Task<ElectionCycleDto> UpdateElectionCycle(ElectionCycleDto electionCycle);


    /// <summary>
    /// Delete an Election Cycle
    /// </summary>
    /// <param name="electionCycleId">ID of the Election Cycle</param>
    /// <returns>Boolean result of deletion</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="DeleteElectionCycle()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="Delete()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn bool"];
    /// IElectionCycleSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    Task<bool> DeleteElectionCycle(long electionCycleId);


    /// <summary>
    /// Get an Election Cycle by ID
    /// </summary>
    /// <param name="electionCycleId">ID of the Election Cycle</param>
    /// <returns>Election Cycle Dto</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="GetElectionCycleByID()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="GetById()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn ElectionCycle"];
    /// IElectionCycleSvc >> Actor [label="\nreturn ElectionCycleDto"];
    /// \endmsc
    Task<ElectionCycleDto> GetElectionCycleByID(long electionCycleId);

    /// <summary>
    /// Get all Election Cycles
    /// </summary>
    /// <returns>List of Election Cycle Dto</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="GetAllElectionCycles()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="GetAll()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn List<ElectionCycle>"];
    /// IElectionCycleSvc >> Actor [label="\nreturn List<ElectionCycleDto>"];
    /// \endmsc
    Task<List<ElectionCycleDto>> GetAllElectionCycles();


    /// <summary>
    /// Get an Election Cycle by year
    /// </summary>
    /// <param name="year">Year (type string)</param>
    /// <returns>Election Cycle Dto</returns>
    /// \msc
    /// Actor, IElectionCycleSvc [label="IElectionCycleSvc"], IElectionCycleRepositorySvc;
    /// Actor => IElectionCycleSvc [label="GetElectionByYear()"];
    /// IElectionCycleSvc => IElectionCycleRepositorySvc [label="GetElectionByYear()"];
    /// IElectionCycleRepositorySvc >> IElectionCycleSvc [label="\nreturn ElectionCycle"];
    /// IElectionCycleSvc >> Actor [label="\nreturn ElectionCycleDto"];
    /// \endmsc
    Task<ElectionCycleDto> GetElectionByYear(string year);

}
