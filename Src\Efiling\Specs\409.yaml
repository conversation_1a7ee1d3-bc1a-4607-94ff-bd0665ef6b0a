openapi: 3.0.3
info:
  title: Limited Liability Company Statement of Members
  version: 0.1.0
  description: API schema for submitting the Limited Liability Company Statement of Members (Form 409).
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/MemberStatement/LLC/{filerId}:
    post:
      summary: Submit Statement of Members
      description: Submit a Statement of Members for Limited Liability Companies (LLC).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLC'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LLC:
      type: object
      properties:
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        llcDateQualificationThresholdMet:
          type: string
          format: date
          description: The date this LLC met the qualification threshold to become an LLC
        llcName:
          type: string
          description: Legal name of the Limited Liability Company (LLC)
        llcStreetAddress:  
          $ref: './common-schemas.yaml#/components/schemas/Address'
        llcMailingAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        llcResponsibleOfficer:
          type: object
          properties:
            name:
              type: string
              description: Name of the responsible or principal officer.
            phoneNumber:
              type: string
              description: Phone number of the responsible officer as digits only.
              pattern: '^\d+$'
              maxLength: 40
            email:
              type: string
              format: email
              description: Email address of the responsible officer
          required:
            - name
        members:
          type: array
          items:
            type: object
            properties:
              fullName:
                type: string
                description: Full legal name of the member
              tenPercentOrGreater:
                type: boolean
                description: >
                  At least one of this or capitalContributionsOver10K must be 
                  true for the member to qualify.
              capitalContributionOver10K:
                type: boolean
                description: >
                  At least one of this or tenPercentOrGreater must be true for 
                  the member to qualify.
              datesOfCapitalContributions:
                type: array
                description: >
                    For those members qualifying because of meeting the $10,000 
                    capital contribution threshold, enter the dates of each 
                    capital contribution made since qualification and in the 
                    preceding 12 months.
                items:
                  type: string
                  format: date               
              cumulativeCapitalContribution:
                type: number
                format: float
                description: Cumulative capital contributions of the member
              percentageOwnership:
                type: number
                format: float
                description: Percentage ownership interest in the LLC (0 - 100)
                minimum: 0
                maximum: 100
                multipleOf: 0.1
            required:
              - fullName
            anyOf:
              - required: [tenPercentOrGreater]
                properties:
                  tenPercentOrGreater:
                    enum: [true]
              - required: [capitalContributionOver10K]
                properties:
                  capitalContributionOver10K:
                    enum: [true]
        memberLLCs:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                description: Name of the member LLC
              tenPercentOrGreater:
                type: boolean
                description: >
                  At least one of this or capitalContributionsOver10K must be 
                  true for the member to qualify.
              capitalContributionOver10K:
                type: boolean
                description: >
                  At least one of this or tenPercentOrGreater must be true for 
                  the member to qualify.
              datesOfCapitalContributions:
                type: array
                description: >
                    For those members qualifying because of meeting the $10,000 
                    capital contribution threshold, enter the dates of each 
                    capital contribution made since qualification and in the 
                    preceding 12 months.
                items:
                  type: string
                  format: date               
              cumulativeCapitalContribution:
                type: number
                format: float
                description: Cumulative capital contributions of the member
              percentageOwnership:
                type: number
                format: float
                description: Percentage ownership interest in the LLC (0 - 100)
                minimum: 0
                maximum: 100
                multipleOf: 0.1
              members:
                type: array
                items:
                  type: string
                  description: Full name of the member of the member LLC 
            required:
              - name
            anyOf:
              - required: [tenPercentOrGreater]
                properties:
                  tenPercentOrGreater:
                    enum: [true]
              - required: [capitalContributionOver10K]
                properties:
                  capitalContributionOver10K:
                    enum: [true]
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - llcName
        - llcStreetAddress
        - llcResponsibleOfficer
        - attestation
