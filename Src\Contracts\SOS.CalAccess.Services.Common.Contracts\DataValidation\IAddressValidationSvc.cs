using Refit;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Services.Common.DataValidation;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Address Validation Service is responsible for validating input address and return true if address is valid and return false if invalid using SOAP service
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The screen or service invoking this layer is developed.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to call ValidateAddress method in SOAP service
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS12 Create service to validate address using Accumail</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | Accumail SOAP api              | HTTP GET                            |  Validate Address            |
#endregion
public interface IAddressValidationSvc
{
    /// <summary>
    /// Validate address and return true if address is valid and return false if invalid
    /// </summary>
    /// <param name="addressDto">Address DTO Object</param>
    /// <param name="callDecisions">Flag to validate the object against decisions</param>
    /// <returns></returns>
    [Post("/" + ValidateAddressPath)]
    Task<AddressValidationResponse> ValidateAddress(AddressValidationDto addressDto, bool callDecisions = false);

    const string ValidateAddressPath = "api/AddressValidation/Validate";

}
