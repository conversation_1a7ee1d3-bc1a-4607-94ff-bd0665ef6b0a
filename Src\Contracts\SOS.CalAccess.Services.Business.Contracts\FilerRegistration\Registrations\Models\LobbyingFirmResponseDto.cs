using SOS.CalAccess.Models.FilerRegistration.Registrations;


namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Response DTO for Lobbying Firm registrations.
/// </summary>
public class LobbyingFirmResponseDto
{
    public LobbyingFirmResponseDto()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="LobbyingFirmResponseDto"/> class.
    /// </summary>
    /// <param name="lobbyingFirm">The lobbying firm registration entity.</param>
    public LobbyingFirmResponseDto(LobbyingFirm lobbyingFirm)
    {
        Id = lobbyingFirm.Id;
        Name = lobbyingFirm.Name;
        FilerId = lobbyingFirm.FilerId;
        AddressListId = lobbyingFirm.AddressListId.GetValueOrDefault();
        PhoneNumberListId = lobbyingFirm.PhoneNumberListId.GetValueOrDefault();

        if (lobbyingFirm.AddressList?.Addresses != null)
        {
            Addresses = [.. lobbyingFirm.AddressList.Addresses.Select(a => new AddressDto(a))];
        }
    }

    /// <summary>
    /// Gets the unique identifier.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the firm name.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets the filer identifier.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets the address list ID.
    /// </summary>
    public long AddressListId { get; set; }

    /// <summary>
    /// Gets the addresses associated with this registration.
    /// </summary>
    public List<AddressDto> Addresses { get; set; } = new();

    /// <summary>
    /// Gets the phone number list ID.
    /// </summary>
    public long PhoneNumberListId { get; set; }
}
