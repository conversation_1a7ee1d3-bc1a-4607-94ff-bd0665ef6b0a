

namespace SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits.Models;
/// <summary>
/// Represents the request to add or configure expenditure limits for a specific elected office and election cycle.
/// </summary>
public class ExpenditureLimitDto
{
    /// <summary>
    /// The elected office for which the expenditure limit is being configured (e.g., "Governor", "Secretary of State").
    /// </summary>
    public long ElectedOffice { get; set; }

    /// <summary>
    /// The election cycle for the expenditure limit (e.g., "2026 General", "2024 Primary").
    /// </summary>
    public long ElectionCycle { get; set; }

    /// <summary>
    /// The expenditure limit applicable to the primary election.
    /// </summary>
    public decimal PrimaryElectionLimit { get; set; }

    /// <summary>
    /// The expenditure limit applicable to the general election.
    /// </summary>
    public decimal GeneralElectionLimit { get; set; }

    /// <summary>
    /// The expenditure limit applicable to a special election.
    /// </summary>
    public decimal SpecialElectionLimit { get; set; }

    /// <summary>
    /// The expenditure limit applicable to a special runoff election.
    /// </summary>
    public decimal SpecialRunoffElectionLimit { get; set; }
}

