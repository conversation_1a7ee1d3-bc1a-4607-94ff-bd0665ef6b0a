﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="1.20.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.1.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="1.2.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.OpenApi" Version="1.5.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="1.16.4" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.21.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.1.0" />
    
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSwag.Annotations" Version="14.2.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Contracts\SOS.CalAccess.Services.Common.Contracts\SOS.CalAccess.Services.Common.Contracts.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Data.EntityFramework\SOS.CalAccess.Data.EntityFramework.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Models\SOS.CalAccess.Models.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Services.Business\SOS.CalAccess.Services.Business.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Services.Common\SOS.CalAccess.Services.Common.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Services.WebApi\SOS.CalAccess.Services.WebApi.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>