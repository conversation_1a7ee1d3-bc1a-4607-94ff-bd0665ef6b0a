namespace SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits.Models;
/// <summary>
/// Represents the contribution limit for a specific donor type.
/// </summary>
public class DonorTypeAmountDto
{
    /// <summary>
    /// The type of donor (e.g., "Individual", "Small Contributor Committee").
    /// from a reference table of Donor Type
    /// </summary>
    public long DonorType { get; set; }

    /// <summary>
    /// The monetary contribution limit for the specified donor type.
    /// </summary>
    public decimal LimitAmount { get; set; }
}
