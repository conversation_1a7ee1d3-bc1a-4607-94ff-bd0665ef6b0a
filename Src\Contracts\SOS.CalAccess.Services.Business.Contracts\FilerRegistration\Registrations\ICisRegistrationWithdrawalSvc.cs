using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
/// <summary>
/// Service interface for managing Candidate Intention Statement (CIS) registration withdrawal process.
/// Provides methods for creating, retrieving, and canceling a CIS withdrawal.
/// </summary>
public interface ICisRegistrationWithdrawalSvc
{
    /// <summary>
    /// Creates a new CIS registration withdrawal.
    /// The associated registration must have a status of 'Accepted'.
    /// </summary>
    /// <param name="request">The request object containing withdrawal details.</param>
    [Post("/" + CreateWithdrawalPath)]
    Task CreateCisWithdrawal(long registrationId);
    const string CreateWithdrawalPath = "api/CisRegistrationWithdrawal/{registrationId}";

    /// <summary>
    /// Retrieves a CIS registration withdrawal by its registration ID.
    /// </summary>
    /// <param name="registrationId">The unique identifier of the withdrawal registration.</param>
    /// <returns>Returns the CIS withdrawal details.</returns>
    [Get("/" + GetWithdrawalPath)]
    Task<CisRegistrationWithdrawalDto> GetCisWithdrawal(long registrationId);
    const string GetWithdrawalPath = "api/CisRegistrationWithdrawal/{registrationId}";

    /// <summary>
    /// Cancels (soft deletes) a CIS registration withdrawal.
    /// Allowed only when the record is in 'Draft' or 'Pending' status.
    /// </summary>
    /// <param name="registrationId">The unique identifier of the withdrawal registration to cancel.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    [Delete("/" + CancelWithdrawalPath)]
    Task CancelCisWithdrawal(long registrationId);
    const string CancelWithdrawalPath = "api/CisRegistrationWithdrawal/{registrationId}";

    /// <summary>
    /// Submits an attestation to withdraw a Candidate Intention Statement (CIS) registration.
    /// </summary>
    /// <param name="registrationId">The ID of the CIS registration being withdrawn.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Put("/" + AttestationCisWithdrawalPath)]
    Task<RegistrationResponseDto> AttestationCisWithdrawal(CisRegistrationAttestationWithdrawalDto cisRegistrationAttestationWithdrawalDto);
    const string AttestationCisWithdrawalPath = "api/CisRegistrationWithdrawal/attestation";

}
