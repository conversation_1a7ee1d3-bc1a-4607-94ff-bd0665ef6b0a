using System.Collections.ObjectModel;
using SOS.CalAccess.Models.Email;
using SOS.CalAccess.Services.Common.Email.Model;

namespace SOS.CalAccess.Services.Common.Email;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines methods for sending messages using the SendGrid API and tracking the status of sent messages.
/// </p>
/// <p>
/// This service allows sending of both simple plain text email messages or using templates generated within Sendgrid for richer html based email messages.
/// <br/>https://www.twilio.com/docs/sendgrid/ui/sending-email/how-to-send-an-email-with-dynamic-templates
/// <br/>Sendgrid provides rich editors for managing html email templates (https://www.twilio.com/docs/sendgrid/ui/sending-email/editor).
/// </p>
/// <p>
/// Status updates will be received from a SendGrid Event Webhook.  To use event webhooks the CARS solution system exposes a URL that accepts a POST request from SendGrid. 
/// SendGrid then sends email event data as Send<PERSON><PERSON> processes each change to a message by calling the URL. This means status updates are generated in near real-time.
/// The URL SendGrid uses for the webhook events is configured on the SendGrid account.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service implementation handles all necessary configuration and authorization required to send a request to the SendGrid API
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Sends an email message to a user
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS5- Notifification- Email(SMS)  </li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IEmailMessageRepository        | Create                         | Create an email message record |
/// | IEmailMessageRepository        | FindByProviderId               | Find email by provider id      |
/// | IEmailMessageRepository        | Update                         | Update an email message record |
/// | ISendGridClient                | SendEmailAsync                 | Send an email |
#endregion
public interface IEmailSvc
{
    /// <summary>
    /// Send an email with the provided subject and a plain text body containing the message to the specified recipients
    /// </summary>
    /// <param name="emailMessageRequest"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IEmailSvc, IEmailMessageRepository, ISendGridClient;
    /// Actor => IEmailSvc [label="SendPlainEmail()"];
    /// IEmailSvc => IEmailMessageRepository [label="Create()"];
    /// IEmailMessageRepository >> IEmailSvc [label="\nreturn "];
    /// IEmailSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public Task SendPlainEmail(EmailMessageRequest emailMessageRequest);

    /// <summary>
    /// Send an email using an HTML template defined in SendGrid for the body of the message to the specified recipients
    /// </summary>
    /// <param name="emailTemplateRequest"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IEmailSvc, IEmailMessageRepository, ISendGridClient;
    /// Actor => IEmailSvc [label="SendTemplatedEmail()"];
    /// IEmailSvc => IEmailMessageRepository [label="Create()"];
    /// IEmailMessageRepository >> IEmailSvc [label="\nreturn "];
    /// IEmailSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public Task SendTemplatedEmail(EmailTemplateRequest emailTemplateRequest);

    /// <summary>
    /// Update the status of the email based on data recieved from SendGrid Event Webhook
    /// </summary>
    /// <param name="providerId"></param>
    /// <param name="status"></param>
    /// <param name="issueClassification"></param>
    /// <param name="errorResponse"></param>
    /// <param name="attemptCount"></param>
    /// <returns></returns>
    /// 
    /// \msc
    /// Actor, IEmailSvc, IEmailMessageRepository;
    /// Actor => IEmailSvc [label="UpdateEmailStatus()"];
    /// IEmailSvc => IEmailMessageRepository [label="FindByProviderId()"];
    /// IEmailMessageRepository >> IEmailSvc [label="\nreturn "];
    /// IEmailSvc => IEmailMessageRepository [label="Update()"];
    /// IEmailMessageRepository >> IEmailSvc [label="\nreturn "];
    /// IEmailSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public Task UpdateEmailStatus(string providerId, string status, string issueClassification, string errorResponse, string attemptCount, string smtpId, string sendGridEventId, DateTime dateUpdated);
    /// <summary>
    /// Upadate status of Email Message after sent to Send Grid
    /// </summary>
    /// <param name="updateEmailRequest"></param>
    /// <returns></returns>
    Task UpdateEmailStatus(UpdateEmailRequest updateEmailRequest);
}
