using Refit;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IdentityProviderSynchronizationSvc provides methods to update the CARS database when a user record is created or updated in Entra ID.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend business application layer service invoked by the web API layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Authorization is performed at the Web API services layer.
/// </p>
/// <p>
/// The Identity Provider handles validation of provided email address, required fields, uniqueness constraints, and password strength rules
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to synchronize information from the solutions identity provider to the solutions DB for use in displaying the information through the various portals.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>UA-02: Create a User Account</li>
/// <li>UA-03: Update a User Account</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IUserRepository                | Create                         | Insert User Record           |
/// | IUserRepository                | Update                         | Update User Record           |
/// | IWorkflowSvc                   | TriggerWorkflow                | Workflow                     |
#endregion
public interface IIdentityProviderSynchronizationSvc
{
    /// <summary>
    /// CreateUser is called by a webhook to create a user record in CalAccess after a new user is successfully added to the solutions identity provider (Entra Id)
    /// Triggers a workflow in Decisions upon user creation
    /// </summary>
    /// <param name="request"></param>
    ///
    /// \msc
    /// Actor, IIdentityProviderSynchronizationSvc, IDecisionsSvc, IUserRepository;
    /// Actor => IIdentityProviderSynchronizationSvc [label="CreateUser()"];
    /// IIdentityProviderSynchronizationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IIdentityProviderSynchronizationSvc [label="return"];
    /// IIdentityProviderSynchronizationSvc => IUserRepository [label="Create()"];
    /// IUserRepository >> IIdentityProviderSynchronizationSvc [label="return"];
    /// IIdentityProviderSynchronizationSvc >> Actor [label="return"];
    /// \endmsc
    [Post("/" + CreateNewUserPath)]
    public Task CreateNewUser(SyncUserRequest syncUserRequest);
    const string CreateNewUserPath = "api/IdentityProvider/User/CreateUser";

    /// <summary>
    /// UpdateUser is called by a webhook to update a user record in CalAccess after a user is successfully modified in the solutions identity provider (Entra Id)
    /// </summary>
    /// <param name="request"></param>
    ///
    /// \msc
    /// Actor, IIdentityProviderSynchronizationSvc, IDecisionsSvc, IUserRepository;
    /// Actor => IIdentityProviderSynchronizationSvc [label="UpdateUser()"];
    /// IIdentityProviderSynchronizationSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IIdentityProviderSynchronizationSvc [label="return"];
    /// IIdentityProviderSynchronizationSvc => IUserRepository [label="Update()"];
    /// IUserRepository >> IIdentityProviderSynchronizationSvc [label="return"];
    /// IIdentityProviderSynchronizationSvc >> Actor [label="return"];
    /// \endmsc
    public Task UpdateUser(SyncUserRequest request);

    /// <summary>
    /// Checks if the user with the given entra Id exists
    /// </summary>
    /// <param name="entraOid"></param>
    /// <returns></returns>
    [Get("/" + GetUserByEntraOidPath)]
    Task<SyncUserRequest?> GetUserByEntraOid(string entraOid);
    const string GetUserByEntraOidPath = "api/IdentityProvider/User/GetUserByEntraOid";
}
