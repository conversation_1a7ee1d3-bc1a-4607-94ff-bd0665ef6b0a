openapi: 3.0.3
info:
  title: FPPC Form 461 - <PERSON> and Independent Expenditure Committee Campaign Statement API
  description: API for managing FPPC Form 461 submissions and related data for major donors and independent expenditure committees.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/IndependentExpenditureCommittee/{filerId}:
    post:
      summary: Submit a new Form 461 - Major Donor and Independent Expenditure Committee Campaign Statement
      description: Submit a new campaign statement for major donors or independent expenditure committees.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Form461Submission'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

  /Campaign/Disclosure/MajorDonor:
    post:
      summary: Submit a new Form 461 - Major Donor and Independent Expenditure Committee Campaign Statement
      description: Submit a new campaign statement for major donors or independent expenditure committees.

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Form461Submission'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'


components:
  schemas:
    Form461Submission:
      type: object
      properties:
        filer:
          type: object
          description: "Either 'filerId' or 'natureAndInterestsOfFiler' is required, but not both."
          oneOf:
            - type: object
              properties:
                filerId:
                  type: string
              required:
                - filerId
            - $ref: '#/components/schemas/NatureAndInterestsOfFiler'
        summary:
          type: object
          properties:
            totalExpenditures100orMore:
              type: number
              description: Total expenditures and contributions (including loans) of $100 or more made during this period.
            totalUnitemizedExpendituresUnder100:
              type: number
              description: Total unitemized expenditures and contributions under $100 made during this period.
            totalExpendituresThisPeriod:
              type: number
              description: Total expenditures and contributions made during this period (sum of itemized and unitemized).
            totalExpendituresPriorStatements:
              type: number
              description: Total expenditures and contributions made in prior statements.
            totalExpendituresSinceJanuary1:
              type: number
              description: Total expenditures and contributions made since January 1 of the current calendar year.
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
        contributions:
          type: array
          description: Itemized contributions made during the reporting period.
          items:
            $ref: '#/components/schemas/ContributionDetails'
        statementPeriod:
          type: object
          description: The period covered by the statement being submitted.
          properties:
            startDate:
              type: string
              format: date
              description: Start date of the statement period.
            endDate:
              type: string
              format: date
              description: End date of the statement period.
            submittedDate:
              type: string
              format: date
              description: Form submitted date.
            electionDate:
              type: string
              format: date
              description: Date of election.
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
      required:
        - filer

    ContributionDetails:
      type: object
      properties:
        transactionDate:
          type: string
          format: date
          description: Date of the contribution or payment.
        name:
          type: string
          maxLength: 32
          # enum:
          #   - Monetary Contribution
          #   - Loan
          #   - Non-Monetary Contribution
          #   - Independent Expenditure
          description: Type of payment made.
        payee:
          type: object
          properties:
            idNumber:
              type: string
              description: Unique identifier number for the payee or recipient.
            firstName:
              type: string
              description: First name of the payee or recipient.
            lastName:
              type: string
              description: Last name of the payee or recipient.
            address:
              $ref: './common-schemas.yaml#/components/schemas/Address'
        candidate_office_measure_jurisdiction_committee:
          type: object
          properties:
            Name:
              type: string
              description: Name of the candidate, office, measure, or committee.
            electionOfficeSought:
              $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
            committee:
              type: string
              description: Committee name.
            code:
              type: string
              description: Ballot measure code.
            position:
              type: string
              enum:
                - Support
                - Oppose
              description: Whether the payment supports or opposes the candidate or measure.
        cumulativeAmount:
          type: number
          description: Cumulative amount contributed to this candidate, measure, or committee during the calendar year.
        amountThisPeriod:
          type: number
          description: Amount contributed during this period.
        paymentDescription:
          type: string
          description: Detailed description of the payment.
        subTotalCumulative:
          type: number
          description: Cumulative subtotal contributed during this period.
        subTotalThisPeriod:
          type: number
          description: Subtotal amount contributed during this period.
    NatureAndInterestsOfFiler:
          type: object
          properties:
            individualFiler:
              type: object
              description: Details if the filer is an individual.
              properties:
                employerName:
                  type: string
                  description: Name of the employer.
                employerAddress:
                  $ref: './common-schemas.yaml#/components/schemas/Address'
                businessInterests:
                  type: string
                  description: Nature of the filer's business interests.
            businessEntityFiler:
              type: object
              description: Details if the filer is a business entity.
              properties:
                businessActivity:
                  type: string
                  description: Description of the business activity in which the entity is engaged.
            associationFiler:
              type: object
              description: Details if the filer is an association.
              properties:
                interests:
                  type: string
                  description: A specific description of the association's interests.
            otherEntityFiler:
              type: object
              description: Details if the filer is another type of entity.
              properties:
                commonEconomicInterest:
                  type: string
                  description: Description of the common economic interests of the group or entity.
