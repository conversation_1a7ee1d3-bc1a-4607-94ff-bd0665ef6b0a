using Refit;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Business application layer service invoked by the Web API layer
/// </p>
/// <h4>Assumptions</h4>
/// <h4>Business Function</h4>
/// <p>
/// Allow user to save notification Preference in CARS 
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>UA-03: Update a User Account</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service.
/// </p>
/// | Service                              | Operation  | Description                          |
/// | ------------------------------------ | ---------- | ------------------------------------ |
/// | IUserNotificationPrefenceRepository  | Create     | Insert a UserNotificationPrefence    |
/// | IUserNotificationPrefenceRepository  | GetById    | Retrieve UserNotificationPrefences   |
/// | IUserNotificationPrefenceRepository  | Update     | Update a UserNotificationPrefence    |
/// | IUserNotificationPrefenceRepository  | Delete     | Delete a UserNotificationPrefence    |
#endregion

public interface IUserNotificationPreferenceSvc
{
    /// <summary>
    /// Create a UserNotificationPreference
    /// </summary>
    /// <param name="preference"></param>
    /// <returns>Task</returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IUserNotificationPrefenceRepository [label="IUserNotification\nPrefenceRepository"];
    /// Actor => IUserNotificationPreferenceSvc [label="CreateUserNotificationPreference()"];
    /// IUserNotificationPreferenceSvc => IUserNotificationPrefenceRepository [label="CreateUserNotificationPreference()"];
    /// IUserNotificationPrefenceRepository >> IUserNotificationPreferenceSvc [label="\nreturn "];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + CreateUserNotificationPreferencePath)]
    Task CreateUserNotificationPreference([Body] UserNotificationPreference preference);
    const string CreateUserNotificationPreferencePath = "api/NotificationPreferences/add";

    /// <summary>
    /// Method to fetch all preferences for each filer a user is associated with
    /// </summary>
    /// <param name="userId"></param>
    /// <returns>Task<UserNotificationPreference></returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IUserNotificationPrefenceRepository [label="IUserNotification\nPrefenceRepository"];
    /// Actor => IUserNotificationPreferenceSvc [label="ViewUserNotificationPreference()"];
    /// IUserNotificationPreferenceSvc => IUserNotificationPrefenceRepository [label="ViewUserNotificationPreference()"];
    /// IUserNotificationPrefenceRepository >> IUserNotificationPreferenceSvc [label="\nreturn UserNotificationPreference"];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn List<UserNotificationPreference>>"];
    /// \endmsc
    [Get("/" + ViewUserNotificationPreferencesPath)]
    Task<List<UserNotificationPreference>> ViewUserNotificationPreferences(long userId);
    const string ViewUserNotificationPreferencesPath = "api/NotificationPreferences/{userId}";

    /// <summary>
    /// Update notification preferences
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Task</returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IUserNotificationPrefenceRepository [label="IUserNotification\nPrefenceRepository"];
    /// Actor => IUserNotificationPreferenceSvc [label="UpdateUserNotificationPreference()"];
    /// IUserNotificationPreferenceSvc => IUserNotificationPrefenceRepository [label="UpdateUserNotificationPreference()"];
    /// IUserNotificationPrefenceRepository >> IUserNotificationPreferenceSvc [label="\nreturn "];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + UpdateUserNotificationPreferencesPath)]
    Task UpdateUserNotificationPreferences([Body] List<UserNotificationPreference> preferences);
    const string UpdateUserNotificationPreferencesPath = "api/NotificationPreferences/update";

    /// <summary>
    /// Remove a notification preference 
    /// </summary>
    /// <param name="preferenceId"></param>
    /// <returns>Task</returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IUserNotificationPrefenceRepository [label="IUserNotification\nPrefenceRepository"];
    /// Actor => IUserNotificationPreferenceSvc [label="RemoveUserNotificationPreference()"];
    /// IUserNotificationPreferenceSvc => IUserNotificationPrefenceRepository [label="RemoveUserNotificationPreference()"];
    /// IUserNotificationPrefenceRepository >> IUserNotificationPreferenceSvc [label="\nreturn "];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Delete("/" + RemoveUserNotificationPreferencePath)]
    Task RemoveUserNotificationPreference(long preferenceId);
    const string RemoveUserNotificationPreferencePath = "api/NotificationPreferences/delete/{preferenceId}";

    /// <summary>
    /// Method to fetch all filers and roles for a user
    /// </summary>
    /// <param name="userId"></param>
    /// <returns>Task<List<FilerUserRoleDto>></returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IFilerUserRepository [label="IFilerUserRepository"];
    /// Actor => IUserNotificationPreferenceSvc [label="GetFilersAndRolesForUser()"];
    /// IUserNotificationPreferenceSvc => IFilerUserRepository [label="GetFilerUsersByUserId()"];
    /// IFilerUserRepository >> IUserNotificationPreferenceSvc [label="\nreturn List<FilerUser>"];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn List<FilerUserRoleDto>"];
    /// \endmsc
    [Get("/" + GetFilersForUserPath)]
    Task<List<FilerUserRoleDto>> GetFilersAndRolesForUser(long userId);
    const string GetFilersForUserPath = "api/NotificationPreferences/{userId}/filers";

    /// <summary>
    /// Fetch the Id of the user requesting notification preferences
    /// </summary>
    /// <returns>Task<long></returns>
    /// 
    /// \msc
    /// Actor, IUserNotificationPreferenceSvc [label="IUserNotification\nPreferenceSvc"], IAuthorizationSvc [label="IAuthorizationSvc"];
    /// Actor => IUserNotificationPreferenceSvc [label="GetInitiatingUserId()"];
    /// IUserNotificationPreferenceSvc => IAuthorizationSvc [label="GetInitiatingUserId()"];
    /// IAuthorizationSvc >> IUserNotificationPreferenceSvc [label="\nreturn UserId"];
    /// IUserNotificationPreferenceSvc >> Actor [label="\nreturn UserId"];
    /// \endmsc
    [Get("/" + GetInitiatingUserIdPath)]
    Task<long?> GetInitiatingUserId();
    const string GetInitiatingUserIdPath = "api/NotificationPreferences/GetInitiatingUserId";
}
