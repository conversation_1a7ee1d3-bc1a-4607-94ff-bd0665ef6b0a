using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for response body of SmoRegistrationModel.SearchCommitteeByNameId API.
/// </summary>
public class CommitteeSearchResultDto
{
    /// <summary>
    /// Committee Id
    /// </summary>
    [Required]
    public required long Id { get; set; }

    /// <summary>
    /// Gets or sets the Committee name.
    /// </summary>
    [Required]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or set the Committee address from the registration
    /// </summary>
    [Required]
    public required IEnumerable<AddressDto> Addresses { get; set; }

}
