namespace SOS.CalAccess.Services.Business.SystemAdministration.ReferenceData.Models;
/// <summary>
/// Represents a system parameter and its associated values used for configuration or lookup purposes.
/// </summary>
public class SystemParameterValueDto
{
    /// <summary>
    /// The unique identifier for the system parameter value entry.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// The short name or code of the parameter (used as a key or identifier).
    /// </summary>
    public string ParameterName { get; set; }

    /// <summary>
    /// A human-readable description of the parameter and its purpose.
    /// </summary>
    public string ParameterDescription { get; set; }

    /// <summary>
    /// The first value associated with the parameter .
    /// </summary>
    public string Value1 { get; set; }

    /// <summary>
    /// The second value associated with the parameter (used optionally for additional values e.g. of date range values ).
    /// </summary>
    public string Value2 { get; set; }


    /// <summary>
    /// The last modified date time of the parameter.
    /// </summary>
    public DateTime ModifiedDate { get; set; }


    /// <summary>
    /// Indicates whether the parameter value entry is marked as deleted (used for soft-delete scenarios).
    /// </summary>
    public bool IsDeleted { get; set; }
}
