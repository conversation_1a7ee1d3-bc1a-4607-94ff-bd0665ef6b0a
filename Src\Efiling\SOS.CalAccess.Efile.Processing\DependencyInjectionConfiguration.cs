using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Common.BusinessRules;
using globalDependencyInjection = SOS.CalAccess.Services.WebApi.DependencyInjectionConfiguration;

namespace SOS.CalAccess.Efile.Processing;

/// <summary>
/// Helper methods used to set up dependency injection for this project.
/// </summary>
[ExcludeFromCodeCoverage]
internal sealed class DependencyInjectionConfiguration
{
    #region Properties

    /// <summary>
    /// Settings pulled from the JSON file
    /// </summary>
    private readonly IConfiguration _configuration;

    /// <summary>
    /// Common helper methods used to set up dependency injection for both Efile functions.
    /// </summary>
    private readonly CommonDependencyInjection _commonDI;

    /// <summary>
    /// Configuration keys that must exist and be non-empty at runtime.
    /// If any are missing, dependency injection and application startup will fail.
    /// </summary>
    private static readonly string[] _requiredSettings =
    {
        "Decisions:Host",
        "Decisions:SessionId"
    };

    /// <summary>
    /// The service collection used to register all D.I. services.
    /// </summary>
    private readonly IServiceCollection _services;

    #endregion

    /// <summary>
    /// Save configuration locally, then verify that all required settings are present.
    /// </summary>
    /// <param name="services"></param>
    /// <exception cref="ArgumentNullException">If "services" is null</exception>
    internal DependencyInjectionConfiguration(IServiceCollection services)
    {
        _services = services ?? throw new ArgumentNullException(nameof(services));
        string environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Development";
        bool isDevelopmentEnvironment = environment.Equals("Development", StringComparison.OrdinalIgnoreCase);
        string settingsFilename = isDevelopmentEnvironment ? "local.settings.json" : "settings.json";

        _configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(settingsFilename)
            .Build();

        ValidateSettings();
        _commonDI = new CommonDependencyInjection(_configuration, isDevelopmentEnvironment);
    }

    /// <summary>
    /// Setup and configure dependency injection for the data layer repositories.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddRepositories()
    {
        // Common dependency injection for Repositories, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddRepositories(_services);

        // Add Repositories Services Dependencies those are need both Efile Validateion and Processing functions.
        _commonDI.AddRepositories(_services);
    }

    /// <summary>
    /// Add common services dependencies.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddCommonServices()
    {
        // Common dependency injection for Common Services, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddCommonServices(_services, _configuration);

        // Add Common Services Dependencies those are need both Efile Validateion and Processing functions.
        _commonDI.AddCommonServices(_services);

        // Add Efile Processing Common Services Dependencies.
        _services.AddSingleton(new DecisionsSvcOptions(
            _configuration["Decisions:Host"],
            _configuration["Decisions:SessionId"]
        ));
    }

    /// <summary>
    /// Add business service dependencies.
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// </summary>
    internal void AddBusinessServices()
    {
        // Common dependency injection for business services, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddBusinessServices(_services);

        // Add Business Services Dependencies those are need both Efile Validateion and Processing functions.
        CommonDependencyInjection.AddBusinessServices(_services);

        // Add Efile Processing Business Services Dependencies.
    }

    /// <summary>
    /// Setup of Services provided by Maplight Accelerator project
    /// Before adding the dependencies in this method, please ensure they are not already registered in Common DI and API project.
    /// If they are, you should not register them again here to avoid duplication and potential conflicts.
    /// These classes should eventually be able to be removed when functionality is fully implemented in the CARS application.
    /// </summary>
    internal void AddMaplightAcceleratorClasses()
    {
        // Common dependency injection for Maplight Accelerator Classes, used for both Efile(Validation and Processing) and Api
        globalDependencyInjection.AddMaplightAcceleratorClasses(_services);

        // Add Maplight Accelerator Classes Dependencies those are need both Efile Validateion and Processing functions.
        CommonDependencyInjection.AddMaplightAcceleratorClasses(_services);
    }

    /// <summary>
    /// Check if any required setting is missing or empty. Only reports the first one found.
    /// </summary>
    /// <exception cref="InvalidOperationException">Identifies which one is missing.</exception>
    private void ValidateSettings()
    {
        string? firstMissingSetting = _requiredSettings
            .FirstOrDefault(key => string.IsNullOrWhiteSpace(_configuration[key]));

        if (firstMissingSetting != null)
        {
            throw new InvalidOperationException($"Required setting \"{firstMissingSetting}\" is missing or empty.");
        }
    }
}
