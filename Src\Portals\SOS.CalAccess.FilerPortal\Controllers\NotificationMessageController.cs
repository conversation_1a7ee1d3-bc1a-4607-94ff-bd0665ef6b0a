using System.Globalization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.FilerPortal.Models.Grid;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Notifications;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Controllers;

[Route("Messages")]
public class NotificationMessageController(INotificationSvc notificationSvc, IReferenceDataSvc referenceDataSvc) : Controller
{
    [HttpGet("")]
    public async Task<IActionResult> Messages()
    {
        var messagesControllerName = nameof(Messages);
        var filerTypes = await referenceDataSvc.GetAllFilerTypes();

        var allOption = new FilerType() { Id = 0, Name = "All" };
        filerTypes = [allOption, .. filerTypes];

        var largeDataGridModel = new LargeDataGridModel
        {
            GridId = "Messages",
            AllowPaging = true,
            PageSize = 20,
            RowDataBound = "onRowDataBound",
            Action = nameof(RetrievePagedList),
            Controller = "NotificationMessage",
            SearchableColumns = new List<string>() { nameof(NotificationMessageDto.Subject), nameof(NotificationMessageDto.Message) },
            Columns = new List<DataGridColumn>{
            new() {Field= nameof(NotificationMessageDto.CreatedAt), HeaderText = ResourceConstants.MessageDate, IsDateTime = true},
            new() {Field= nameof(NotificationMessageDto.FilerName), HeaderText = ResourceConstants.MessageFilerName},
            new() {Field= nameof(NotificationMessageDto.Subject), HeaderText = ResourceConstants.MessageSubject, IsLink = true, ActionName = "Details", ControllerName = messagesControllerName},
            new() {Field= nameof(NotificationMessageDto.NotificationTypeName), HeaderText = ResourceConstants.MessageType},
            new() {Field= nameof(NotificationMessageDto.DueDate), HeaderText = ResourceConstants.MessageDueDate, IsDateTime = true},
            new() {Field= nameof(NotificationMessageDto.IsPriorityMessage), HeaderText = ResourceConstants.MessagePriority, IsBooleanDataType = true },
            new() {Field= nameof(NotificationMessageDto.IsActionRequired), HeaderText = ResourceConstants.MessageActionRequired, IsBooleanDataType = true }
            },
            AllowDeleting = true,
            ActionItems = new List<GridActionItem>
            {
                new() { Label = "", Action = "markReadOrUnread", ControllerName = messagesControllerName, ActionName = "MarkReadOrUnread"},
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = messagesControllerName, ActionName = "Delete"},
            },
            DeleteConfirmationMessage = ResourceConstants.NotificationDetailsDeleteConfirmation,
            DeleteConfirmationField = "Subject",
            ProcessBatchDeleteResponseFunction = "showToastIfNoMessageDeleted"
        };

        var viewModel = new NotificationMessageViewModel()
        {
            FilerTypes = filerTypes.ToDictionary(x => x.Id.ToString(CultureInfo.InvariantCulture), x => x.Name),
            LargeDataGridModel = largeDataGridModel,
            PriorityColumnIndexInGrid = GetPriorityColumnIndex(largeDataGridModel.Columns)

        };

        return View(messagesControllerName, viewModel);
    }

    private static int GetPriorityColumnIndex(List<DataGridColumn> columns)
    {
        for (int i = 0; i < columns.Count; i++)
        {
            if (columns[i].Field == nameof(NotificationMessageDto.IsPriorityMessage))
            {
                return i + 1; //plus one hidden column for the Id
            }
        }
        return -1;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("Delete/{id:long}")]
    public async Task<IActionResult> Messages([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        int recordsAffected = await notificationSvc.DeleteNotification(id);

        return Ok(recordsAffected);
    }

    [HttpPost("RetrievePagedList")]
    public async Task<IActionResult> RetrievePagedList([FromBody] CustomDataManagerRequest dm)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        try
        {
            var username = User.Identity?.Name ?? "";
            var filerTypeId = !string.IsNullOrEmpty(dm.Filter) ? long.Parse(dm.Filter, CultureInfo.InvariantCulture) : 0;
            string sortColumn = dm.Sorted?.FirstOrDefault()?.Name ?? nameof(NotificationMessage.CreatedAt);
            SortDirection sortDirection = string.Equals("ascending", dm.Sorted?.FirstOrDefault()?.Direction, StringComparison.OrdinalIgnoreCase) ? SortDirection.Ascending : SortDirection.Descending;
            string searchKey = string.Empty;
            List<string>? searchFields = null;
            if (dm.Search != null && dm.Search.Count > 0)
            {
                searchFields = dm.Search[0].Fields;
                searchKey = dm.Search[0].Key;
            }

            var request = new PagedUserDataRequest(username, dm.Skip, dm.Take, sortColumn, sortDirection, searchFields, searchKey);
            var response = await notificationSvc.ListAllNotifications(request, filerTypeId);

            return Json(response);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("Details/{id:long}")]
    public async Task<IActionResult> Details([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var message = await notificationSvc.ViewNotification(id);

        return View(message);
    }

    [HttpPost("MarkReadOrUnread/{id:long}")]
    public async Task<IActionResult> MarkReadOrUnread([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        await notificationSvc.MarkReadOrUnread(id);

        return Ok();
    }

}
