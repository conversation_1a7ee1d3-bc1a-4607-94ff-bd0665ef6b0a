using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Efile.Model;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Represents a single object for complete Candidate Intention Statement
/// (form 604) for submission through the Efile API.
/// </summary>
public class LobbyistRegistrationSubmissionDto
{
    /// <summary>
    /// Candidate statement short form submission
    /// </summary>
    public Lobbyist Lobbyist { get; set; } = new Lobbyist
    {
        Name = string.Empty,
        StatusId = RegistrationStatus.Accepted.Id,
    };

    /// <summary>
    /// Filing period for the candidate statement short form
    /// </summary>
    public EfileAttestation? Attestation { get; set; } = new EfileAttestation();

    /// <summary>
    /// Amendment details for the candidate statement short form    
    /// </summary>
    public EfileAmendment? Amendment { get; set; } = new EfileAmendment();

    public long? LobbyistEmployerOrLobbyingFirmId { get; set; }
    /// <summary>
    /// Submission Status
    /// </summary>
    public bool IsSubmission { get; set; }

    /// <summary>
    /// Api user id from config
    /// </summary>
    public long UserId { get; set; }
}
