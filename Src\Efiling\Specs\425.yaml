openapi: 3.0.3
info:
  title: Form 425 - Statement of No Activity API
  description: API for submitting Form 425, used by recipient committees that have not received contributions or made expenditures during a six-month period.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/NoActivity/{filerId}:
    post:
      summary: Submit Form 425 - Statement of No Activity
      description: Submit Form 425, including committee information, treasurer details, reporting period, and verification.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                submittedDate:
                  type: string
                  format: date
                  description: Date of stamp.
                treasurerInformation:
                  type: object
                  properties:
                    treasurer:
                      type: object
                      properties:
                        firstName:
                          type: string
                          description: First name of the Treasurer.
                        lastName:
                          type: string
                          description: Last name of the Treasurer.
                        address:
                          $ref: './common-schemas.yaml#/components/schemas/Address'
                        phone:
                          type: string
                          description: Phone number of the Treasurer as digits only.
                          pattern: '^\d+$'
                          maxLength: 40
                        email:
                          type: string
                          format: email
                          description: Fax number or email address of the Treasurer as digits only.
                        fax:
                          type: string
                          description: Fax number of the committee as digits only (optional).
                          pattern: '^\d+$'
                          maxLength: 40
                      description: Details of the Treasurer.
                    assistantTreasurer:
                      type: object
                      properties:
                        firstName:
                          type: string
                          description: First name of the Assistant Treasurer (if any).
                        lastName:
                          type: string
                          description: Last name of the Assistant Treasurer (if any).
                        address:
                          $ref: './common-schemas.yaml#/components/schemas/Address'
                        phoneNumber:
                          type: string
                          description: Phone number of the Assistant Treasurer as digits only.
                          pattern: '^\d+$'
                          maxLength: 40
                        email:
                          type: string
                          description: Fax number or email address of the Treasurer as digits only.
                        fax:
                          type: string
                          description: Fax number of the committee as digits only (optional).
                          pattern: '^\d+$'
                          maxLength: 40
                      description: Details of the Assistant Treasurer, if applicable.
                noActivityPeriod:
                  type: object
                  properties:
                    startDate:
                      type: string
                      format: date
                      description: Start date period
                    endDate:
                      type: string
                      format: date
                      description: End date period
                attestation:
                    $ref: './common-schemas.yaml#/components/schemas/Attestation'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'
