﻿using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Accept an email address and send an email containing the associated username 
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The Web API service invoking this layer is configured
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Allow a user to recover the username via email 
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>UA-05: Recover a Username</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service
/// </p>
/// | Service              | Operation       | Description                      |
/// | -------------------- | --------------- | -------------------------------- |
/// | IUserRepository      | GetById         | Retrieve a User                      |
#endregion

public interface IUsernameRecoverySvc
{
    /// <summary>
    /// Recover the Username for a user
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Task<string></returns>
    ///
    /// \msc
    /// Actor, IUsernameRecoverySvc [label="IUsernameRecovery\nSvc"], IUserRepository;
    /// Actor => IUsernameRecoverySvc [label="RecoverUsername()"];
    /// IUsernameRecoverySvc => IUserRepository [label="GetUser()"];
    /// IUserRepository >> IUsernameRecoverySvc [label="\nreturn User"];
    /// IUsernameRecoverySvc >> Actor [label="\nreturn string"];
    /// \endmsc
    public Task<string> RecoverUsername(RecoverUsernameRequest request);
}
