openapi: 3.0.3
info:
  title: Lobbying Coalition Payment Reporting API
  description: API for managing payments received by lobbying coalitions, as described in Form 635-C.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Disclosure/PaymentsReceived/{filerId}:
    post:
      summary: Record a payment received by the lobbying coalition.
      description: Add details of a payment received by a coalition member.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    PaymentRequest:
      type: object
      properties:
        periodFrom:
          type: string
          format: date
          description: Start date of the reporting period in `YYYY-MM-DD` format.
        periodTo:
          type: string
          format: date
          description: End date of the reporting period in `YYYY-MM-DD` format.
        # cumulativePeriodBeginning:
        #   type: string
        #   format: date
        #   description: Date marking the start of the cumulative reporting period in `YYYY-MM-DD` format.
        payments:
          type: array
          description: List of payments made.
          items:
            $ref: '#/components/schemas/Payment'

    Payment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          $ref: '#/components/schemas/Name'
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        amountReceived:
          type: number
          format: float
          description: Amount received from the member during the reporting period.
        # cumulativeAmount:
        #   type: number
        #   format: float
        #   description: Cumulative amount received from the member since the start of the biennial legislative session.
      required:
        - name
        - address
        - amountReceived

    Name:
      type: object
      description: Complete name components of an individual
      properties:
        firstName:
          type: string
          description: First name of the individual
          minLength: 1
        middleName:
          type: string
          description: Middle name(s) of the individual
          nullable: true
        lastName:
          type: string
          description: Last name of the individual
          minLength: 1
        suffix:
          type: string
          description: Name suffix (e.g., Jr., Sr., III)
          nullable: true
      required:
        - firstName
        - lastName
      example:
        firstName: "John"
        middleName: "Robert"
        lastName: "Smith"
        suffix: "Jr."

