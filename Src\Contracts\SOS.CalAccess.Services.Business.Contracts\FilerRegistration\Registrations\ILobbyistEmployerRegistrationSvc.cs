// <copyright file="ILobbyistEmployerRegistrationSvc.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling lobbyist employer registration operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the registration data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this service is to facilitate the retrieval and management of lobbyist employer registrations.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IRegistrationRepository        | FindLobbyistEmployerById       | Retrieves a lobbyist employer registration by ID. |
#endregion
public interface ILobbyistEmployerRegistrationSvc
{
    /// <summary>
    /// Retrieves the Lobbyist Employer registration for a given ID.
    /// </summary>
    /// <param name="id">The unique identifier of the Lobbyist Employer registration.</param>
    /// <returns>Returns the Lobbyist Employer registration details if found; otherwise, null.</returns>
    [Get("/" + GetLobbyistEmployerPath)]
    Task<LobbyistEmployerResponseDto?> GetLobbyistEmployer(long id);
    const string GetLobbyistEmployerPath = "api/Registration/LobbyistEmployer/{id}";

    /// <param name="id"> The unique identifier of the Filer.</param>
    /// <returns>Returns the Lobbyist Employer registration details if found; otherwise, null.</returns>
    [Get("/" + GetLobbyistEmployerByFilerIdPath)]
    Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByFilerId(long id);
    const string GetLobbyistEmployerByFilerIdPath = "api/Registration/LobbyistEmployer/Filer/{id}";

    /// <summary>
    /// Retrieves the Lobbyist Employer registration for a given name.
    /// </summary>
    /// <param name="name">The name of the Lobbyist Employer registration.</param>
    /// <returns>Returns the Lobbyist Employer registration details if found; otherwise, null.</returns>
    [Get("/" + GetLobbyistEmployerByNamePath)]
    Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByName(string name);
    const string GetLobbyistEmployerByNamePath = "api/Registration/LobbyistEmployer";

    /// <summary>
    /// Cancel a draft lobbyist employer registration
    /// </summary>
    /// <param name="id">Unique identifier for lobbyist employer registration record</param>
    /// <returns></returns>
    [Post("/" + CancelLobbyistEmployerRegistrationPath)]
    Task CancelLobbyistEmployerRegistration(long id);
    const string CancelLobbyistEmployerRegistrationPath = "api/Registration/LobbyistEmployer/Cancel/{id}";

    [Post("/" + CreateLobbyistEmployerRegistrationPage03Path)]
    Task<RegistrationResponseDto> CreateLobbyistEmployerRegistrationPage03(LobbyistEmployerGeneralInfoRequest request);
    const string CreateLobbyistEmployerRegistrationPage03Path = "api/Registration/LobbyistEmployer/Page03";

    [Put("/" + UpdateLobbyistEmployerRegistrationPage03Path)]
    Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage03(long id, LobbyistEmployerGeneralInfoRequest request);
    const string UpdateLobbyistEmployerRegistrationPage03Path = "api/Registration/LobbyistEmployer/{id}/Page03";

    [Put("/" + UpdateLobbyistEmployerRegistrationPage04Path)]
    Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage04(long id, LobbyistEmployerStateAgenciesRequest request);
    const string UpdateLobbyistEmployerRegistrationPage04Path = "api/Registration/LobbyistEmployer/{id}/Page04";

    [Put("/" + UpdateLobbyistEmployerRegistrationPage05Path)]
    Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage05(long id, LobbyistEmployerLobbyingInterestsRequest request);
    const string UpdateLobbyistEmployerRegistrationPage05Path = "api/Registration/LobbyistEmployer/{id}/Page05";
}
