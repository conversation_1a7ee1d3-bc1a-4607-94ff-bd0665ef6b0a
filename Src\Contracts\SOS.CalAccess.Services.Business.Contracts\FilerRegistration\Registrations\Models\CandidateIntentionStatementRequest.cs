namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// CandidateIntentionStatementRequest.
/// </summary>
public record CandidateIntentionStatementRequest
{
    /// <summary>
    /// Gets or sets Candidate OriginalId.
    /// </summary>
    public long? OriginalId { get; set; }

    /// <summary>
    /// Gets or sets Candidate ParentId.
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// Gets or sets Candidate FirstName.
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Gets or sets Candidate MiddleName.
    /// </summary>
    public string? MiddleName { get; set; }

    /// <summary>
    /// Gets or sets Candidate LastName.
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Gets or sets Candidate Email.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets Candidate Telephone.
    /// </summary>
    public PhoneNumberDto? Telephone { get; set; }

    /// <summary>
    /// Gets or sets Candidate FaxNumber.
    /// </summary>
    public PhoneNumberDto? FaxNumber { get; set; }

    /// <summary>
    /// Gets or sets Candidate SelfRegister.
    /// </summary>
    public bool? SelfRegister { get; set; }

    /// <summary>
    /// Gets or sets PreviousCandidate.
    /// </summary>
    public bool? PreviousCandidate { get; set; }

    /// <summary>
    /// Gets or sets IsSameAsCandidateAddress.
    /// </summary>
    public bool? IsSameAsCandidateAddress { get; set; }

    /// <summary>
    /// Gets or sets Candidate CandidateId.
    /// </summary>
    public long? CandidateId { get; set; }

    /// <summary>
    /// Gets or sets Candidate address details.
    /// </summary>
    public AddressDto? CandidateAddress { get; set; }

    /// <summary>
    /// Gets or sets Candidate mailing address details.
    /// </summary>
    public AddressDto? CandidateMailingAddress { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

    /// <summary>
    /// Gets or sets the PoliticalPartyId.
    /// </summary>
    public long? PoliticalPartyId { get; set; }

    /// <summary>
    /// Gets or sets ExpenditureLimitAccepted.
    /// </summary>
    public bool? ExpenditureLimitAccepted { get; set; }

    /// <summary>
    /// Gets or sets ContributedPersonalExcessFundsOn.
    /// </summary>
    public DateTime? ContributedPersonalExcessFundsOn { get; set; }

    /// <summary>
    /// Gets or sets ElectionRaceId.
    /// </summary>
    public long? ElectionRaceId { get; set; }
    /// <summary>
    /// Gets or sets Version.
    /// </summary>
    public int? Version { get; set; }
}

/// <summary>
/// CandidateIntentionStatementExpenditureLimitRequest.
/// </summary>
public record CandidateIntentionStatementExpenditureLimitRequest
{
    /// <summary>
    /// Gets or sets ExpenditureLimitAccepted.
    /// </summary>
    public bool? ExpenditureLimitAccepted { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

    /// <summary>
    /// Gets or sets the ContributedPersonalExcessFundsOn.
    /// </summary>
    public DateTime? ContributedPersonalExcessFundsOn { get; set; }

    /// <summary>
    /// Gets or sets the ExpenditureExceeded.
    /// </summary>
    public bool? ExpenditureExceeded { get; set; }

}
