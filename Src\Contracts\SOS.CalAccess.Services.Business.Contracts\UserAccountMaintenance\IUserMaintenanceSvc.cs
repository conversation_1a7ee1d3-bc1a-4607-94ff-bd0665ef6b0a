using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;

namespace SOS.CalAccess.Services.Business.UserAccountMaintenance;

/// <summary>
/// Interface user maintenance service
/// </summary>
/// 
#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IUserMaintenanceSvc provides methods to update the CARS database when a user add, update or remove the address, phone number or email in the application.
/// </p>
/// <p>
/// Also list, view and search users in the application from the database.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend business application layer service invoked by the web API layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Authorization is performed at the Web API services layer.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to add and update information from the solutions to the solutions DB for use in displaying the information through the various portals.
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UA-03: Search for a User/Entity</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                      | Operation                    | Description                         |
/// | ---------------------------- | ---------------------------- | ----------------------------------- |
/// | IUserRepository              | ListUsers                    | Gets list of users                  |
/// | IUserAddressRepository       | Create, Update, Delete       | Search user by user id or user name |
/// | IUserPhoneNumberRepository   | Create, Update, Delete       | View user details                   |
/// | IUserEmailAddressRepository  | Create, Update, Delete       | Add address to user account         |
/// | IAddressVerificationSvc      | VerifyUserAddress            | Verify user address                 |
/// | IEmailSvc                    | SendEmail                    | Sends an email                      |
/// | IUserRepository              | DeactivateUser               | Deactivates a specific user         |
#endregion

public interface IUserMaintenanceSvc
{
    /// <summary>
    /// Gets list of all users
    /// </summary>
    /// <returns>Task<IEnumerable<User>></returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="ListUsers()"];
    /// IUserMaintenanceSvc => IUserRepository [label="GetAll()"];
    /// IUserRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get(ListUsersPath)]
    public Task<IEnumerable<User>> ListUsers();
    public const string ListUsersPath = "/api/UserMaintenance/User/All";

    /// <summary>
    /// Search user by user id or user name
    /// </summary>
    /// <param name="user"></param>
    /// <returns>Task<IEnumerable<User>></returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="SearchUsers()"];
    /// IUserMaintenanceSvc => IUserRepository [label="SearchUsers()"];
    /// IUserRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post(SearchUsersPath)]
    public Task<IEnumerable<User>> SearchUsers(User user);
    public const string SearchUsersPath = "/api/UserMaintenance/User/Search";

    /// <summary>
    /// View user details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns>Task<User></returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="ViewUser()"];
    /// IUserMaintenanceSvc => IUserRepository [label="GetById()"];
    /// IUserRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get(ViewUserPath)]
    public Task<User> ViewUser(int userId);
    public const string ViewUserPath = "/api/UserMaintenance/User/{userId}";

    /// <summary>
    /// View user details by user id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns>Task<User></returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="ViewUser()"];
    /// IUserMaintenanceSvc => IUserRepository [label="GetById()"];
    /// IUserRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get(GetCurrentUserPath)]
    public Task<BasicUserDto> GetCurrentUser();
    public const string GetCurrentUserPath = "/api/UserMaintenance/CurrentUser";

    /// <summary>
    /// Add address to user account
    /// </summary>
    /// <param name="address"></param>
    /// <param name="userId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IAddressVerificationSvc [label="IAddress \n Verification \n Svc"], IUserAddressRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="AddUserAddress()"];
    /// IUserMaintenanceSvc => IAddressVerificationSvc [label="VerifyAddress()"];
    /// IAddressVerificationSvc >> IUserMaintenanceSvc [label="\nreturn bool"];
    /// IUserMaintenanceSvc => IUserAddressRepository [label="Create()"];
    /// IUserAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void AddUserAddress(Address address, int userId);

    /// <summary>
    /// Update user address
    /// </summary>
    /// <param name="address"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IAddressVerificationSvc [label="IAddress \n Verification \n Svc"], IUserAddressRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="UpdateUserAddress()"];
    /// IUserMaintenanceSvc => IAddressVerificationSvc [label="VerifyAddress()"];
    /// IAddressVerificationSvc >> IUserMaintenanceSvc [label="\nreturn bool"];
    /// IUserMaintenanceSvc => IUserAddressRepository [label="Update()"];
    /// IUserAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void UpdateUserAddress(UserAddress address);

    /// <summary>
    /// Remove user address
    /// </summary>
    /// <param name="userAddressId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserAddressRepository [label="IUser \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="RemoveUserAddress()"];
    /// IUserMaintenanceSvc => IUserAddressRepository [label="Delete()"];
    /// IUserAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void RemoveUserAddress(int userAddressId);

    /// <summary>
    /// Add user phone number
    /// </summary>
    /// <param name="phoneNumber"></param>
    /// <param name="userId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserPhoneNumberRepository [label="IUserPhoneNumber \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="AddUserPhoneNumber()"];
    /// IUserMaintenanceSvc => IUserPhoneNumberRepository [label="Create()"];
    /// IUserPhoneNumberRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void AddUserPhoneNumber(PhoneNumber phoneNumber, int userId);

    /// <summary>
    /// Update user phone number
    /// </summary>
    /// <param name="phoneNumber"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserPhoneNumberRepository [label="IUserPhoneNumber \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="UpdateUserPhoneNumber()"];
    /// IUserMaintenanceSvc => IUserPhoneNumberRepository [label="Update()"];
    /// IUserPhoneNumberRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void UpdateUserPhoneNumber(UserPhoneNumber phoneNumber);

    /// <summary>
    /// Remove user phone number
    /// </summary>
    /// <param name="userPhoneNumberId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserPhoneNumberRepository [label="IUserPhoneNumber \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="RemoveUserPhoneNumber()"];
    /// IUserMaintenanceSvc => IUserPhoneNumberRepository [label="Delete()"];
    /// IUserPhoneNumberRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void RemoveUserPhoneNumber(int userPhoneNumberId);

    /// <summary>
    /// Add user email address
    /// </summary>
    /// <param name="emailAddress"></param>
    /// <param name="userId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserEmailAddressRepository [label="IUserEmailAddress \n Repository"], IEmailSvc;
    /// Actor => IUserMaintenanceSvc [label="AddUserEmailAddress()"];
    /// IUserMaintenanceSvc => IUserEmailAddressRepository [label="Create()"];
    /// IUserEmailAddressRepository => IUserMaintenanceSvc [label="return"];
    /// IUserMaintenanceSvc => IUserMaintenanceSvc [label="RequestUserEmailVerification()"];
    /// IUserMaintenanceSvc >> Actor [label="return"];
    /// \endmsc
    public void AddUserEmailAddress(EmailAddress emailAddress, string userId);

    /// <summary>
    /// Generates a unique code, stores it in the database on the email address record, 
    /// and sends an email containing the code to the email address
    /// </summary>
    /// <param name="emailId"></param>
    /// <returns>int</returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], , IUserEmailAddressRepository [label="IUserEmailAddress \n Repository"], IEmailSvc;
    /// Actor => IUserMaintenanceSvc [label="RequestUserEmailVerification()"];
    /// IUserMaintenanceSvc => IUserEmailAddressRepository [label="Update()"];
    /// IUserEmailAddressRepository => IUserMaintenanceSvc [label="return"];
    /// IUserMaintenanceSvc => IEmailSvc [label="SendEmail()"];
    /// IEmailSvc >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn VerificationCode()"];
    /// \endmsc
    public int RequestUserEmailVerification(string emailId);

    /// <summary>
    /// Verify user email
    /// </summary>
    /// <param name="emailId"></param>
    /// <param name="verificationCode"></param>
    /// <returns>bool</returns>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserEmailAddressRepository [label="IUserEmailAddress \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="VerifyUserEmail()"];
    /// IUserMaintenanceSvc => IUserEmailAddressRepository [label="GetById()"];
    /// IUserEmailAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc => IUserEmailAddressRepository [label="Update()"];
    /// IUserEmailAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    public bool VerifyUserEmail(int emailId, string verificationCode);

    /// <summary>
    /// Remove user email address
    /// </summary>
    /// <param name="userId"></param>
    /// 
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUser \n Maintenance \n Svc"], IUserEmailAddressRepository [label="IUserEmailAddress \n Repository"];
    /// Actor => IUserMaintenanceSvc [label="RemoveUserEmailAddress()"];
    /// IUserMaintenanceSvc => IUserEmailAddressRepository [label="Delete()"];
    /// IUserEmailAddressRepository >> IUserMaintenanceSvc [label="\nreturn "];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    public void RemoveUserEmailAddress(int emailId);

    /// <summary>
    /// Get a collection of user information by a list of user name
    /// </summary>
    /// <param name="userNames">List of user name</param>
    /// <returns></returns>
    [Get(GetListUsersByUserNamePath)]
    Task<List<User>> GetListUsersByUserNameAsync(List<string> userNames);
    const string GetListUsersByUserNamePath = "/api/UserMaintenance/User/SearchByUserNames";

    /// <summary>
    /// Gets the user details.
    /// </summary>
    /// <param name="userId">The ID of the user to retrieve</param>
    /// <returns>User object with detailed information</returns
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUserMaintenanceSvc"], IUserRepositorySvc [label="IUserRepositorySvc"];
    /// Actor => IUserMaintenanceSvc [label="GetUser(userId)"];
    /// IUserMaintenanceSvc => IUserRepositorySvc [label="GetUser(userId)"];
    /// IUserRepositorySvc >> IUserMaintenanceSvc [label="\nreturn User"];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn User"];
    /// \endmsc
    [Get(GetUserByIDPath)]
    Task<User> GetUser(long userId);
    const string GetUserByIDPath = "/api/UserMaintenance/User/GetUser";


    /// <summary>
    /// Updates the details of a user.
    /// </summary>
    /// <param name="user">User object containing updated data</param>
    /// <returns>Updated User object</returns
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUserMaintenanceSvc"], IUserRepositorySvc [label="IUserRepositorySvc"];
    /// Actor => IUserMaintenanceSvc [label="UpdateUser(user)"];
    /// IUserMaintenanceSvc => IUserRepositorySvc [label="UpdateUser(user)"];
    /// IUserRepositorySvc >> IUserMaintenanceSvc [label="\nreturn User"];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn User"];
    /// \endmsc
    [Post(UpdateUserPath)]
    public Task<User> UpdateUser(User user);
    const string UpdateUserPath = "/api/UserMaintenance/User/UpdateUser";

    /// <summary>
    /// Deactivates a user using the user ID and inactivation reason.
    /// Call EntraId to Deactivate user - interact with Entra ID via a RestAPI and call Entra ID to disable an account via graph database call. 
    /// </summary>
    /// <param name="userId">The ID of the user to deactivate</param>
    /// <param name="inactivateReason">The reason code for inactivation</param>
    /// <returns>bool indicating whether the deactivation was successful</returns>
    /// \msc
    /// Actor, IUserMaintenanceSvc [label="IUserMaintenanceSvc"], IUserRepositorySvc [label="IUserRepositorySvc"], EntraId;
    /// Actor => IUserMaintenanceSvc [label="DeactivateUser()"];
    /// IUserMaintenanceSvc => IUserRepositorySvc [label="Update()"];
    /// IUserRepositorySvc >> IUserMaintenanceSvc [label="\nreturn bool"];
    /// IUserMaintenanceSvc => EntraId [label="DeactivateUser()"];
    /// EntraId >> IUserMaintenanceSvc [label="\nreturn bool"];
    /// IUserMaintenanceSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    [Post(DeactivateUserPath)]
    public Task<bool> DeactivateUser(long userId, long inactivateReason);
    const string DeactivateUserPath = "/api/UserMaintenance/User/DeactivateUser";
}
