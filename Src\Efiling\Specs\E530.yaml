openapi: 3.0.3
info:
  title: Issue Advocacy Disclosure Form (E-530)
  description: API for submitting Form E-530
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/IssueAdvocacy/{filerId}:
    post:
      summary: Submit the Issue Advocacy Disclosure Form
      description: Submit the Issue Advocacy Disclosure Form (E-530).
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IssueAdvocacyDisclosure'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    IssueAdvocacyDisclosure:
      type: object
      properties:
        candidateInformation:
          $ref: '#/components/schemas/CandidateInformation'
        typeOfCommunication:
          type: object
          properties:
            campaignLiterature:
              type: boolean
              description: Campaign literature and mailings
            printAds:
              type: boolean
              description: Print ads
            radioAirtime:
              type: boolean
              description: Radio airtime and production costs
            tvCableAirtime:
              type: boolean
              description: TV or cable airtime and production costs
            informationTechnology:
              type: boolean
              description: Information technology costs (Internet, e-mail)
            billboards:
              type: boolean
              description: Billboards
            other:
              type: boolean
              description: Other
            otherDescription:
              type: string
              description: Description of other communication types (required if 'Other' is checked)
          required:
            - campaignLiterature
            - printAds
            - radioAirtime
            - tvCableAirtime
            - informationTechnology
            - billboards
            - other
        contributionDetails:
          type: object
          properties:
            receivedLargeContribution:
              type: boolean
              description: Did you receive $5,000.00 or more from a single contributor?
          required:
            - receivedLargeContribution
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'

      required:
        - candidateInformation
        - typeOfCommunication
        - contributionDetails

    CandidateInformation:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the candidate.
        lastName:
          type: string
          description: Last name of the candidate.
        officeSought:
          $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
        districtNumber:
          type: string
          description: District number, if applicable.
        paymentDate:
          type: string
          format: date
          description: Date of the payment.
        paymentAmount:
          type: number
          format: double
          description: Amount of the payment.
      required:
        - firstName
        - lastName
        - officeSought
        - districtNumber
        - paymentDate
        - paymentAmount
