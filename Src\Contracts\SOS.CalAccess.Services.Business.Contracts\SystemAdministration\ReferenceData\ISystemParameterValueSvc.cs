using SOS.CalAccess.Services.Business.SystemAdministration.ReferenceData.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.ReferenceData;
#region DesignNotes
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the service contract for managing system parameter values within the platform.
/// It offers a set of operations to create, update, delete, retrieve, and search configuration values.
/// These parameters are key to controlling feature flags, system behavior, and other environment-driven settings.
/// </p>
///
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that incoming DTOs are validated at the API or application level before reaching the service.
/// The repository layer handles persistence and is responsible for mapping between DTOs and entities.
/// </p>
///
/// <h4>Business Function</h4>
/// <p>
/// This service acts as an intermediary between external consumers and the data layer for configuration management.
/// It supports full lifecycle control of parameter values and enables querying via business identifiers like short name.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA-01: Configure System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// The following repository methods are invoked by this service interface:
/// </p>
/// | Service                          | Operation            | Description                                                  |
/// |----------------------------------|-----------------------|--------------------------------------------------------------|
/// | ISystemParameterRepositorySvc    | Create               | Persists a new system parameter value.                       |
/// | ISystemParameterRepositorySvc    | Update               | Updates an existing system parameter value.                  |
/// | ISystemParameterRepositorySvc    | Delete               | Deletes a system parameter value by ID.                      |
/// | ISystemParameterRepositorySvc    | GetAll               | Retrieves all system parameter values.                       |
/// | ISystemParameterRepositorySvc    | SearchByShortName    | Finds parameter values based on their short name.            |
#endregion
public interface ISystemParameterValueSvc
{
    /// <summary>
    /// Creates a new system parameter value.
    /// </summary>
    /// <param name="systemParameterValue">System parameter value DTO</param>
    /// <returns>System parameter value DTO</returns>
    /// \msc
    /// Actor, ISystemParameterValueSvc [label="ISystemParameterValueSvc"], ISystemParameterRepositorySvc;
    /// Actor => ISystemParameterValueSvc [label="CreateSystemParameter()"];
    /// ISystemParameterValueSvc => ISystemParameterRepositorySvc [label="Create()"];
    /// ISystemParameterRepositorySvc >> ISystemParameterValueSvc [label="\nreturn SystemParameterValue"];
    /// ISystemParameterValueSvc >> Actor [label="\nreturn SystemParameterValueDto"];
    /// \endmsc
    Task<SystemParameterValueDto> CreateSystemParameter(SystemParameterValueDto systemParameterValue);

    /// <summary>
    /// Updates an existing system parameter value.
    /// </summary>
    /// <param name="systemParameterValue">System parameter value DTO</param>
    /// <returns>System parameter value DTO</returns>
    /// \msc
    /// Actor, ISystemParameterValueSvc [label="ISystemParameterValueSvc"], ISystemParameterRepositorySvc;
    /// Actor => ISystemParameterValueSvc [label="UpdateSystemParameter()"];
    /// ISystemParameterValueSvc => ISystemParameterRepositorySvc [label="Update()"];
    /// ISystemParameterRepositorySvc >> ISystemParameterValueSvc [label="\nreturn SystemParameterValue"];
    /// ISystemParameterValueSvc >> Actor [label="\nreturn SystemParameterValueDto"];
    /// \endmsc
    Task<SystemParameterValueDto> UpdateSystemParameter(SystemParameterValueDto systemParameterValue);


    /// <summary>
    /// Deletes a system parameter value by ID.
    /// </summary>
    /// <param name="systemParameterId">ID of the system parameter value to delete</param>
    /// <returns>True if deletion was successful, otherwise false</returns>
    /// \msc
    /// Actor, ISystemParameterValueSvc [label="ISystemParameterValueSvc"], ISystemParameterRepositorySvc;
    /// Actor => ISystemParameterValueSvc [label="DeleteSystemParameter()"];
    /// ISystemParameterValueSvc => ISystemParameterRepositorySvc [label="Delete()"];
    /// ISystemParameterRepositorySvc >> ISystemParameterValueSvc [label="\nreturn bool"];
    /// ISystemParameterValueSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    Task<bool> DeleteSystemParameter(long systemParameterId);

    /// <summary>
    /// Retrieves all system parameter values.
    /// </summary>
    /// <returns>A collection of system parameter value DTOs</returns>
    /// \msc
    /// Actor, ISystemParameterValueSvc [label="ISystemParameterValueSvc"], ISystemParameterRepositorySvc;
    /// Actor => ISystemParameterValueSvc [label="GetSystemParameters()"];
    /// ISystemParameterValueSvc => ISystemParameterRepositorySvc [label="GetAll()"];
    /// ISystemParameterRepositorySvc >> ISystemParameterValueSvc [label="\nreturn IEnumerable<SystemParameterValue>"];
    /// ISystemParameterValueSvc >> Actor [label="\nreturn IEnumerable<SystemParameterValueDto>"];
    /// \endmsc
    Task<IEnumerable<SystemParameterValueDto>> GetSystemParameters();

    /// <summary>
    /// Searches system parameter values by  name.
    /// </summary>
    /// <param name="shortName">The short name used to filter parameters</param>
    /// <returns>A collection of matching system parameter value DTOs</returns>
    /// \msc
    /// Actor, ISystemParameterValueSvc, ISystemParameterRepositorySvc;
    /// Actor => ISystemParameterValueSvc [label="SearchSystemParametersByName()"];
    /// ISystemParameterValueSvc => ISystemParameterRepositorySvc [label="SearchSystemParameterByName()"];
    /// ISystemParameterRepositorySvc >> ISystemParameterValueSvc [label="\nreturn IEnumerable<SystemParameter>"];
    /// ISystemParameterValueSvc >> Actor [label="\nreturn IEnumerable<SystemParameterValueDto>"];
    /// \endmsc
    Task<IEnumerable<SystemParameterValueDto>> SearchSystemParametersByName(string shortName);

}
