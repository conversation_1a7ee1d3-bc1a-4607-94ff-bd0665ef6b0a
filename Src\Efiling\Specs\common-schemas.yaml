openapi: 3.0.3
info:
  title: Common Schemas
  version: 0.1.0
  description: Shared components
paths: {}

components:
  schemas:
    Address:
      type: object
      description: Detailed business or residential address
      properties:
        id:
          type: string
          format: uuid
          description: Unique Id for the address.
        street:
          type: string
          description: Street address.
        street2:
          type: string
          description: Street address.
        city:
          type: string
          description: City.
        state:
          type: string
          description: State.
        zipCode:
          type: string
          description: ZIP code.
        county:
          type: string
          description: County.
        country:
          type: string
          description: Country.
        type:
          type: string
          description: Type of Address.
      required:
        - street
        - city
        - state
        - zipCode
        - country
        - type
      example:
        street: '123 Main Street'
        street2: 'Suite 456'
        city: 'Sacramento'
        state: 'CA'
        zipCode: '95814'
        country: 'United States'
        type: 'Residential'
    OrgAddress:
      allOf:
        - $ref: '#/components/schemas/Address'
        - type: object
          required:
            - county
          example:
            street: '123 Main Street'
            street2: 'Suite 456'
            city: 'Sacramento'
            county: 'Sacramento'
            state: 'CA'
            zipCode: '95814'
            country: 'United States'
            type: 'Residential'
    ElectionOffice:
      type: object
      properties:
        office:
          type: string
          # enum:
          #   - Governor
          #   - Lieutenant Governor
          #   - Attorney General
          #   - Insurance Commissioner
          #   - Controller
          #   - Secretary of State
          #   - Treasurer
          #   - Superintendent of Public Instruction
          #   - Assembly Member
          #   - Senator
          #   - CalPERS member elect
          #   - CalSTRS member elect
          #   - Other
          description: Office held or sought by the candidate.
        officeDescription:
          type: string
          description: Title of the office by the candidate.
        jurisdiction:
          type: string
          # enum:
          #   - State
          #   - City
          #   - County
          #   - Multi-County
          description: Jurisdiction of the office.
        jurisdictionDescription:
          type: string
          description: Jurisdiction of the candidate or measure.
        electionDistrictNumber:
          type: string
          description: District number (if applicable).

    PartyAffiliation:
      type: string
      description: Political parties qualified by the California Secretary of State.
      # enum:
      #   - American Independent Party
      #   - Democratic Party
      #   - Green Party
      #   - Libertarian Party
      #   - Peace and Freedom Party
      #   - Republican Party
      #   - Non-Partisan

    FilingResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique Id for the filing.
        filingStatus:
          type: string
          enum:
            - Rejected
            - Submitted
        validationErrors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
      required:
        - id
        - filingStatus

    FilingType:
      type: object
      properties:
        amendment:
          type: boolean
        isPreElectionStatement:
          type: boolean
        isQuarterlyStatement:
          type: boolean
        isSemiAnnualStatement:
          type: boolean
        isSpecialOddYearReport:
          type: boolean
        termination:
          type: boolean
        amendmentNumber:
          type: string
        initialFilingId:
          type: string
        explanation:
          type: string

    ValidationError:
      type: object
      properties:
        field:
          type: string
        errorMessage:
          type: string

    FilerDetails:
      type: object
      properties:
        id:
          type: string
          description: Unique ID for the filer details.
        name:
          $ref: '#/components/schemas/PersonName'
        businessAddress:
          $ref: '#/components/schemas/Address'
        mailingAddress:
          $ref: '#/components/schemas/Address'
        phone:
          $ref: '#/components/schemas/PhoneNumber'
        fax:
          $ref: '#/components/schemas/PhoneNumber'
        email:
          type: string
          format: email
          description: Filer's email address.
      required:
        - name
        - businessAddress
        - phone

    FilerContact:
      type: object
      required:
        - contactTypeId,
        - externalId,
        - contactInfo
      properties:
        contactTypeId:
          type: number
          description: contact type id defines either Individual or Organization
        externalId:
          type: string
          description: A unique identifier for this contact in the context of a filer
        contactInfo:
            oneOf:
            - $ref: '#/components/schemas/PersonContact'
            - $ref: '#/components/schemas/OrganizationContact'

    PersonContact:
      type: object
      required:
        - personName,
        - address
      properties:
        personName:
          $ref: '#/components/schemas/PersonName'
        address:
          $ref: '#/components/schemas/Address'
        occupation:
          type: string
        employer:
          type: string
        phone:
          type: string
          description: Filer's phone number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        email:
          type: string
          format: email
          description: Filer's email address.

    OrganizationContact:
      type: object
      required:
        - organizationName,
        - address
      properties:
        organizationName:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        phone:
          type: string
          description: Filer's phone number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        email:
          type: string
          format: email
          description: Filer's email address.

    PersonName:
      type: object
      required:
        - firstName
        - lastName
      properties:
        firstName:
          type: string
        middleName:
          type: string
        lastName:
          type: string

    Spokesperson:
      type: object
      properties:
        name:
          type: string
          description: Name of the spokesperson.
        address:
          $ref: '#/components/schemas/Address'
        occupation:
          type: string
          description: Occupation of the spokesperson, if applicable.
        amountPaid:
          type: number
          format: double
          description: Total amount paid to the spokesperson.
        dateOfExpenditure:
          type: string
          format: date
          description: Date of the expenditure or services received.
      required:
        - name
        - address
        - amountPaid
        - dateOfExpenditure

    Amendment:
      type: object
      properties:
        isAmendment:
          type: boolean
          description: Indicates if the submission is an amendment to a previously filed form.
        supercededFilingId:
          type: number
          format: int64
          description: The Secretary of State issued filing ID number for the filing being amended
      description: Amendment information, if applicable.
      required:
        - isAmendment
        - supercededFilingId

    AmendmentWithExplanation:
      type: object
      properties:
        isAmendment:
          type: boolean
          description: Indicates if the submission is an amendment to a previously filed form.
        supercededFilingId:
          type: string
          description: The Secretary of State issued filing ID number for the filing being amended
        descriptionOfAmendment:
          type: string
          description: Explanation of changes
      description: Amendment information, if applicable.
      required:
        - isAmendment
        - supercededFilingId
        - descriptionOfAmendment

    Attestation:
      type: object
      properties:
        firstName:
          type: string
          description: First Name of verification officer.
        lastName:
          type: string
          description: Last Name of verification officer.
        executedAt:
          type: string
          format: date
          description: Date the statement was executed.
        executedAtCity:
          type: string
          description: Executed at City.
        executedAtState:
          type: string
          description: Executed at state.
        role:
          type: string
        signature:
          type: string
          description: Name of the treasurer or filer.
      required:
        - executedAt
        - signature

    Measure:
      type: object
      properties:
        name:
          type: string
          description: Name of the measure supported or opposed.
        jurisdiction:
          type: string
          description: District number of the measure (if applicable).
        position:
          type: string
          enum:
            - Support
            - Oppose
          description: Ballot measure supported/opposed.
        number:
          type: string
          description: Ballot/Measure number.
      required:
        - name
        - position

    Candidate:
      type: object
      properties:
        firstName:
          type: string
          description: Name of the candidate supported or opposed.
        lastName:
          type: string
          description: Last Name of the candidate supported or opposed.
        electionCity:
          type: string
          description: City (if applicable).
        electionCounty:
          type: string
          description: County (if applicable).
        officeSoughtOrHeld:
          type: string
          description: Office sought or held by the candidate.
        districtNumber:
          type: string
          description: District number of the office (if applicable).
        jurisdiction:
          type: string
          description: District number of the office (if applicable).
        position:
          type: string
          enum:
            - Support
            - Oppose
          description: Candidate supported/opposed.
      required:
        - firstName
        - lastName
        - position

    PhoneNumber:
      type: object
      properties:
        countryCode:
          type: string
          description: Country Code.
          default: "+1"
        number:
          type: string
          description: Phone Number with area code, digits only.
          pattern: '^\d+$'
          maxLength: 20
        extension:
          type: string
          description: Phone Number Extension.
          maxLength: 20
      required:
        - countryCode
        - number

  responses:
    OK:
      description: Successful operation.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/FilingResponse'
    BadRequest:
      description: Request is invalid and could not be processed. Submitted data failed schema validation or contained other errors.
    Unauthorized:
      description: Unauthorized filing.
    InternalServerError:
      description: Internal server error.
