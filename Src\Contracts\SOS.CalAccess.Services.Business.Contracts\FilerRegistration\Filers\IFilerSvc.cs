
using Refit;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers;

#region Design Notes
/// <summary>
/// Interface for the Filer Service.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Filer Service. The service is responsible for managing filer data, including creating, updating, and terminating filers.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating filer data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the lifecycle of filers, including creation, modification, and termination of filer records.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FD-01: Enter an Activity Report</li>
/// <li>FD-02: Modify an Activity Report</li>
/// <li>FD-03: Upload an Activity Report</li>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-03: Terminate a Registration</li>
/// <li>FR-04: Withdraw a Registration</li>
/// <li>FR-05: Establish Major Donor/Independent Expenditure and Payment To Influence Filer Entity Accounts</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IFilerRepository               | Create                         | Creates a new filer          |
/// | IFilerRepository               | Update                         | Updates an existing filer    |
/// | IDecisionsSvc                  | InitiateWorkflow               | Initiates a workflow         |
/// | IAuditSvc                      | RecordAuditEvent               | Records an audit event       |   
/// | IFilerRepositorySvc            | MergeFiler                     | Records an audit event       |
#endregion

/// <summary>
/// Interface for the Filer Service.
/// </summary>
public interface IFilerSvc
{
    /// <summary>
    /// Adds a filer and associated filer user.
    /// </summary>
    /// <param name="filerRequest">The filer entity to add.</param>
    /// <returns>The ID of the created filer.</returns>
    [Post(AddFilerAsyncPath)]
    Task<long> AddFilerAsync(Filer filerRequest);
    const string AddFilerAsyncPath = "/ForServicesOnly";

    /// <summary>
    /// Updates an existing filer.
    /// </summary>
    /// <param name="filer">The filer to update.</param>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="UpdateFiler()"];
    /// IFilerSvc => IFilerRepository [label="Update()"];
    /// IFilerRepository >> IFilerSvc [label="return"];
    /// IFilerSvc >> Actor [label="return"];
    /// \endmsc
    public void UpdateFiler(Filer filer);

    /// <summary>
    /// Terminates a filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer to terminate.</param>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IDecisionsSvc [label="IDecision \n Svc"], IFilerRepository [label="IFiler \n Repository"], IAuditSvc [label="IAudit \n Svc"];
    /// Actor => IFilerSvc [label="TerminateFiler()"];
    /// IFilerSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IFilerSvc [label="return TOutput"];
    /// IFilerSvc => IFilerRepository [label="Update()"];
    /// IFilerRepository >> IFilerSvc [label="return"];
    /// IFilerSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IFilerSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IFilerSvc >> Actor [label="return"];
    /// \endmsc
    public void TerminateFiler(long filerId);

    /// <summary>
    /// Updates the status of a filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="status">The new status of the filer.</param>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="UpdateFilerStatus()"];
    /// IFilerSvc => IFilerRepository [label="Update()"];
    /// IFilerRepository >> IFilerSvc [label="return"];
    /// IFilerSvc >> Actor [label="return"];
    /// \endmsc
    public void UpdateFilerStatus(long filerId, FilerStatus status);

    /// <summary>
    /// Gets a filer by ID.
    /// </summary>
    /// <param name="filerId">The ID of the filer to retrieve.</param>
    /// <returns>The filer with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="GetFiler()"];
    /// IFilerSvc => IFilerRepository [label="Get()"];
    /// IFilerRepository >> IFilerSvc [label="return Filer"];
    /// IFilerSvc >> Actor [label="return Filer"];
    /// \endmsc
    public Task<Filer?> GetFiler(long filerId);

    /// <summary>
    /// Gets a filer by ID.
    /// </summary>
    /// <param name="filerId">The ID of the filer to retrieve.</param>
    /// <returns>Filer DTO object</returns>
    /// 
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="GetFiler()"];
    /// IFilerSvc => IFilerRepository [label="Get()"];
    /// IFilerRepository >> IFilerSvc [label="return Filer"];
    /// IFilerSvc >> Actor [label="return FilerDto"];
    /// \endmsc
    [Get(GetFilerDtoPath)]
    public Task<FilerDto> GetFilerDto(long filerId);
    const string GetFilerDtoPath = "/api/Filer/{filerId}";

    /// <summary>
    /// Gets all filers.
    /// </summary>
    /// <returns>A collection of all filers.</returns>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="GetAllFilers()"];
    /// IFilerSvc => IFilerRepository [label="GetAll()"];
    /// IFilerRepository >> IFilerSvc [label="return IEnumerable<Filer>"];
    /// IFilerSvc >> Actor [label="return IEnumerable<Filer>"];
    /// \endmsc
    public Task<IEnumerable<Filer>> GetAllFilers();

    /// <summary>
    /// Get all filers for a specific user.
    /// </summary>
    /// <param name="userId">The ID of the user.</param>
    /// <returns>A collection of filers.</returns>
    ///
    /// \msc
    /// Actor, IFilerSvc [label="IFiler \n Svc"], IFilerRepository [label="IFiler \n Repository"];
    /// Actor => IFilerSvc [label="GetAllByUserId()"];
    /// IFilerSvc => IFilerRepository [label="GetAllByUserId()"];
    /// IFilerRepository >> IFilerSvc [label="return IEnumerable<Filer>"];
    /// IFilerSvc >> Actor [label="return IEnumerable<Filer>"];
    /// \endmsc
    public Task<IEnumerable<Filer>> GetAllByUserId(long userId);

    /// <summary>
    /// Get FilerLink of a SMO registration application by FilerId and FilerLinkType Id
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <param name="filerLinkTypeId">ID of filer type link</param>
    /// <returns></returns>
    Task<FilerLink?> GetFilerLinkByFilerIdAndLinkTypeAsync(long filerId, long filerLinkTypeId);

    /// <summary>
    /// Add FilerLink that links to a registration
    /// </summary>
    /// <param name="filerLink"> FilerLink object</param>
    /// <returns></returns>
    Task AddFilerLinkAsync(FilerLink filerLink);

    /// <summary>
    /// Update a filer link
    /// </summary>
    /// <param name="filerLink"> FilerLink object</param>
    /// <returns></returns>
    Task UpdateFilerLinkAsync(FilerLink filerLink);

    /// <summary>
    /// Update role of a filer
    /// </summary>
    /// <param name="id"> ID of Filer User</param>
    /// <param name="filerRoleId">ID of filer role</param>
    /// <returns></returns>
    [Put(UpdateFilerUserRoleAsyncPath)]
    Task UpdateFilerUserRoleAsync(long filerUserId, long filerRoleId);
    const string UpdateFilerUserRoleAsyncPath = "FilerUser/{filerUserId}/FilerRole";

    /// <summary>
    /// Get filer user information
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <returns>Dto of Filer user information</returns>
    [Get(GetFilerUserAsyncPath)]
    Task<FilerUserDto?> GetFilerUserAsync(long filerId);
    const string GetFilerUserAsyncPath = "{filerId}/FilerUser";

    /// <summary>
    /// Get filer users information
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <returns>A collection of dto filer user information</returns>
    [Get(GetFilerUsersAsyncPath)]
    Task<List<FilerUserDto>> GetFilerUsersAsync(long filerId);
    const string GetFilerUsersAsyncPath = "{filerId}/FilerUsers";

    /// <summary>
    /// Get filer user information
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <param name="userId">ID of user</param>
    /// <returns>Filer user information</returns>
    [Get(GetFilerUserByUserIdAsyncPath)]
    Task<FilerUserDto?> GetFilerUserByUserIdAsync(long filerId, long userId);
    const string GetFilerUserByUserIdAsyncPath = "{filerId}/FilerUser/User/{userId}";

    /// <summary>
    /// Add new filer user
    /// </summary>
    /// <param name="filerUser">Filer user object</param>
    /// <returns>A dto object of filer user information</returns>
    [Post(AddFilerUserAsyncPath)]
    Task<FilerUserDto> AddFilerUserAsync(FilerUser filerUser);
    const string AddFilerUserAsyncPath = "{filerId}/FilerUser";

    /// <summary>
    /// Returns true if user initiating request has the filer role for the filer.
    /// </summary>
    /// <param name="filerId">Filer ID</param>
    /// <param name="filerRoleId">Filer Role ID</param>
    /// <returns>A dto object of filer user information</returns>
    [Get(CurrentUserHasFilerRolePath)]
    Task<bool> CurrentUserHasFilerRole(long filerId, long filerRoleId);
    const string CurrentUserHasFilerRolePath = "{filerId}/CurrentUserHasRole/{filerRoleId}";

    /// <summary>
    /// Find the registration ID of a filer at a specific point in time
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <param name="pointInTime">The point in time to compare against</param>
    /// <returns>ID of registration at that point</returns>
    [Get(GetFilerRegistrationAsOfAsyncPath)]
    Task<long> GetFilerRegistrationAsOfAsync(long filerId, DateTime pointInTime);
    const string GetFilerRegistrationAsOfAsyncPath = "/ForServicesOnly";


    /// <summary>
    /// Merge filer records based on primary and secondary filer IDs
    /// </summary>
    /// <param name="primaryFilerId">Primary filer ID</param>
    /// <param name="secondaryFilerId">Secondary filer ID</param>
    /// <returns>True if merge is successful, otherwise false</returns>
    /// \msc
    /// Actor, IFilerSvc [label="IFilerSvc"], IFilerRepositorySvc [label="IFilerRepositorySvc"];
    /// Actor => IFilerSvc [label="MergeFiler()"];
    /// IFilerSvc => IFilerRepositorySvc [label="MergeFiler()"];
    /// IFilerRepositorySvc >> IFilerSvc [label="\nreturn bool"];
    /// IFilerSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    [Post(MergeFilerPath)]
    public Task<bool> MergeFiler(long primaryFilerId, long secondaryFilerId);
    const string MergeFilerPath = "/Filers/MergeFiler";

    /// <summary>
    /// Deletes the specified filer
    /// </summary>
    /// <param name="filerId">ID of the filer to delete</param>
    /// <returns>True if deletion is successful, otherwise false</returns>
    /// \msc
    /// Actor, IFilerSvc [label="IFilerSvc"], IFilerRepositorySvc [label="IFilerRepositorySvc"];
    /// Actor => IFilerSvc [label="DeleteFiler()"];
    /// IFilerSvc => IFilerRepositorySvc [label="Update()"];
    /// IFilerRepositorySvc >> IFilerSvc [label="\nreturn bool"];
    /// IFilerSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    [Post(DeleteFilerPath)]
    public Task<bool> DeleteFiler(long filerId);
    const string DeleteFilerPath = "/Filers/DeleteFiler";

    /// <summary>
    /// Refers the specified filer to the FPPC by updating its status
    /// </summary>
    /// <param name="filerId">ID of the filer to refer</param>
    /// <returns>True if update is successful, otherwise false</returns>
    /// \msc
    /// Actor, IFilerSvc [label="IFilerSvc"], IFilerRepositorySvc [label="IFilerRepositorySvc"];
    /// Actor => IFilerSvc [label="ReferFilerToFPPC()"];
    /// IFilerSvc => IFilerRepositorySvc [label="Update()"];
    /// IFilerRepositorySvc >> IFilerSvc [label="\nreturn bool"];
    /// IFilerSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    [Post(ReferFilerToFPPCPath)]
    public Task<bool> ReferFilerToFPPC(long filerId);
    const string ReferFilerToFPPCPath = "/Filers/ReferFilerToFPPC";


}
