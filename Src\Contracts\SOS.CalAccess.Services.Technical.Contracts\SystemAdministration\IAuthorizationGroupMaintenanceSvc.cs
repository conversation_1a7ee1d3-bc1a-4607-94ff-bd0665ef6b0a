using Refit;
using SOS.CalAccess.Models.Authorization;


namespace SOS.CalAccess.Services.Technical.SystemAdministration;

/// <summary>
/// Interface Authorization Group Maintenance Service
/// </summary>
/// 
#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IAuthorizationGroupMaintenanceSvc provides methods to update the CARS database when a user add, update or remove the permission groups in the application.
/// </p>
/// <p>
/// Also list and view permission groups in the application from the database.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend business application layer service invoked by the web API layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Authorization is performed at the Web API services layer.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to add and update information from the solutions to the solutions DB for use in displaying the information through the various portals.
/// </p>
/// <h4>Feature</h4>
/// <ul>
/// <li>UA-02</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                               | Operation                    | Description                       |
/// | ------------------------------------- | ---------------------------- | --------------------------------- |
/// | IAuthorizationGroupRepository         | ListGroups                   | Gets list of groups               |
/// | IAuthorizationGroupRepository         | ViewGroup                    | View group details                |
/// | IAuthorizationGroupRepository         | AddGroup                     | Add group to user account         |
/// | IAuthorizationGroupRepository         | UpdateGroup                  | Update user group                 |
/// | IAuthorizationGroupRepository         | RemoveGroup                  | Remove user group                 |
/// | IPermissionRepository                 | AddPermissions               | Add permissions to group          |
#endregion
public interface IAuthorizationGroupMaintenanceSvc
{
    const string ListGroupsPath = "api/UserGroup/ListGroups";
    const string AddGroupPath = "api/UserGroup/AddGroup";
    const string ViewGroupPath = "api/UserGroup/ViewGroup";
    const string UpdateGroupPath = "api/UserGroup/UpdateGroup";
    const string RemoveGroupPath = "api/UserGroup/RemoveGroup";
    const string ListPermissionsPath = "api/UserGroup/ListPermissions";
    const string GetPermissionsPath = "api/UserGroup/GetAuthorizationGroupPermissionsByGroupId";
    const string RemovePermissionsPath = "api/UserGroup/RemoveAuthorizationGroupPermissions";


    /// <summary>
    /// Gets list of groups
    /// </summary>
    /// <returns>Task<IEnumerable<Group>></returns>
    /// 
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="ListGroups()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="GetAll()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + ListGroupsPath)]
    public Task<IEnumerable<MaintainAuthorizationGroupDto>> ListGroups();

    /// <summary>
    /// View group permissions of user account
    /// </summary>
    /// <param name="groupId"></param>
    /// <returns>Task<Group></returns>
    /// 
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="ViewGroup()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="GetById"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + ViewGroupPath)]
    public Task<MaintainAuthorizationGroupDto> ViewGroup(long groupId);

    /// <summary>
    /// Add group permissions to user account
    /// </summary>
    /// <param name="group"></param>
    /// 
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="AddGroup()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="Create()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + AddGroupPath)]
    public Task AddGroup(MaintainAuthorizationGroupDto group);

    /// <summary>
    /// Update group permissions 
    /// </summary>
    /// <param name="group"></param>
    /// 
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="UpdateGroup()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="Update()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + UpdateGroupPath)]
    public Task UpdateGroup(MaintainAuthorizationGroupDto group);

    /// <summary>
    /// Remove group 
    /// </summary>
    /// 
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="RemoveGroup()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="Delete()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + RemoveGroupPath)]
    public Task RemoveGroup(long groupId);

    /// <summary>
    /// Get authorization group permissions by group id
    /// </summary>
    /// <param name="groupId"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="GetAuthorizationGroupPermissionsByGroupId()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="GetAuthorizationGroupPermissionsByGroupId()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Get("/" + GetPermissionsPath)]
    public Task<List<AuthorizationGroupPermission>> GetAuthorizationGroupPermissionsByGroupId(long groupId);

    /// <summary>
    /// Remove authorization group permissions
    /// </summary>
    /// <param name="maintainAuthorizationGroupDto"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, IAuthorizationGroupMaintenanceSvc [label="IAuthorizationGroup \n MaintenanceSvc"], IAuthorizationGroupRepository [label="IAuthorizationGroup \n Repository"], IPermissionRepository [label="IPermission \n Repository"];
    /// Actor => IAuthorizationGroupMaintenanceSvc [label="RemoveAuthorizationGroupPermissions()"];
    /// IAuthorizationGroupMaintenanceSvc => IAuthorizationGroupRepository [label="RemoveAuthorizationGroupPermissions()"];
    /// IAuthorizationGroupRepository >> IAuthorizationGroupMaintenanceSvc [label="\nreturn "];
    /// IAuthorizationGroupMaintenanceSvc >> Actor [label="\nreturn "];
    /// \endmsc
    [Post("/" + RemovePermissionsPath)]
    public Task RemoveAuthorizationGroupPermissions(MaintainAuthorizationGroupDto maintainAuthorizationGroupDto);

}
