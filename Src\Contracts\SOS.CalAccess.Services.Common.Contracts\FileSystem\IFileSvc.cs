namespace SOS.CalAccess.Services.Common.FileSystem;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IFileSvc provides methods to interact with Azure Blob Storage REST API to store and retrieve files.
/// </p>
/// <p>
/// The implmentation will wrap the SDK provided by the Azure.Storage.Blobs package to provide consistent handling of authentication and authorization, configuration, retries, error handling, and other common concerns when interacting with Azure Blob Storage.
/// </p>
/// <p>
/// None of the methods support reading or writing to the local file system as the CARS solution primarily will be running in a containerized environment where the file system is assumed to be ephemeral.
/// </p>
/// <p>
/// In terms of Architecture Design this translates to backend common application layer service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to support the storage and retrieval of uploaded images, large json documents, pdfs, and other file based data in a persistent file store outside the database
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS8- File System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | Azure Blob Storage             | Read Blob |  Reads a blob from Azure Storage|
/// | Azure Blob Storage             | Write Blob | Writes a blob to Azure storage |
/// 
#endregion
public interface IFileSvc
{

    /// <summary>
    /// Create a file from the provided data stream in Azure Blob Storage.
    /// If a file already exists with the same filename in the container this method will throw an exception
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="data"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="POST"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task CreateFile(string container, string filename, Stream data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a Stream that when written to will create a file in Azure Blob Storage.
    /// If a file already exists with the same filename in the container this method will throw an exception
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="POST"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task<Stream> CreateFileStream(string container, string filename, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a file from the provided data stream in Azure Blob Storage.
    /// If a file already exists with the same filename in the container this method will delete the existing file and replace it with the new content
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="data"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="POST"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task OverwriteFile(string container, string filename, Stream data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a Stream that when written to will create a file in Azure Blob Storage.
    /// If a file already exists with the same filename in the container this method will delete the existing file and replace it with the new content
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="POST"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task<Stream> OverwriteFileStream(string container, string filename, CancellationToken cancellationToken = default);

    /// <summary>
    /// Read a file from Azure Blob Storage and write it to the supplied output stream
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="outputStream"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="GET"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task ReadFileIntoStream(string container, string filename, Stream outputStream, CancellationToken cancellationToken = default);

    /// <summary>
    /// Opean a stream to read a file from Azure Blob Storage
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="CreateFile()"];
    /// IFileSvc => AzureBlobStorage [label="GET"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task<Stream> ReadFile(string container, string filename, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a file in Azure Blob Storage.
    /// </summary>
    /// <param name="container"></param>
    /// <param name="filename"></param>
    /// <returns></returns>
    /// \msc
    /// Actor, IFileSvc, AzureBlobStorage;
    /// Actor => IFileSvc [label="DeleteBlobAsync()"];
    /// IFileSvc => AzureBlobStorage [label="POST"];
    /// AzureBlobStorage >> IFileSvc [label="return"];
    /// IFileSvc >> Actor [label="\nreturn "];
    /// \endmsc   
    Task DeleteBlobAsync(string container, string filename);
}
