openapi: 3.0.3
info:
  title: Form 498 - Slate Mailer Late Payment Report API
  description: API for submitting and managing Form 498 Slate Mailer Late Payment Reports.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/SlateMailer/LatePayments/{filerId}:
    post:
      summary: Submit Form 498 Slate Mailer Late Payment Report
      description: Submit a Form 498 report with details of late payments received by a slate mailer organization.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                submittedDate:
                 type: string
                 format: date
                 description: Date Stamp.
                latePaymentsReceived:
                  type: array
                  items:
                    $ref: '#/components/schemas/LatePaymentReceived'
                  description: List of late payments received.
                amendment:
                  $ref: './common-schemas.yaml#/components/schemas/Amendment'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:

    LatePaymentReceived:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the payor (if individual).
        lastName:
          type: string
          description: Last name of the payor (if individual).
        orgName:
          type: string
          description: Name of the organization (if payor is an organization).
        idNumber:
          type: string
          description: ID number of the payor (if applicable).
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        phone:
          type: string
          description: Filer's phone number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        fax:
          type: string
          description: Optional fax number as digits only.
          pattern: '^\d+$'
          maxLength: 40
        email:
          type: string
          format: email
          description: Filer's email address.
        transactionDate:
          type: string
          format: date
          description: Date the payment was received.
        amount:
          type: number
          format: float
          description: Amount of the late payment received.
        occupation:
          type: string
          description: Occupation of the payor (if the payor is self-employed, mark it as "self-employed").
        employer:
          type: string
          description: Employer of the payor (if the payor is self-employed, mark business name here).
        candidateOrMeasureDetails:
          type: array
          items:
            $ref: '#/components/schemas/CandidateOrMeasureDetails'
          description: Details about candidates or measures supported or opposed by the payment.

    CandidateOrMeasureDetails:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the candidate (if applicable).
        lastName:
          type: string
          description: Last name of the candidate (if applicable).
        name:
          type: string
          description: Name of the ballot measure (if applicable).
        officeSought:
          $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
        position:
          type: string
          enum:
            - Support
            - Oppose
          description: Whether the payment supports or opposes the candidate or measure.
        amountAttributed:
          type: number
          format: float
          description: Amount attributed to this candidate or measure.
