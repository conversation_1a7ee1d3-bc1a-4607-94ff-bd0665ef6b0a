using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
/// <summary>
//This dto is used for receiving contents of the form 400 and sending to the submission service
/// </summary>

public class EfileSlateMailerOrganizationDto
{
    public SlateMailerOrganization? SlateMailerOrg { get; set; }
    public long VendorUserId { get; set; }
    public string? AttesterName { get; set; }
    public string? AttesterRole { get; set; }
    public long? LinkedCommitteeId { get; set; }
}
