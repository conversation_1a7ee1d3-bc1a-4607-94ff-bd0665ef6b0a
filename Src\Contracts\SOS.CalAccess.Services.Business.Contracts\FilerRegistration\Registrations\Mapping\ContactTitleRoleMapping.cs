using System.Collections.Immutable;
using SOS.CalAccess.Models.Authorization;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;

public static class ContactTitleRoleMapping
{
    public static readonly ImmutableDictionary<string, long> TitleOrRoleNameToRoleId = new Dictionary<string, long>
    {
        [FilerRole.SlateMailerOrg_Treasurer.Name] = FilerRole.SlateMailerOrg_Treasurer.Id,
        [FilerRole.SlateMailerOrg_AssistantTreasurer.Name] = FilerRole.SlateMailerOrg_AssistantTreasurer.Id,
        [FilerRole.SlateMailerOrg_Officer.Name] = FilerRole.SlateMailerOrg_Officer.Id,
        [FilerRole.SlateMailerOrg_AccountManager.Name] = FilerRole.SlateMailerOrg_AccountManager.Id,
    }.ToImmutableDictionary();

    public static readonly ImmutableDictionary<long, string> RoleIdToName = new Dictionary<long, string>
    {
        [FilerRole.SlateMailerOrg_Treasurer.Id] = FilerRole.SlateMailerOrg_Treasurer.Name,
        [FilerRole.SlateMailerOrg_AssistantTreasurer.Id] = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
        [FilerRole.SlateMailerOrg_Officer.Id] = FilerRole.SlateMailerOrg_Officer.Name,
        [FilerRole.SlateMailerOrg_AccountManager.Id] = FilerRole.SlateMailerOrg_AccountManager.Name,
    }.ToImmutableDictionary();
}
