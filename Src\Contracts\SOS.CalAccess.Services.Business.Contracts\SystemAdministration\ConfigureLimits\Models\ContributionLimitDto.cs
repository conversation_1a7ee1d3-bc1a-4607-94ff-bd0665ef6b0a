namespace SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits.Models;
/// <summary>
///Contribution limits for a specific elected office and election cycle by date range and specific to Donor type
/// </summary>
public class ContributionLimitDto
{
    /// <summary>
    /// The elected office for which the contribution limit is being configured
    /// </summary>
    public long ElectedOffice { get; set; }         // e.g., "Governor", "Secretary of State" id's

    /// <summary>
    ///  The election cycle for the contribution limit
    /// </summary>
    public long ElectionCycle { get; set; }
    /// <summary>
    /// Start Date for Contribution Limit
    /// </summary>
    public DateTime StartDate { get; set; }
    /// <summary>
    /// End date for Contribution limit 
    /// </summary>
    public DateTime EndDate { get; set; }
    /// <summary>
    /// List of Contribution Limits by Type
    /// </summary>
    public List<DonorTypeAmountDto> ContributionLimits { get; set; } = [];

}
