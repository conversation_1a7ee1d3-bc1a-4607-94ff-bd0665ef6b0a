// <copyright file="AuthHeaderHandler.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Net.Http.Headers;

namespace SOS.CalAccess.FilerPortal.Authentication;

/// <summary>
/// A message handler that adds an Authorization header with a Bearer token to outgoing HTTP requests.
/// </summary>
/// <param name="httpContextAccessor">The HTTP context accessor to retrieve the current user's token.</param>
public class AuthHeaderHandler(IHttpContextAccessor httpContextAccessor) : DelegatingHandler
{
    /// <summary>
    /// Sends an HTTP request with an Authorization header containing a Bearer token.
    /// </summary>
    /// <param name="request">The HTTP request message.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>The HTTP response message.</returns>
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        var token = httpContextAccessor.HttpContext?.User.FindFirst(CustomClaimTypes.AccessToken)?.Value;

        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

        return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
    }
}
