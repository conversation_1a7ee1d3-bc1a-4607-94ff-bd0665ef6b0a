using SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits.Models;

namespace SOS.CalAccess.Services.Business.SystemAdministration.ConfigureLimits;
#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for handling configuration of ExpenditureLimits operations within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// It is assumed that the expenditure limit data provided is accurate and meets the required standards.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The main business function of this repository is to facilitate the creation, retrieval, updating, and deletion of expenditure limits.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SA-01: Configure System</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this repository.
/// </p>
/// | Service                      | Operation                            | Description                                             |
/// | --------------------------- | ------------------------------------ | ------------------------------------------------------- |
/// | IExpenditureLimitRepository | Create                                | Creates a new expenditure limit.                        |
/// | IExpenditureLimitRepository | Update                                | Updates an existing expenditure limit.                  |
/// | IExpenditureLimitRepository | GetAll                                | Retrieves all expenditure limits.                       |
/// | IExpenditureLimitRepository | Delete                                | Deletes a specific expenditure limit.                   |
/// | IExpenditureLimitRepository | FindById                              | Retrieves an expenditure limit by its ID.               |
/// | IExpenditureLimitRepository | FindByElectiveOffice                  | Retrieves expenditure limits by Elective Office ID.     |
#endregion
public interface IExpnditureLimitSvc
{
    /// <summary>
    /// Create an expenditure limit
    /// </summary>
    /// <param name="expenditureLimit">Expenditure limit</param>
    /// <returns>Expenditure limit dto</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="CreateExpenditureLimit()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="Create()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn ExpenditureLimit"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn ExpenditureLimitDto"];
    /// \endmsc
    Task<ExpenditureLimitDto> CreateExpenditureLimit(ExpenditureLimitDto expenditureLimit);


    /// <summary>
    /// Update an expenditure limit
    /// </summary>
    /// <param name="expenditureLimit">Expenditure limit</param>
    /// <returns>Expenditure limit dto</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="UpdateExpenditureLimit()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="Update()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn ExpenditureLimit"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn ExpenditureLimitDto"];
    /// \endmsc
    Task<ExpenditureLimitDto> UpdateExpenditureLimit(ExpenditureLimitDto expenditureLimit);


    /// <summary>
    /// Delete an expenditure limit
    /// </summary>
    /// <param name="expenditureLimit">Expenditure limit</param>
    /// <returns>true if deleted successfully</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="DeleteExpenditureLimit()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="Delete()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn bool"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn bool"];
    /// \endmsc
    public Task<bool> DeleteExpenditureLimit(ExpenditureLimitDto expenditureLimit);

    /// <summary>
    /// Get all expenditure limits
    /// </summary>
    /// <returns>List of expenditure limit DTOs</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="GetExpenditureLimits()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="GetAll()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn IEnumerable<ExpenditureLimit>"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn IEnumerable<ExpenditureLimitDto>"];
    /// \endmsc
    public Task<IEnumerable<ExpenditureLimitDto>> GetExpenditureLimits();


    /// <summary>
    /// Find an expenditure limit by ID
    /// </summary>
    /// <param name="id">Expenditure limit ID</param>
    /// <returns>Expenditure limit DTO</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="FindExpenditureLimitById()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="FindById()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn ExpenditureLimit"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn ExpenditureLimitDto"];
    /// \endmsc
    public Task<ExpenditureLimitDto> FindExpenditureLimitById(long id);


    /// <summary>
    /// Find expenditure limits by elective office ID
    /// </summary>
    /// <param name="electiveOfficeId">Elective office ID</param>
    /// <returns>List of expenditure limit DTOs</returns>
    /// \msc
    /// Actor, IExpenditureLimitSvc [label="IExpenditureLimitSvc"], IExpenditureLimitRepository [label="IExpenditureLimitRepository"];
    /// Actor => IExpenditureLimitSvc [label="FindExpenditureLimitByElectiveOffice()"];
    /// IExpenditureLimitSvc => IExpenditureLimitRepository [label="FindByElectiveOffice()"];
    /// IExpenditureLimitRepository >> IExpenditureLimitSvc [label="\nreturn IEnumerable<ExpenditureLimit>"];
    /// IExpenditureLimitSvc >> Actor [label="\nreturn IEnumerable<ExpenditureLimitDto>"];
    /// \endmsc
    IEnumerable<ExpenditureLimitDto> FindExpenditureLimitByElectiveOffice(long electiveOfficeId);

}
