using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.ControllerServices.SmoCampaignStatementCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using FilingSummaryType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using SmoRegistrationBasicResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationBasicResponseDto;

namespace SOS.CalAccess.FilerPortal.Controllers;

[Route("Filing/[controller]")]
[SuppressMessage("SonarQube", "S107:Methods should not have too many parameters", Justification = "Will refactor later")]
public class SmoCampaignStatementController(
    ISmoCampaignStatementCtlSvc smoCampaignStatementCtlSvc,
    ISmoCampaignStatementSvc smoCampaignStatementSvc,
    ISmoRegistrationSvc smoRegistrationSvc,
    Generated.IContactsApi contactsApi,
    IStringLocalizer<SharedResources> localizer,
    IAuthorizationSvc authorizationSvc,
    IToastService toastService,
    ICandidateSvc candidateSvc,
    IAccuMailValidatorService accuMailValidatorService,
    IBallotMeasureSvc ballotMeasureSvc) : Controller
{
    /// <summary>
    /// Common async logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    /// <param name="next"></param>
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var isAmending = false;

        // Cases for when we get the filingId from query (usually from doing GET of pages)
        if (context.ActionArguments.TryGetValue("filingId", out var filingIdObj))
        {
            var filingId = Convert.ToInt64(filingIdObj, CultureInfo.InvariantCulture);
            isAmending = await smoCampaignStatementCtlSvc.IsSmoCampaignStatementAmendment(filingId);
        }
        // Cases for when we get the filingId from model or parameters (usually from doing POST of pages)
        else if (context.ActionArguments.TryGetValue("model", out var argument)
            || context.ActionArguments.TryGetValue("parameters", out argument))
        {
            var idProperty = argument?.GetType().GetProperty("Id");
            if (idProperty != null)
            {
                var idValue = idProperty.GetValue(argument);
                if (idValue is long filingId)
                {
                    isAmending = await smoCampaignStatementCtlSvc.IsSmoCampaignStatementAmendment(filingId);
                }
            }
        }
        SetCommonViewData(isAmending);
        await next();
    }

    public IActionResult Index()
    {
        // Page01 of the controller = Selection page
        return RedirectToAction(nameof(Selection));
    }

    [HttpGet(nameof(RedirectToDashboard))]
    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "DisclosureTemporaryDashboard");
    }

    /// <summary>
    /// Redirects user to the starting page when editing this form.
    /// </summary>
    /// <param name="id">Filing ID</param>
    /// <returns></returns>
    [HttpPost(nameof(Cancel))]

    public async Task<IActionResult> Cancel(
    [Required] long id,
    CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            await smoCampaignStatementCtlSvc.CancelSmoCampaignStatementDraft(id);
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
            return RedirectToDashboard();
        }
        return NotFound();
    }

    /// <summary>
    /// Closes form without changes and routes the user to the Review screen.
    /// </summary>
    /// <param name="action"></param>
    /// <returns></returns>
    [HttpPost(nameof(CancelDiscardDraft))]
    public IActionResult CancelDiscardDraft(
        [Required] long id,
        FormAction? action = null)
    {
        if (ModelState.IsValid)
        {
            SetLocalizedToast(CommonResourceConstants.ToastCanceled);
            return RedirectToAction(nameof(Review), new { filingId = id });
        }
        return NotFound();
    }

    /// <summary>
    /// Redirects user to the starting page when editing this form.
    /// </summary>
    /// <param name="filingId">Filing ID</param>
    /// <returns></returns>
    [HttpGet(nameof(Edit))]
    public IActionResult Edit([Required] long filingId)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return RedirectToAction(nameof(Review), new { filingId });
    }

    private void SetCommonViewData(bool isAmending = false)
    {
        ViewData[LayoutConstants.Title] = !isAmending ? localizer[ResourceConstants.SmoCampaignStatementTitle].Value : localizer[ResourceConstants.SmoCampaignStatementAmendTitle].Value;
        ViewData[LayoutConstants.Subtitle] = localizer[ResourceConstants.SmoCampaignStatementSubtitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new("Filer Portal", "/FilerPortal"),
            new("Slate Mailer", "/Filing/SmoCampaignStatement"),
        };
    }

    #region Selection (FD-CF-SMO-Selection)
    /// <summary>
    /// Loader for FD-CF-SMO-Selection page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(Selection))]
    public IActionResult Selection()
    {
        return View(new SmoCampaignStatementSelectionViewModel());
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost(nameof(Selection))]
    public async Task<IActionResult> Selection(
    SmoCampaignStatementSelectionViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Create:
                    return await SelectionCreate(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    [HttpPost(nameof(SelectionCreate))]
    private async Task<IActionResult> SelectionCreate(SmoCampaignStatementSelectionViewModel model)
    {
        if (!model.RegistrationId.HasValue && !model.FilerId.HasValue)
        {
            ModelState.AddModelError("RegistrationId", string.Format(CultureInfo.InvariantCulture, localizer[ResourceConstants.SmoCampaignStatementSelectionSearchErrorMessage].Value));
            return View(nameof(Selection), new SmoCampaignStatementSelectionViewModel());
        }

        // If model state is valid and id exist, create the DisclosureFiling record and navigate to the Review Page
        if (ModelState.IsValid && model.RegistrationId.HasValue && model.FilerId.HasValue)
        {
            long? filingId = await smoCampaignStatementCtlSvc.CreateSmoCampaignStatement((long)model.RegistrationId);
            return RedirectToAction(nameof(Review), new { filingId });
        }

        return View(nameof(Selection), new SmoCampaignStatementSelectionViewModel());
    }

    /// <summary>
    /// Search Slate Mailer By ID or Name
    /// </summary>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Json result of committee information</returns>
    [HttpGet(nameof(SearchSmoByIdOrName))]
    public async Task<JsonResult> SearchSmoByIdOrName([FromQuery] string search, CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            // statusId for SearchSmoIdOrNameAsync is Accepted by default - pass in null
            var result = await smoRegistrationSvc.SearchSmoRegistrationByIdOrNameAsync(search, null);

            return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
        }

        return new JsonResult(new List<SmoRegistrationBasicResponseDto>());
    }

    #endregion

    #region Review
    /// <summary>
    /// Loader for FD-CF-SMO-Overview page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(Review))]
    public async Task<IActionResult> Review(long filingId, SmoCampaignStatementReviewViewModel? model = null)
    {
        ModelState.Remove("Action"); // No FormAction being performed
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // if selectedFilingPeriod has a value then call ctlSvc to get remaining SmoCampaignStatementReviewViewModel
        if (ModelState.IsValid && model != null)
        {
            var completeModel = await smoCampaignStatementCtlSvc.GetCompleteReviewPageViewModel(filingId);

            var isAuthorized = await authorizationSvc.IsAuthorized(new AuthorizationRequest(Permission.Disclosure_401_Attest, User, filerId: completeModel!.FilerId));

            if (isAuthorized)
            {
                completeModel.IsUserAuthorizedToAttest = true;
            }

            return View(completeModel);
        }

        // Retrieve unreported filing periods
        var unreportedFilingPeriods = await smoCampaignStatementCtlSvc.GetUnreportedFilingPeriods(filingId);

        var newModel = new SmoCampaignStatementReviewViewModel()
        {
            Id = filingId,
            FilingPeriodOptions = unreportedFilingPeriods
        };
        return View(newModel);
    }

    [HttpPost(nameof(Review))]
    public IActionResult Review(SmoCampaignStatementReviewViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Submit:
                    return RedirectToAction(nameof(Verification), new { filingId = model.Id });
                case FormAction.SaveAndClose:
                    return SaveReview();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    /// <summary>
    /// Call to update the Review.cshtml view model once filing period has been selected
    /// </summary>
    /// <returns> Redirect to Review page (with or without selectedFilingPeriod) </returns>
    [HttpPost(nameof(SelectFilingPeriodOption))]
    public async Task<IActionResult> SelectFilingPeriodOption(SmoCampaignStatementReviewViewModel model, long? selectedFilingPeriodId)
    {
        ModelState.Remove("Action"); // No FormAction being performed when we select a filing period

        if (ModelState.IsValid && selectedFilingPeriodId != null)
        {
            await smoCampaignStatementCtlSvc.UpdateSmoCampaignStatement(model.Id, selectedFilingPeriodId);
            return RedirectToAction(nameof(Review), new { filingId = model.Id });
        }
        return RedirectToAction(nameof(Review), new { filingId = model.Id, model });
    }

    /// <summary>
    /// Set Saved message toast and redirect to dashboard.
    /// </summary>
    /// <returns>Redirect to dashboard</returns>
    [HttpGet(nameof(SaveReview))]
    public IActionResult SaveReview()
    {
        SetLocalizedToast(CommonResourceConstants.SavedMessage);

        return RedirectToDashboard();
    }

    /// <summary>
    /// Call to mark a filing summary as nothing to report. Update review page.
    /// </summary>
    /// <returns> Redirect to Review page (with or without selectedFilingPeriod) </returns>
    [HttpGet(nameof(MarkFilingSummaryAsNothingToReport))]
    public async Task<IActionResult> MarkFilingSummaryAsNothingToReport(long? filingId, long? filingSummaryId)
    {
        ModelState.Remove("Action"); // No FormAction being performed when we select a filing period

        if (ModelState.IsValid && filingSummaryId != null)
        {
            await smoCampaignStatementCtlSvc.MarkFilingSummaryAsNothingToReport(filingId, filingSummaryId);
            return RedirectToAction(nameof(Review), new { filingId });
        }
        return RedirectToAction(nameof(Review), new { filingId });
    }

    #endregion

    #region General Information
    /// <summary>
    /// Loader for General Information page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(GeneralInformation))]
    public async Task<IActionResult> GeneralInformation(long filingId)
    {
        var model = await smoCampaignStatementCtlSvc.GetGeneralInformationPageViewModel(filingId);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        return View(NotFound());

    }

    [HttpPost(nameof(GeneralInformation))]
    public IActionResult GeneralInformation(
        SmoCampaignStatementGeneralInfoViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return RedirectToAction(nameof(Review), new { filingId = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Filing Summary
    /// <summary>
    /// Loader for Filing Summary page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(FilingSummary))]
    public async Task<IActionResult> FilingSummary(long filingId)
    {
        var model = await smoCampaignStatementCtlSvc.GetFilingSummaryPageViewModel(filingId);

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View(model);
    }

    [HttpPost(nameof(FilingSummary))]
    public IActionResult FilingSummary(
        SmoCampaignStatementFilingSummaryViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return RedirectToAction(nameof(Review), new { filingId = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Transaction Summary (Payments Received / Payments Made / Payments made by agent or independent contractor / Persons receiving $1000 / Candidates and measures not listed)
    [HttpGet(nameof(TransactionSummary))]
    public async Task<IActionResult> TransactionSummary(long filingId, string summaryType)
    {
        if (ModelState.IsValid)
        {
            SmoCampaignStatementTransactionSummaryViewModel model;

            // Render the transaction summary based on the summaryType value

            switch (summaryType)
            {
                case var type when type == FilingSummaryType.PaymentReceivedSummary.Name:
                    model = await smoCampaignStatementCtlSvc.GetPaymentsReceived(filingId);
                    return View(model);
                case var type when type == FilingSummaryType.PaymentMadeSummary.Name:
                    model = await smoCampaignStatementCtlSvc.GetPaymentsMade(filingId);
                    return View(model);
                case var type when type == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name:
                    model = await smoCampaignStatementCtlSvc.GetPaymentsMadeByAnAgentOrIndependentContractor(filingId);
                    return View(model);
                case var type when type == FilingSummaryType.PersonReceiving1000OrMoreSummary.Name:
                    model = await smoCampaignStatementCtlSvc.GetPersonsReceiving1000OrMore(filingId);
                    return View(model);
                case var type when type == FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name:
                    model = await smoCampaignStatementCtlSvc.GetCandidatesAndMeasureOnPaymentsReceived(filingId);
                    return View(model);
                default:
                    return View(new SmoCampaignStatementTransactionSummaryViewModel());
            }
        }
        return NotFound();

    }

    [HttpPost(nameof(TransactionSummary))]
    public async Task<IActionResult> TransactionSummary(SmoCampaignStatementTransactionSummaryViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await TransactionSummarySave(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    [HttpPost(nameof(TransactionSummarySave))]
    public async Task<IActionResult> TransactionSummarySave(SmoCampaignStatementTransactionSummaryViewModel model)
    {
        // If transaction summary is of type Payment Received or Payment Made, save and run decisions for unitemized amount
        if (model.SummaryType == FilingSummaryType.PaymentReceivedSummary.Name || model.SummaryType == FilingSummaryType.PaymentMadeSummary.Name)
        {
            await smoCampaignStatementCtlSvc.SubmitTransactionSummaryUnitemizedPaymentForm(model, ModelState);
        }

        if (ModelState.IsValid)
        {
            SetLocalizedToast(CommonResourceConstants.SavedMessage);
            return RedirectToAction(nameof(Review), new { filingId = model.Id });
        }

        if (!ModelState.IsValid && model.Id.HasValue && model.FilerId.HasValue && model.FilingSummaryId.HasValue)
        {
            if (model.SummaryType == FilingSummaryType.PaymentReceivedSummary.Name)
            {
                var newModel = await smoCampaignStatementCtlSvc.GetPaymentsReceived(model.Id.Value);
                newModel.UnitemizedPaymentLessThan100 = model.UnitemizedPaymentLessThan100; // retain the original input that triggered Decisions error
                return View(nameof(TransactionSummary), newModel);
            }
            else
            {
                var newModel = await smoCampaignStatementCtlSvc.GetPaymentsMade(model.Id.Value);
                newModel.UnitemizedPaymentLessThan100 = model.UnitemizedPaymentLessThan100; // retain the original input that triggered Decisions error
                return View(nameof(TransactionSummary), newModel);
            }
        }
        return NotFound();
    }
    #endregion

    #region Candidates Measures Not Listed 01
    /// <summary>
    /// Loader for Candidates Measures Not Listed 01 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(CandidatesMeasuresNotListed01))]
    public async Task<IActionResult> CandidatesMeasuresNotListed01([FromQuery] CandidatesMeasuresNotListedParameters parameters)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetCandidatesMeasuresNotListedViewModel(parameters);

        return View(model);
    }

    [HttpPost(nameof(CandidatesMeasuresNotListed01))]
    public async Task<IActionResult> CandidatesMeasuresNotListed01(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToTransactionSummary(model.Id, FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name);
                case FormAction.Continue:
                    return await CandidatesMeasuresNotListed01Continue(model);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private async Task<IActionResult> CandidatesMeasuresNotListed01Continue(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.ValidateCandidateOrMeasureNotListedAsync(model, ModelState);

        if (!ModelState.IsValid)
        {
            return View(nameof(CandidatesMeasuresNotListed01), model);
        }

        return RedirectToAction(nameof(CandidatesMeasuresNotListed02), new
        {
            filingId = model.Id,
            jurisdiction = model.Jurisdiction!,
            pertainsTo = model.PertainsTo!,
            position = model.Position!,
            disclosureWithoutPaymentId = model.DisclosureWithoutPaymentId,
        });
    }
    #endregion

    #region Candidates Measures Not Listed 02
    /// <summary>
    /// Loader for Candidates Measures Not Listed 02 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(CandidatesMeasuresNotListed02))]
    public async Task<IActionResult> CandidatesMeasuresNotListed02([FromQuery] CandidatesMeasuresNotListedParameters parameters)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (string.IsNullOrWhiteSpace(parameters.Jurisdiction) ||
            string.IsNullOrWhiteSpace(parameters.PertainsTo) ||
            string.IsNullOrWhiteSpace(parameters.Position))
        {
            return RedirectToAction(nameof(CandidatesMeasuresNotListed01));
        }

        var model = await smoCampaignStatementCtlSvc.GetCandidatesMeasuresNotListedViewModel(parameters);

        return View(model);
    }

    [HttpPost(nameof(CandidatesMeasuresNotListed02))]
    public async Task<IActionResult> CandidatesMeasuresNotListed02(
        SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToAction(nameof(CandidatesMeasuresNotListed01), new
                    {
                        filingId = model.Id,
                        jurisdiction = model.Jurisdiction!,
                        pertainsTo = model.PertainsTo!,
                        position = model.Position!,
                        disclosureWithoutPaymentId = model.DisclosureWithoutPaymentId,
                    });
                case FormAction.SaveAndClose:
                    return await CandidatesMeasuresNotListed02Save(model);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private async Task<IActionResult> CandidatesMeasuresNotListed02Save(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.SaveCandidateOrMeasureWithoutPaymentReceivedAsync(model, ModelState);

        if (!ModelState.IsValid)
        {
            return View(nameof(CandidatesMeasuresNotListed02), model);
        }

        return SaveAndRedirectToTransactionSummary(model.Id, FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name);
    }
    #endregion

    #region Payment Received 01
    /// <summary>
    /// Loader for Payment Received 01 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentReceived01))]
    public async Task<IActionResult> PaymentReceived01([Required] long filingId, [Required] long filerId, long? contactId, long? transactionId)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetFilerContactViewModelAsync(filingId, filerId, contactId, transactionId, TransactionType.PaymentReceived.Name);
        model.ScreenType = SmoCampaignStatementScreenTypes.PaymentReceived;

        return View(model);
    }

    [HttpPost(nameof(PaymentReceived01))]
    public IActionResult PaymentReceived01(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentReceivedSummary.Name);
                case FormAction.Continue:
                    return PaymentReceived01Continue(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private IActionResult PaymentReceived01Continue(SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (model.ContactId is null)
        {
            ModelState.AddModelError(nameof(model.ContactId), string.Format(CultureInfo.InvariantCulture, localizer[ResourceConstants.FieldIsRequired].Value, "This field"));
            return View(nameof(PaymentReceived01), model);
        }

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(PaymentReceived02), new
            {
                filingId = model.Id,
                filerId = model.FilerId,
                contactId = model.ContactId,
                transactionId = model.TransactionId
            });
        }

        return View(nameof(PaymentReceived01), model);
    }
    #endregion

    #region Payment Received 02
    /// <summary>
    /// Loader for Payment Received 02 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentReceived02))]
    public async Task<IActionResult> PaymentReceived02([Required] long filingId, long? filerId, long? contactId, long? transactionId)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetFilerContactViewModelAsync(filingId, filerId, contactId, transactionId, TransactionType.PaymentReceived.Name);
        model.ScreenType = SmoCampaignStatementScreenTypes.PaymentReceived;

        return View(model);
    }

    [HttpPost(nameof(PaymentReceived02))]
    public async Task<IActionResult> PaymentReceived02(
        SmoCampaignStatementTransactionEntryViewModel model,
        CancellationToken cancellationToken)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToAction(nameof(PaymentReceived01), new
                    {
                        filingId = model.Id,
                        filerId = model.FilerId,
                        contactId = model.ContactId,
                        transactionId = model.TransactionId
                    });
                case FormAction.Continue:
                    return await PaymentReceived02Continue(model, cancellationToken);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentReceivedSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    private async Task<IActionResult> PaymentReceived02Continue(
        SmoCampaignStatementTransactionEntryViewModel model,
        CancellationToken cancellationToken)
    {

        if (string.IsNullOrWhiteSpace(model.ParticipantType))
        {
            ModelState.AddModelError(nameof(SmoCampaignStatementTransactionEntryViewModel.ParticipantType), string.Format(CultureInfo.InvariantCulture, localizer[ResourceConstants.FieldIsRequired].Value, "This field"));
            return View(nameof(PaymentReceived02), model);
        }

        PopulateAddresses(model);

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(nameof(PaymentReceived02), model);
        }

        PopulateAddressesBackToModel(model);

        await smoCampaignStatementCtlSvc.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, ModelState, cancellationToken);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(PaymentReceived03), new
            {
                filingId = model.Id,
                contactId = model.ContactId,
                transactionId = model.TransactionId
            });
        }

        return View(nameof(PaymentReceived02), model);
    }
    #endregion

    #region Payment Received 03
    /// <summary>
    /// Loader for Payment Received 03 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentReceived03))]
    public async Task<IActionResult> PaymentReceived03([FromQuery] PaymentReceivedParameters parameters)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetPaymentReceived03ViewModelAsync(parameters);
        model.ScreenType = SmoCampaignStatementScreenTypes.PaymentReceived;

        return View(model);
    }

    [HttpPost(nameof(PaymentReceived03))]
    public async Task<IActionResult> PaymentReceived03(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(PaymentReceived02), new { filingId = model.Id, contactId = model.ContactId, transactionId = model.TransactionId });
            case FormAction.Continue:
                return await PaymentsReceived03Continue(model);
            case FormAction.Cancel:
                return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentReceivedSummary.Name);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }

    private async Task<IActionResult> PaymentsReceived03Continue(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.ValidatePaymentReceivedRequestAsync(model, ModelState);

        if (!ModelState.IsValid)
        {
            return View(nameof(PaymentReceived03), model);
        }

        return RedirectToAction(nameof(PaymentReceived04), new
        {
            filingId = model.Id,
            contactId = model.ContactId.GetValueOrDefault(),
            jurisdiction = model.Jurisdiction!,
            pertainsTo = model.PertainsTo!,
            position = model.Position!,
            notes = model.Notes,
            transactionAmount = model.TransactionAmount,
            transactionDate = model.TransactionDate.GetValueOrDefault(),
            transactionId = model.TransactionId,
            attachedFileGuidsJson = model.AttachedFileGuidsJson
        });
    }
    #endregion

    #region Payment Received 04
    /// <summary>
    /// Loader for Payment Received 04 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentReceived04))]
    public async Task<IActionResult> PaymentReceived04([FromQuery] PaymentReceivedParameters parameters)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (string.IsNullOrWhiteSpace(parameters.Jurisdiction) || string.IsNullOrWhiteSpace(parameters.PertainsTo))
        {
            return RedirectToAction(nameof(PaymentReceived03), new
            {
                filingId = parameters.Id,
                contactId = parameters.ContactId,
                transactionId = parameters.TransactionId,
                attachedFileGuidsJson = parameters.AttachedFileGuidsJson,
            });
        }

        var model = await smoCampaignStatementCtlSvc.GetPaymentReceived04ViewModelAsync(parameters);
        model.ScreenType = SmoCampaignStatementScreenTypes.PaymentReceived;

        _ = (ModelState?.Remove(nameof(model.TransactionAmount)));

        return View(model);
    }

    [HttpPost(nameof(PaymentReceived04))]
    public async Task<IActionResult> PaymentReceived04(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(PaymentReceived03), new
                {
                    filingId = model.Id,
                    contactId = model.ContactId.GetValueOrDefault(),
                    jurisdiction = model.Jurisdiction!,
                    pertainsTo = model.PertainsTo!,
                    position = model.Position!,
                    notes = model.Notes,
                    transactionAmount = model.TransactionAmount,
                    transactionDate = model.TransactionDate.GetValueOrDefault(),
                    transactionId = model.TransactionId,
                });
            case FormAction.SaveAndClose:
                return await PaymentReceived04Save(model);
            case FormAction.Cancel:
                return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentReceivedSummary.Name);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                break;
        }

        return View(model);
    }

    private async Task<IActionResult> PaymentReceived04Save(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.SaveTransactionAsync(model, ModelState, TransactionType.PaymentReceived.Name);

        if (!ModelState.IsValid)
        {
            return View(nameof(PaymentReceived04), model);
        }

        return SaveAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentReceivedSummary.Name);
    }
    #endregion

    #region Payment Made 01
    /// <summary>
    /// Loader for Payment Made 01 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentMade01))]
    public async Task<IActionResult> PaymentMade01([Required] long filingId, [Required] long filerId, long? contactId, long? transactionId, bool isPaymentMadeByAgent = false)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetFilerContactViewModelAsync(filingId, filerId, contactId, transactionId, TransactionType.PaymentMade.Name);
        model.ScreenType = ResolvePaymentMadeScreenType(isPaymentMadeByAgent);

        return View(model);
    }

    [HttpPost(nameof(PaymentMade01))]
    public IActionResult PaymentMade01(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, ResolvePaymentMadeFilingSummaryType(model.ScreenType));
                case FormAction.Continue:
                    return RedirectToAction(nameof(PaymentMade02), new
                    {
                        filingId = model.Id,
                        filerId = model.FilerId,
                        transactionId = model.TransactionId,
                        contactId = model.ContactId,
                        isPaymentMadeByAgent = IsPaymentMadeByAgent(model.ScreenType),
                    });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Payment Made 02
    /// <summary>
    /// Loader for Payment Made 02 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentMade02))]
    public async Task<IActionResult> PaymentMade02([Required] long filingId, long? filerId, long? contactId, long? transactionId, bool isPaymentMadeByAgent = false)
    {
        var model = await smoCampaignStatementCtlSvc.GetFilerContactViewModelAsync(filingId, filerId, contactId, transactionId, TransactionType.PaymentMade.Name);
        model.ScreenType = ResolvePaymentMadeScreenType(isPaymentMadeByAgent);

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    [HttpPost(nameof(PaymentMade02))]
    public async Task<IActionResult> PaymentMade02(
        SmoCampaignStatementTransactionEntryViewModel model,
        CancellationToken cancellationToken)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, ResolvePaymentMadeFilingSummaryType(model.ScreenType));
                case FormAction.Continue:
                    return await PaymentMade02Continue(model, cancellationToken);
                case FormAction.Previous:
                    return RedirectToAction(nameof(PaymentMade01), new
                    {
                        filingId = model.Id,
                        transactionId = model.TransactionId,
                        filerId = model.FilerId,
                        contactId = model.ContactId,
                        isPaymentMadeByAgent = IsPaymentMadeByAgent(model.ScreenType),
                    });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> PaymentMade02Continue(
        SmoCampaignStatementTransactionEntryViewModel model,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(model.ParticipantType))
        {
            ModelState.AddModelError(nameof(SmoCampaignStatementTransactionEntryViewModel.ParticipantType), string.Format(CultureInfo.InvariantCulture, localizer[ResourceConstants.FieldIsRequired].Value, "This field"));
            return View(nameof(PaymentMade02), model);
        }

        PopulateAddresses(model);

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(nameof(PaymentMade02), model);
        }

        PopulateAddressesBackToModel(model);

        await smoCampaignStatementCtlSvc.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayeeFilerContactForm, ModelState, cancellationToken);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(PaymentMade03), new
            {
                filingId = model.Id,
                contactId = model.ContactId,
                transactionId = model.TransactionId,
                isPaymentMadeByAgent = IsPaymentMadeByAgent(model.ScreenType),
            });
        }

        return View(nameof(PaymentMade02), model);
    }
    #endregion

    #region Payment Made 03
    /// <summary>
    /// Loader for Payment Made 03 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PaymentMade03))]
    public async Task<IActionResult> PaymentMade03([Required] long filingId, [Required] long contactId, long? transactionId, bool isPaymentMadeByAgent = false)
    {
        var model = await smoCampaignStatementCtlSvc.GetPaymentMade03ViewModelAsync(filingId, contactId, transactionId);
        model.ScreenType = ResolvePaymentMadeScreenType(isPaymentMadeByAgent);

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    [HttpPost(nameof(PaymentMade03))]
    public async Task<IActionResult> PaymentMade03(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(PaymentMade02), new
                {
                    filingId = model.Id,
                    contactId = model.ContactId,
                    transactionId = model.TransactionId,
                    isPaymentMadeByAgent = IsPaymentMadeByAgent(model.ScreenType),
                });
            case FormAction.SaveAndClose:
                return await PaymentMade03SaveAsync(model);
            case FormAction.Cancel:
                return CancelAndRedirectToTransactionSummary(model.Id, ResolvePaymentMadeFilingSummaryType(model.ScreenType));
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }

    private async Task<IActionResult> PaymentMade03SaveAsync(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.SaveTransactionAsync(model, ModelState, TransactionType.PaymentMade.Name);

        if (!ModelState.IsValid)
        {
            // Remap the CodeOptions again
            model.CodeOptions = await smoCampaignStatementCtlSvc.GetPaymentCodeListDropdown();
            return View(nameof(PaymentMade03), model);
        }

        if (model.IsPaidByAgentOrContractor)
        {
            return SaveAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name);
        }
        else
        {
            return SaveAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PaymentMadeSummary.Name);
        }
    }
    #endregion

    #region Person Receiving 01
    /// <summary>
    /// Loader for Person Receiving 01 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PersonReceiving01))]
    public async Task<IActionResult> PersonReceiving01(PersonReceivingParameters parameters)
    {
        var model = await smoCampaignStatementCtlSvc.GetPersonReceivingFilerContactViewModelAsync(
            parameters.Id,
            parameters.ContactId,
            parameters.TransactionId,
            parameters.OfficerId,
            parameters.RegistrationId,
            parameters.FilerId);

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View(model);
    }

    [HttpPost(nameof(PersonReceiving01))]
    public async Task<IActionResult> PersonReceiving01(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return await PersonReceiving01Continue(model);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PersonReceiving1000OrMoreSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> PersonReceiving01Continue(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.ValidatePersonReceivingOfficerRequestAsync(model, ModelState);

        if (!ModelState.IsValid)
        {
            return View(nameof(PersonReceiving01), model);
        }

        return RedirectToAction(nameof(PersonReceiving02), new
        {
            filingId = model.Id,
            transactionId = model.TransactionId,
            filerId = model.FilerId,
            contactId = model.ContactId,
            officerId = model.OfficerId,
            registrationId = model.RegistrationId
        });
    }
    #endregion

    #region Person Receiving 02
    /// <summary>
    /// Loader for Person Receiving 02 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PersonReceiving02))]
    public async Task<IActionResult> PersonReceiving02(PersonReceivingParameters parameters)
    {
        var model = await smoCampaignStatementCtlSvc.GetPersonReceivingFilerContactViewModelAsync(
            parameters.Id,
            parameters.ContactId,
            parameters.TransactionId,
            parameters.OfficerId,
            parameters.RegistrationId,
            parameters.FilerId);

        if (ModelState.IsValid)
        {
            ModelState.Clear();
            return View(model);
        }

        return NotFound();
    }

    [HttpPost(nameof(PersonReceiving02))]
    public async Task<IActionResult> PersonReceiving02(SmoCampaignStatementTransactionEntryViewModel model, CancellationToken cancellationToken)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToAction(nameof(PersonReceiving01), new
                    {
                        filingId = model.Id,
                        transactionId = model.TransactionId,
                        filerId = model.FilerId,
                        contactId = model.ContactId,
                        officerId = model.OfficerId,
                        registrationId = model.RegistrationId,
                    });
                case FormAction.Continue:
                    return await PersonReceiving02Continue(model, cancellationToken);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PersonReceiving1000OrMoreSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> PersonReceiving02Continue(
        SmoCampaignStatementTransactionEntryViewModel model,
        CancellationToken cancellationToken)
    {
        await smoCampaignStatementCtlSvc.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.OfficerFilerContactForm, ModelState, cancellationToken);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(PersonReceiving03), new
            {
                filingId = model.Id,
                contactId = model.ContactId,
                transactionId = model.TransactionId,
            });
        }

        return View(nameof(PersonReceiving02), model);
    }
    #endregion

    #region Person Receiving 03
    /// <summary>
    /// Loader for Person Receiving 03 page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(PersonReceiving03))]
    public async Task<IActionResult> PersonReceiving03([Required] long filingId, [Required] long contactId, long? transactionId)
    {
        var model = await smoCampaignStatementCtlSvc.GetPersonReceiving03ViewModelAsync(filingId, contactId, transactionId);

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    [HttpPost(nameof(PersonReceiving03))]
    public async Task<IActionResult> PersonReceiving03(SmoCampaignStatementTransactionEntryViewModel model)
    {
        ClearModelWhenCancelTransactionEntry(model);

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToAction(nameof(PersonReceiving02), new
                    {
                        filingId = model.Id,
                        contactId = model.ContactId,
                        transactionId = model.TransactionId,
                    });
                case FormAction.SaveAndClose:
                    return await PersonReceiving03SaveAsync(model);
                case FormAction.Cancel:
                    return CancelAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PersonReceiving1000OrMoreSummary.Name);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> PersonReceiving03SaveAsync(SmoCampaignStatementTransactionEntryViewModel model)
    {
        await smoCampaignStatementCtlSvc.SaveTransactionAsync(model, ModelState, TransactionType.PersonReceiving1000OrMore.Name);

        if (!ModelState.IsValid)
        {
            return View(nameof(PersonReceiving03), model);
        }

        return SaveAndRedirectToTransactionSummary(model.Id, FilingSummaryType.PersonReceiving1000OrMoreSummary.Name);
    }
    #endregion

    #region Verification
    /// <summary>
    /// Loader for Verification page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(Verification))]
    public async Task<IActionResult> Verification(long filingId, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var smoCampaignStatement = await smoCampaignStatementSvc.GetSmoCampaignStatementOverviewAsync(filingId);
        var isAuthorized = await authorizationSvc.IsAuthorized(new AuthorizationRequest(Permission.Disclosure_401_Attest, User, filerId: smoCampaignStatement.FilerId));

        var model = await smoCampaignStatementCtlSvc.GetVerificationPageViewModel(filingId, isAuthorized);
        return View(model);
    }

    [HttpPost(nameof(Verification))]
    public async Task<IActionResult> Verification(SmoCampaignStatementVerificationViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Previous:
                    return RedirectToAction(nameof(Review), new { filingId = model.Id });
                case FormAction.Continue:
                    return await VerificationContinue(model);
                case FormAction.SaveAndClose:
                    return SaveReview();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    [HttpPost(nameof(VerificationContinue))]
    private async Task<ActionResult> VerificationContinue(SmoCampaignStatementVerificationViewModel model)
    {
        if (model.IsUserAuthorizedToAttest && model.IsVerificationCertified)
        {
            await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Disclosure_401_Attest, User, filerId: model.FilerId));
            await smoCampaignStatementCtlSvc.AttestSmoCampaignStatement(model, ModelState);
        }
        else
        {
            await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Disclosure_401_Notify, User, filerId: model.FilerId));
            await smoCampaignStatementCtlSvc.SendForAttestation(model, ModelState);
        }
        if (!ModelState.IsValid)
        {
            return View(nameof(Verification), model);
        }

        return RedirectToAction(nameof(Confirmation), new { filingId = model.Id });
    }
    #endregion

    #region Confirmation
    /// <summary>
    /// Loader for Confirmation page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet(nameof(Confirmation))]
    public async Task<IActionResult> Confirmation(long filingId, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await smoCampaignStatementCtlSvc.GetConfirmationPageViewModel(filingId);
        return View(model);
    }

    [HttpPost(nameof(Confirmation))]
    public IActionResult Confirmation(SmoCampaignStatementConfirmationViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Close:
                    return RedirectToDashboard();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Amendment
    [HttpGet(nameof(Amend))]
    [Route("[controller]/Amend/{filingId:long}")]
    public async Task<IActionResult> Amend([Required] long filingId)
    {
        if (ModelState.IsValid)
        {
            long amendFilingId = await smoCampaignStatementCtlSvc.InitializeSmoCampaignStatementAmendment(filingId);
            return RedirectToAction(nameof(Review), new { filingId = amendFilingId });
        }
        return RedirectToDashboard();
    }
    #endregion

    #region Amendment Explanation
    /// <summary>
    /// Loader for Amendment Explanation.
    /// </summary>
    /// <param name="filingId">Filing ID</param>
    /// <returns>View with model</returns>
    [HttpGet(nameof(AmendmentExplanation))]
    public async Task<IActionResult> AmendmentExplanation([Required] long filingId)
    {
        var model = await smoCampaignStatementCtlSvc.GetAmendmentExplanation(filingId);

        if (ModelState.IsValid)
        {
            return View(model);
        }

        return NotFound();
    }

    [HttpPost(nameof(AmendmentExplanation))]
    public async Task<IActionResult> AmendmentExplanation(
        AmendSmoCampaignStatementAmendmentExplanationViewModel model)
    {

        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await AmendmentExplanationSave(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    [HttpPost(nameof(AmendmentExplanationSave))]
    public async Task<IActionResult> AmendmentExplanationSave(AmendSmoCampaignStatementAmendmentExplanationViewModel model)
    {

        await smoCampaignStatementCtlSvc.UpdateAmendmentExplanation(model, ModelState);

        if (ModelState.IsValid)
        {
            SetLocalizedToast(CommonResourceConstants.SavedMessage);
            return RedirectToAction(nameof(Review), new { filingId = model.Id });
        }

        if (!ModelState.IsValid && model.Id.HasValue)
        {
            return View(nameof(AmendmentExplanation), model);
        }
        return NotFound();
    }
    #endregion

    #region Shared
    [HttpGet("/SmoCampaignStatement/{filingId}/{transactionType}/EditTransactionEntry/{transactionId}")]
    public IActionResult EditTransactionEntry(
        [Required] long filingId,
        [Required] string transactionType,
        [Required] long transactionId)
    {
        if (ModelState.IsValid)
        {
            switch (transactionType)
            {
                case var type when type == TransactionType.PaymentReceived.Name:
                    return RedirectToAction(nameof(PaymentReceived02), new { filingId, transactionId });
                case var type when type == TransactionType.PaymentMade.Name:
                    return RedirectToAction(nameof(PaymentMade02), new { filingId, transactionId });
                case var type when type == $"{TransactionType.PaymentMade.Name}ByAgentOrIndependentContractor":
                    return RedirectToAction(nameof(PaymentMade02), new { filingId, transactionId, isPaymentMadeByAgent = true });
                case var type when type == TransactionType.PersonReceiving1000OrMore.Name:
                    return RedirectToAction(nameof(PersonReceiving02), new { filingId, transactionId });
                default:
                    return NotFound();
            }
        }
        return NotFound();
    }

    [HttpDelete("/SmoCampaignStatement/{filingId}/DeleteTransactionEntry/{transactionId}")]
    public async Task<IActionResult> DeleteTransactionEntry(
        [Required] long filingId,
        [Required] long transactionId)
    {
        if (ModelState.IsValid)
        {
            // DeleteTransactionAsync() that is called in WebApi is a PATCH, soft delete.
            // However, we call this method with HttpDelete to match _SmallGrid usage.
            await smoCampaignStatementCtlSvc.DeleteTransactionEntry(filingId, transactionId);

            return Ok();
        }

        return NotFound();
    }

    [HttpGet("/SmoCampaignStatement/{filingId}/EditDisclosureWithoutPaymentReceived/{disclosureWithoutPaymentId}")]
    public IActionResult EditDisclosureWithoutPaymentReceived(
        [Required] long filingId,
        [Required] long disclosureWithoutPaymentId)
    {
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(CandidatesMeasuresNotListed01), new { filingId, disclosureWithoutPaymentId });
        }

        return NotFound();
    }

    [HttpDelete("/SmoCampaignStatement/{filingId}/DeleteDisclosureWithoutPaymentReceived/{disclosureWithoutPaymentId}")]
    public async Task<IActionResult> DeleteDisclosureWithoutPaymentReceived(
        [Required] long filingId,
        [Required] long disclosureWithoutPaymentId)
    {
        if (ModelState.IsValid)
        {
            // DeleteDisclosureWithoutPaymentReceivedAsync() that is called in WebApi is a PATCH, soft delete.
            // However, we call this method with HttpDelete to match _SmallGrid usage.
            await smoCampaignStatementCtlSvc.DeleteDisclosureWithoutPaymentReceived(filingId, disclosureWithoutPaymentId);

            return Ok();
        }

        return NotFound();
    }

    /// <summary>
    /// Temporary method call to link to as pages get developed. Remove once all pages have been developed
    /// </summary>
    /// <returns> View of under construction page </returns>
    [HttpGet(nameof(UnderConstruction))]
    public IActionResult UnderConstruction()
    {
        return View("/Views/Shared/_UnderConstruction.cshtml");
    }

    /// <summary>
    /// Search Committee By ID or Committee Name
    /// </summary>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Json result of committee information</returns>
    [HttpGet(nameof(SearchCommitteeByIdOrName))]
    public async Task<JsonResult> SearchCommitteeByIdOrName([FromQuery] string search, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<CommitteeSearchResultDto>());
        }

        var result = await smoRegistrationSvc.SearchCommitteeByIdOrName(search);

        return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    /// <summary>
    /// Search Filer Contact By ID or Name
    /// </summary>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Json result of filer contact information</returns>
    [HttpGet("SmoCampaignStatement/SearchFilerContactsByIdOrName")]
    public async Task<JsonResult> SearchFilerContactsByIdOrName(
        [FromQuery][Required] long filerId,
        [FromQuery] string search,
        CancellationToken cancellationToken)
    {
        var contactTypes = new List<string>
        {
            FilerContactType.Individual.Name,
            FilerContactType.Organization.Name,
            FilerContactType.Filer.Name,
            FilerContactType.Candidate.Name
        };

        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<Generated.ContactSearchResultDto>());
        }

        var result = await contactsApi.SearchContactsByNameOrId(filerId, search, contactTypes, cancellationToken);

        return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    /// <summary>
    /// Search Candidate By ID or Name
    /// </summary>
    /// <param name="search">Search text input. Partial of candidate name.</param>
    /// <param name="cancellationToken">Standard .Net cancellationToken for async operations</param>
    /// <returns>JSON result of matched candidate information</returns>
    [HttpGet("/SmoCampaignStatement/SearchCandidateByName")]
    public async Task<JsonResult> SearchCandidateByName([FromQuery] string search, CancellationToken cancellationToken)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Candidate_SearchByName, User));

        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<CandidateSearchResultDto>());
        }

        var data = await candidateSvc.SearchCandidateByName(search);
        return new JsonResult(data, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    /// <summary>
    /// Search ballot measure By ID or ballot number/letter or title
    /// </summary>
    /// <param name="search">Search text input</param>
    /// <param name="cancellationToken">Standard .Net cancellationToken for async operations</param>
    /// <returns>JSON result of matched ballot measure information</returns>
    [HttpGet("/SmoCampaignStatement/SearchBallotMeasureByIdOrName")]
    public async Task<JsonResult> SearchBallotMeasureByIdOrName([FromQuery] string search, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<BallotMeasureDto>());
        }

        var result = await ballotMeasureSvc.SearchBallotMeasuresAsync(search);

        return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    [HttpGet("/SmoCampaignStatement/SearchSmoOfficerByName")]
    public async Task<JsonResult> SearchSmoOfficerByName(
        [FromQuery][Required] long filerId,
        [FromQuery][Required] long filingId,
        [FromQuery] string search)
    {
        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<SmoRegistrationContactDto>());
        }

        var result = await smoCampaignStatementCtlSvc.SearchSmoOfficersAsync(filerId, filingId, search);

        return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    private void SetLocalizedToast(string localizerKey)
    {
        var savedMessage = localizer[localizerKey]?.Value ?? "";
        TempData["ToastMessage"] = savedMessage;
        TempData["ToastType"] = "e-toast-success";
        TempData["ToastShowCloseButton"] = "true";
        TempData["ToastX"] = "Right";
        TempData["ToastY"] = "Bottom";
    }

    private RedirectToActionResult CancelAndRedirectToTransactionSummary(long? filingId, string summaryType)
    {
        SetLocalizedToast(CommonResourceConstants.ToastCanceled);
        return RedirectToAction(nameof(TransactionSummary), new { filingId, summaryType });
    }

    private RedirectToActionResult SaveAndRedirectToTransactionSummary(long? filingId, string summaryType)
    {
        SetLocalizedToast(CommonResourceConstants.ToastSaved);
        return RedirectToAction(nameof(TransactionSummary), new { filingId, summaryType });
    }

    private RedirectToActionResult RedirectToTransactionSummary(long? filingId, string summaryType)
    {
        return RedirectToAction(nameof(TransactionSummary), new { filingId, summaryType });
    }

    private void ClearModelWhenCancelTransactionEntry(SmoCampaignStatementTransactionEntryViewModel model)
    {
        if (model.Action == FormAction.Cancel)
        {
            // Optionally skip validation errors on cancel
            ModelState.Clear();
        }
    }

    private static string ResolvePaymentMadeFilingSummaryType(SmoCampaignStatementScreenTypes? screenType)
    {
        return IsPaymentMadeByAgent(screenType)
            ? FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name
            : FilingSummaryType.PaymentMadeSummary.Name;
    }

    private static SmoCampaignStatementScreenTypes ResolvePaymentMadeScreenType(bool isPaymentMadeByAgent)
    {
        return isPaymentMadeByAgent
            ? SmoCampaignStatementScreenTypes.PaymentMadeByAgent
            : SmoCampaignStatementScreenTypes.PaymentMade;
    }

    private static bool IsPaymentMadeByAgent(SmoCampaignStatementScreenTypes? screenType)
    {
        return screenType == SmoCampaignStatementScreenTypes.PaymentMadeByAgent;
    }

    private static void PopulateAddresses(SmoCampaignStatementTransactionEntryViewModel model)
    {
        model.Addresses = model.ParticipantType switch
        {
            string t when t == FilerContactType.Individual.Name
           => new List<AddressViewModel> { model.IndividualTransactorAddress },
            string t when t == FilerContactType.Organization.Name
                => new List<AddressViewModel> { model.OrganizationTransactorAddress },
            _ => new List<AddressViewModel>()
        };
    }

    private static void PopulateAddressesBackToModel(SmoCampaignStatementTransactionEntryViewModel model)
    {
        var first = model.Addresses?.FirstOrDefault();

        // based on participant type, write it back into the right ViewModel property
        switch (model.ParticipantType)
        {
            case string t when t == FilerContactType.Individual.Name:
                if (first != null)
                {
                    model.IndividualTransactorAddress = first;
                }

                break;

            case string t when t == FilerContactType.Organization.Name:
                if (first != null)
                {
                    model.OrganizationTransactorAddress = first;
                }

                break;
            default:
                break;
        }
    }
    #endregion
}

public class PaymentReceivedParameters
{
    [FromQuery(Name = "filingId")]
    [JsonRequired]
    public long Id { get; set; }

    [FromQuery(Name = "contactId")]
    [JsonRequired]
    public long ContactId { get; set; }

    [FromQuery(Name = "jurisdiction")]
    public string? Jurisdiction { get; set; }

    [FromQuery(Name = "pertainsTo")]
    public string? PertainsTo { get; set; }

    [FromQuery(Name = "position")]
    public string? Position { get; set; }

    [FromQuery(Name = "transactionAmount")]
    public decimal? TransactionAmount { get; set; }

    [FromQuery(Name = "transactionDate")]
    public DateTime? TransactionDate { get; set; }

    [FromQuery(Name = "notes")]
    public string? Notes { get; set; }

    [FromQuery(Name = "transactionId")]
    public long? TransactionId { get; set; }

    [FromQuery(Name = "attachedFileGuidsJson")]
    public string AttachedFileGuidsJson { get; set; } = string.Empty;
}

public class CandidatesMeasuresNotListedParameters
{
    [FromQuery(Name = "filingId")]
    [JsonRequired]
    public long Id { get; set; }

    [FromQuery(Name = "jurisdiction")]
    public string? Jurisdiction { get; set; }

    [FromQuery(Name = "pertainsTo")]
    public string? PertainsTo { get; set; }

    [FromQuery(Name = "position")]
    public string? Position { get; set; }

    [FromQuery(Name = "disclosureWithoutPaymentId")]
    public long? DisclosureWithoutPaymentId { get; set; }
}

public class PersonReceivingParameters
{
    [FromQuery(Name = "filingId")]
    [JsonRequired]
    public long Id { get; set; }

    [FromQuery(Name = "filerId")]
    public long? FilerId { get; set; }

    [FromQuery(Name = "contactId")]
    public long? ContactId { get; set; }

    [FromQuery(Name = "officerId")]
    public long? OfficerId { get; set; }

    [FromQuery(Name = "registrationId")]
    public long? RegistrationId { get; set; }

    [FromQuery(Name = "transactionId")]
    public long? TransactionId { get; set; }
}
