openapi: 3.0.3
info:
  title: Form 625 - Report of Lobbying Firm
  description: Comprehensive API schema for the submission of California Form 625 as per the Political Reform Act.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Disclosure/LobbyingFirm/{filerId}:
    post:
      summary: Submit Form 625 Report
      description: Submits a new lobbying firm report for a specified period, including all necessary details.
      operationId: submitLobbyingFirmReport
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingFirmReport'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingFirmReport:
      type: object
      required:
        - periodCovered
        - cumulativePeriodBeginning
        - lobbyingFirm
        - paymentsReceived
        - paymentsMade
        - activityExpenses
        - campaignContributions
        - attestation
      properties:
        periodCovered:
          type: object
          required:
            - periodFrom
            - periodTo
          properties:
            periodFrom:
              type: string
              format: date
              description: The start date of the reporting period.
            periodTo:
              type: string
              format: date
              description: The end date of the reporting period.
        cumulativePeriodBeginning:
          type: string
          format: date
          description: The cumulative start date, typically January 1 of the biennial legislative session.
        lobbyingFirm:
          type: object
          required:
            - name
            - businessAddress
          properties:
            name:
              type: string
              description: Name of the lobbying firm.
            businessAddress:
              $ref: './common-schemas.yaml#/components/schemas/Address'
            mailingAddress:
              $ref: './common-schemas.yaml#/components/schemas/Address'
            phone:
              type: string
              pattern: '^\d+$'
              maxLength: 40
        lobbyistList:
          type: array
          items:
            type: object
            properties:
              isDirectCommunication:
                type: boolean
                description: "partners, owners, officers, or employees who engaged in direct communication on at least five separate occasions during the period"
              attachment615:
                $ref: './615.yaml#/components/schemas'
              individualLobbyistName:
                type: array
                description: "List of partners, owners, officers, or employees"
                items:
                  type: object
                  properties:
                    title:
                      type: string
                    name:
                      type: string
              lobbyistListOfDirectCommunication:
                $ref: '#/components/schemas/Lobbyist'
        summaryOfPayments:
          type: object
          description: Summary of all payments made and received in the period.
          required:
            - totalPaymentsReceived
            - totalActivityExpenses
            - totalPaymentsToOtherFirms
            - grandTotalPaymentsMade
          properties:
            totalPaymentsReceived:
              type: number
              description: Total payments received in the reporting period.
            totalActivityExpenses:
              type: number
              description: Total activity expenses during the period.
            totalPaymentsToOtherFirms:
              type: number
              description: Total payments made to other lobbying firms.
            grandTotalPaymentsMade:
              type: number
              description: Total of all payments made (sum of activity expenses and payments to other firms).
            isCampaignContributionsMade:
              type: boolean
              description: Is Campaign Contributions Made this quarter, if yes part IV should be completed.
            isFirmLobbyingCoalition:
              type: boolean
              description: Is Firm Lobbying Coalition, if yes form 630 should be attached.
        paymentsReceived:
          type: array
          description: Details of payments received in connection with lobbying activity.
          items:
            type: object
            required:
              - employer
              - legislativeActions
              - fees
              - reimbursements
              - total
              - cumulativeTotal
            properties:
              employer:
                type: object
                required:
                  - name
                  - address
                  - phone
                properties:
                  name:
                    type: string
                  address:
                    $ref: './common-schemas.yaml#/components/schemas/Address'
                  phone:
                    type: string
                    pattern: '^\d+$'
                    maxLength: 40
              legislativeActions:
                type: array
                description: List of legislative or administrative actions actively lobbied.
                items:
                  type: object
                  required:
                    - billNumber
                    - action
                  properties:
                    billNumber:
                      type: string
                    action:
                      type: string
                      description: A brief description of each legislative or administrative action actively lobbied during the quarter.
                    isStateAdministrativeActions:
                      type: boolean
                    nameOfStateAgency:
                      type: string
                      description: State administrative actions, provide the name of the state agency or department.
              fees:
                type: number
                description: Fees and retainers received during the period.
              reimbursements:
                type: number
                description: Reimbursements received during the period.
              advancePayments:
                type: number
                description: AdvancePayments received during the period.
              total:
                type: number
                description: Total amount received during the reporting period.
              cumulativeTotal:
                type: number
                description: Cumulative total amount received since January 1 of the biennial legislative session.
              subTotal:
                type: number
                description: Total amount of total amount received during the reporting period.
        activityExpenses:
          type: object
          description: Activity expenses during the reporting period.
          required:
            - arrangedByFirm
            - arrangedByLobbyists
            - total
          properties:
            arrangedByFirm:
              type: array
              description: Activity expenses arranged or paid by the lobbying firm.
              items:
                type: object
                required:
                  - date
                  - payeeName
                  - payeeAddress
                  - reportablePersons
                  - consideration
                  - total
                properties:
                  date:
                    type: string
                    format: date
                  payeeName:
                    $ref: './common-schemas.yaml#/components/schemas/PersonName'
                  payeeAddress:
                    $ref: './common-schemas.yaml#/components/schemas/Address'
                  reportablePersons:
                    type: array
                    items:
                      $ref: '#/components/schemas/ReportablePerson'
                  eachItemAmount:
                    type: number
                    description: Each item payment
                  consideration:
                    type: string
                    # enum:
                    #   - Breakfast
                    #   - Dinner
                    #   - Drinks
                    #   - Event ticket
                    #   - Flowers
                    #   - Food & beverages
                    #   - Reception
                    #   - Travel expenses
                    #   - Other
                    description: Description of services provided.
                  otherExpenses:
                    type: string
                    description: Text for other payments or event ticket expenses or travel expenses
                  total:
                    type: number
                    description: Total expense amount.
            arrangedByLobbyists:
              type: number
              description: Total expenses arranged by lobbyists employed by the firm.
            total:
              type: number
              description: Total activity expenses (arranged by the firm and lobbyists).
        activityExpensesToOtherLobbyingFirms:
          type: array
          description: Payments made to other lobbying firms.
          items:
            type: object
            required:
              - subcontractor
              - client
              - amount
              - cumulativeTotal
            properties:
              subcontractor:
                type: object
                required:
                  - name
                  - address
                  - phone
                properties:
                  name:
                    type: string
                  address:
                    $ref: './common-schemas.yaml#/components/schemas/Address'
                  phone:
                    type: string
                    pattern: '^\d+$'
                    maxLength: 40
              client:
                type: object
                required:
                  - name
                properties:
                  name:
                    type: string
              amount:
                type: number
                description: Payment amount for the period.
              cumulativeTotal:
                type: number
                description: Cumulative total payments made since January 1 of the biennial legislative session.
              totalPayments:
                type: number
                description: Total payments
        campaignContributions:
          type: object
          description: Campaign contributions during the period.
          properties:
            majorDonorName:
              type: string
              description: Name of the major donor or the recipient committee.
            majorDonorId:
              type: string
              format: uuid
              description: The identifier for the major donor or recipient committee.
            itemizedContributions:
              type: array
              description: Itemized contributions of $100 or more.
              items:
                type: object
                required:
                  - date
                  - recipientName
                  - idOfCommittee
                  - amount
                properties:
                  date:
                    type: string
                    format: date
                  recipientName:
                    type: string
                  idOfCommittee:
                    type: string
                  amount:
                    type: number
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'

    Lobbyist:
      type: object
      properties:
        lobbyistId:
          type: string
          format: uuid
          description: Identifier for the lobbyist.
        lobbyistName:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        title:
          type: string
          description: Title of each partner, officer or employee of the firm.

    ReportablePerson:
      type: object
      properties:
        reportablePersonName:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        officialPosition:
          type: string
          # enum:
          #   - Governor
          #   - Lieutenant Governor
          #   - Attorney General
          #   - Insurance Commissioner
          #   - Controller
          #   - Secretary of State
          #   - Treasurer
          #   - Superintendent of Public Instruction
          #   - Assembly Member
          #   - Senator
          #   - Assembly Staff Member
          #   - Senate Staff Member
          #   - Other
          description: Official position
        otherOfficialPosition:
          type: string
          description: Text for other official position
