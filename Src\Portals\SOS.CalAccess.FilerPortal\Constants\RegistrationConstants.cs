namespace SOS.CalAccess.FilerPortal.Constants;

public static class RegistrationConstants
{
    public static class RegistrationType
    {
        public const string CandidateIntentionStatement = "CandidateIntentionStatement";
        public const string CandidateIntentionStatementAmendment = "CandidateIntentionStatementAmendment";
        public const string CandidateIntentionStatementWithdrawal = "CisWithdrawal";
        public const string SlateMailerOrganization = "SlateMailerOrganization";
        public const string SlateMailerOrganizationAmendment = "SlateMailerOrganizationAmendment";
        public const string Lobbyist = "Lobbyist";
        public const string LobbyistAmendment = "LobbyistAmendment";
        public const string LobbyistWithdrawal = "LobbyistWithdrawal";
        public const string LobbyistTermination = "LobbyistTermination";
        public const string LobbyistRenewal = "LobbyistRenewal";
        public const string LobbyistEmployer = "LobbyistEmployer";
    }

    public static class RoutingPath
    {
        public const string CandidateIntentionStatement = "CandidateRegistration";
        public const string CandidateIntentionStatementAmendment = "AmendCandidateRegistration";
        public const string CandidateIntentionStatementWithdrawal = "CisRegistrationWithdrawal";
        public const string SlateMailerOrganization = "SmoRegistration";
        public const string SlateMailerOrganizationAmendment = "AmendSmoRegistration";
        public const string Lobbyist = "LobbyistRegistration";
        public const string LobbyistRegistrationWithdrawal = "WithdrawRegistration";
        public const string LobbyistRegistrationTermination = "TerminationNotice";
        public const string LobbyistEmployer = "LobbyistEmployerRegistration";
    }
}
