namespace SOS.CalAccess.Services.Business;

/// <summary>
/// Result class for methods that have no return type.
/// </summary>
public class MethodResult
{
    /// <summary>
    /// Constructor for successful result case.
    /// </summary>
    public MethodResult() { }

    /// <summary>
    /// Constructor for failed result case.
    /// </summary>
    /// <param name="ex"></param>
    public MethodResult(Exception ex)
    {
        Error = ex;
    }

    /// <summary>
    /// Gets or sets Error
    /// </summary>
    public Exception? Error { get; set; }

    /// <summary>
    /// Returns true in successful result case.
    /// </summary>
    /// <returns></returns>
    public bool IsOk()
    {
        return Error is null;
    }

    /// <summary>
    /// Returns true in failed result case.
    /// </summary>
    /// <returns></returns>
    public bool IsError()
    {
        return Error is not null;
    }

    /// <summary>
    /// Gets the error stripped of nullability.
    /// </summary>
    /// <returns></returns>
    public Exception GetError()
    {
        return Error!;
    }
}

public class MethodResult<TOkData>
{
    /// <summary>
    /// Constructor for succesful result case.
    /// </summary>
    public MethodResult(TOkData data)
    {
        Data = data;
    }

    /// <summary>
    /// Constructor for failed result case.
    /// </summary>
    /// <param name="ex"></param>
    public MethodResult(Exception ex)
    {
        Error = ex;
    }

    /// <summary>
    /// Gets or sets Data
    /// Should not be null in success case.
    /// Should be null in fail case.
    /// </summary>
    public TOkData? Data { get; set; }

    /// <summary>
    /// Gets or sets Error
    /// Should not be null in fail case.
    /// Should be null in success case.
    /// </summary>
    public Exception? Error { get; set; }

    /// <summary>
    /// Returns true in successful result case.
    /// </summary>
    /// <returns></returns>
    public bool IsOk()
    {
        return Error is null;
    }

    /// <summary>
    /// Returns true in failed result case.
    /// </summary>
    /// <returns></returns>
    public bool IsError()
    {
        return Error is not null;
    }

    /// <summary>
    /// Returns data object stripped of nullability.
    /// Use only after asserting checking that this result is successful.
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InvalidDataException"></exception>
    public TOkData Unwrap()
    {
        if (IsOk())
        {
            return Data!;
        }
        if (IsError())
        {
            throw Error!;
        }
        throw new InvalidDataException("Unknown error in MethodResult.");
    }

    /// <summary>
    /// Gets the error stripped of nullability.
    /// </summary>
    /// <returns></returns>
    public Exception GetError()
    {
        return Error!;
    }
}
