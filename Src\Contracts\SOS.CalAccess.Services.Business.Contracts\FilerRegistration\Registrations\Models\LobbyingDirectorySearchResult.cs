using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Lobbying Directory Search Information 
/// </summary>
public class LobbyingDirectorySearchResult
{

    /// <summary>
    /// Lobbyist Id
    /// </summary>
    [Required]
    public long Id { get; set; }
    /// <summary>
    /// Name of the Lobbyist 
    /// </summary>
    [Required]
    public required string LobbyistName { get; set; }

    /// <summary>
    /// The last Modified dateitme of the Lobbyist pic in Ticks
    /// </summary>
    public long? LobbyistPicLastModifiedDateTimeTicks { get; set; }
    /// <summary>
    /// Phone Number of the Lobbyist 
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// LobbyingFirm
    /// </summary>
    public string? LobbyingFirm { get; set; }

    /// <summary>
    /// URL of the LobbyistPicture - needs to be set based on ID of the lobbyist and the last modified datetime in ticks 
    /// </summary>
    public string? LobbyistPictureURL { get; set; }

}
