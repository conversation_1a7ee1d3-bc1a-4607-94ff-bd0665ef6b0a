
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.SeedData;

#region Design Notes
/// <summary>
/// Interface for the Seed Data Service.
/// </summary>
///
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Seed Data Service. The service is responsible for managing seed data, including creating, updating, and terminating seed data.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating seed data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the lifecycle of seed data, including creation, modification, and termination of seed data records.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>SD-01: Create Seed Data</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | ISeedContactRepository         | GetTestFilerContact            | Get a test filer contact.     |
/// | ISeedRegistrationRepository    | SeedTestCandidateIntentionStatement | Seed a candidate intention statement. |
/// | ISeedRegistrationRepository    | CreateTestFiling                | Create a test filing.         |
/// | ISeedTransactionRepository    | CreateTestActivityExpense       | Create a test activity expense. |
/// | ISeedContactRepository         | CreateTestCommitteeContact      | Create a test committee contact. |
/// | ISeedContactRepository         | CreateTestOrganizationContact   | Create a test organization contact. |
/// | ISeedContactRepository         | CreateTestIndividualContact     | Create a test individual contact. |
/// | ISeedRegistrationRepository    | CreateFilerForRegistration       | Create a filer for a registration. |
#endregion

/// <summary>
/// Interface for the Seed Data Service.
/// </summary>
public interface ISeedDataSvc
{
    /// <summary>
    /// Seed all data.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the seeded data.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task SeedAllData(long userId);

    /// <summary>
    /// Create a candidate intention statement.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the candidate intention statement.</param>
    /// <returns>The created candidate intention statement.</returns>
    Task<CandidateIntentionStatement> CreateCandidateIntentionStatement(long userId);

    /// <summary>
    /// Create a lobbyist employer registration.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the lobbyist employer registration.</param>
    /// <returns>The created lobbyist employer registration.</returns>
    Task<LobbyistEmployer> CreateLobbyistEmployerRegistration(long userId);

    /// <summary>
    /// Create a lobbyist registration.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the lobbyist registration.</param>
    /// <returns>The created lobbyist registration.</returns>
    Task<Lobbyist> CreateLobbyistRegistration(long userId);

    /// <summary>
    /// Create a filer for a registration.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the filer.</param>
    /// <param name="registrationId">The ID of the registration to associate with the filer.</param>
    /// <returns>The created or existing Filer.</returns>
    Task<Filer> CreateFilerForRegistration(long userId, long registrationId);

    /// <summary>
    /// Create a committee contact.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the committee contact.</param>
    /// <param name="filerId">The ID of the filer to associate with the committee contact.</param>
    /// <returns>The created committee contact.</returns>
    Task<CommitteeContact> CreateCommitteeContact(long userId, long filerId);

    /// <summary>
    /// Create an organization contact.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the organization contact.</param>
    /// <param name="filerId">The ID of the filer to associate with the organization contact.</param>
    /// <returns>The created organization contact.</returns>
    Task<OrganizationContact> CreateOrganizationContact(long userId, long filerId);


    /// <summary>
    /// Create an individual contact.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the individual contact.</param>
    /// <param name="filerId">The ID of the filer to associate with the individual cont act.</param>
    /// <returns>The created individual contact.</returns>
    Task<IndividualContact> CreateIndividualContact(long userId, long filerId);

    /// <summary>
    /// Create an activity expense.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the   activity expense.</param>
    /// <param name="contactType">The type of the contact to associate with the activity expense.</param>
    /// <param name="filerId">The ID of the filer to associate with the activity expense.</param>
    /// <returns>The created activity expense.</returns>
    Task<ActivityExpense> CreateActivityExpense(long userId, string contactType, long filerId);

    /// <summary>
    /// Create a primarily formed committee.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the primarily formed committee.</param>
    /// <returns>The created primarily formed committee.</returns>
    Task<PrimarilyFormedCommittee> CreateTestPrimarilyFormedCommittee(long userId);

    /// <summary>
    /// Create a candidate controlled committee.
    /// </summary>
    /// <param name="userId">The ID of the user to associate with the candidate controlled committee.</param>
    /// <returns>The created candidate controlled committee.</returns>
    Task<CandidateControlledCommittee> CreateTestCandidateControlledCommittee(long userId);
}
