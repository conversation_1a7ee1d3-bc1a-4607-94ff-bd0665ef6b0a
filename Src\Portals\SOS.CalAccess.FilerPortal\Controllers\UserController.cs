// <copyright file="UserController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for handling user-related actions.
/// </summary>
/// <param name="userApi">API client for managing users.</param>
public sealed class UserController(IUsersApi userApi) : Controller
{
    private readonly IUsersApi _userApi = userApi;

    /// <summary>
    /// Displays a list of users.
    /// </summary>
    /// <param name="returnUrl">The URL to redirect to after the user is logged in.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A view displaying a list of users.</returns>
    [AllowAnonymous]
    public async Task<IActionResult> Index(
        string? returnUrl, CancellationToken cancellationToken)
    {
        if (User.Identity?.IsAuthenticated == true)
        {
            // User is logged in redirect to Dashboard
            return RedirectToAction(actionName: nameof(DashboardController.Index), controllerName: "Dashboard");
        }
        if (ModelState.IsValid)
        {
            ViewData["ReturnUrl"] = returnUrl;
        }

        var response = await _userApi.GetUsers(cancellationToken);
        var userModels = response.Users.Select(u => new UserViewModel(u));

        return View(userModels);
    }
}
