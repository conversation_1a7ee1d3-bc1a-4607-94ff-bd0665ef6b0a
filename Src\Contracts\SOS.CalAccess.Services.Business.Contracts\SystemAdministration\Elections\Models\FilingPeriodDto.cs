namespace SOS.CalAccess.Services.Business.SystemAdministration.Elections.Models;
/// <summary>
/// Represents a filing period associated with a specific report type and election.
/// </summary>
public class FilingPeriodDto
{

    /// <summary>
    /// The identifier for the report type (e.g., quarterly, semi-annual, pre-election).
    /// </summary>
    public long ReportType { get; set; }

    /// <summary>
    /// The identifier of the related election to which this filing period applies.
    /// </summary>
    public long RelatedElection { get; set; }

    /// <summary>
    /// The start date of the reporting period.
    /// </summary>
    public required DateTime PeriodStartDate { get; set; }

    /// <summary>
    /// The end date of the reporting period.
    /// </summary>
    public required DateTime PeriodEndDate { get; set; }

    /// <summary>
    /// The due date for submitting the report for this filing period.
    /// </summary>
    public required DateTime DueDate { get; set; }

}
