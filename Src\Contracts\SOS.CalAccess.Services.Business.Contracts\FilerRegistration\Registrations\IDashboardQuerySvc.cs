using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface outlines the methods for managing complex Dashboard queries within the system.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// There is complexity that will continue to evolve here as dashboards will be customized to users and roles
/// </p>

#endregion
/// <summary>
/// Service interface for dashboard-related queries
/// </summary>
/// <param name="userId">The UserId of the logged in user.</param>
/// <returns>a Collection of Registrations the user has access to</returns>
///
/// \msc
/// Actor => IDashboardQuerySvc [label="GetDashboardRegistrationsForUserAsync()"];
/// IDashboardQuerySvc => IDashboardQueryRepository [label="GetDashboardRegistrationsForUserAsync()"];
/// IDashboardQueryRepository >> IDashboardQuerySvc [label="return registrations"];
/// IDashboardQuerySvc >> Actor [label="return registrations"];
/// \endmsc
public interface IDashboardQuerySvc
{
    Task<List<RegistrationDashboardDto>> GetDashboardRegistrationsForUserAsync(long userId);
}
