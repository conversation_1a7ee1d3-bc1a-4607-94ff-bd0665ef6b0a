using Newtonsoft.Json.Linq;

namespace SOS.CalAccess.Services.Common.DataValidation;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IElectionRaceSvc provides methods to support validating that a provided race id meets a defined rules.
/// </p>
/// <p>
/// the service provides methods for performing the validation on a Election race id
/// </p>
/// <p>
/// FilingId are stored into the database and referenced by a unique identifier
/// </p>
/// <p>
/// In terms of Architecture Design this translates to a backend common service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS2 Data Validation</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IAmendmentRepository          | GetById                        | Get amendment values by json object        |
#endregion
public interface IElectionRaceSvc
{
    /// <summary>
    /// Evaluate the election race id  and validate that it meets the specific rules.
    /// </summary>
    /// <param name="json">The JSON JObject we're validating</param>
    /// <returns> bool value , that validates the Election race</returns>/returns>
    ///

    Task<bool> ElectionRaceValidation(JObject json);
}
