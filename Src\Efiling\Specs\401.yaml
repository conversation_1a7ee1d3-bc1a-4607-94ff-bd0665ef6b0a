openapi: 3.0.3
info:
  title: Form 401 - Slate Mailer Organization Campaign Statement API
  description: API for submitting and managing Form 401 campaign statements, including all schedules and summaries.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/SlateMailer/{filerId}:
    post:
      summary: Submit Slate Mailer Organization Campaign Statement
      description: Submit Form 401, including all schedules (A-D), summaries, and verification information.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CampaignDisclosureSlateMailer'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    CampaignDisclosureSlateMailer:
      type: object
      properties:
        statementPeriod:
          $ref: '#/components/schemas/StatementPeriod'
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        contactDetails:
          $ref: '#/components/schemas/ContactDetails'
        recipientCommitteeIdNumber:
          type: number
          description: Recipient committee ID number if applicable.
        recipientCommittee:
          oneOf:
          - $ref: './450.yaml#/components/schemas/RecipientCommitteeCampaignStatement'
          - $ref: './460.yaml#/components/schemas/RecipientCommitteeCampaignStatement'
          - $ref: './461.yaml#/components/schemas/Form461Submission'
          description: fill the most recent committee campaign statement (Form 450, 460 or 461).
        summaryOfPayments:
          $ref: '#/components/schemas/SummaryOfPayments'
        scheduleA:
          $ref: '#/components/schemas/ScheduleA'
        scheduleB:
          $ref: '#/components/schemas/ScheduleB'
        scheduleB1:
          $ref: '#/components/schemas/ScheduleB1'
        scheduleC:
          $ref: '#/components/schemas/ScheduleC'
        scheduleD:
          $ref: '#/components/schemas/ScheduleD'
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'

    StatementPeriod:
      type: object
      properties:
        startDate:
          type: string
          format: date
          description: Start date of the reporting period.
        endDate:
          type: string
          format: date
          description: End date of the reporting period.

    ContactDetails:
      type: object
      properties:
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        phoneNumber:
          type: string
          description: Contact phone number as digits only.
          pattern: '^\d+$'
          maxLength: 40

    SummaryOfPayments:
      type: object
      properties:
        totalPaymentsReceived:
          type: object
          properties:
            currentPeriod:
              type: number
              format: float
              description: Total payments received during the reporting period.
            cumulativeToDate:
              type: number
              format: float
              description: Cumulative payments received since January 1.
        totalPaymentsMade:
          type: object
          properties:
            currentPeriod:
              type: number
              format: float
              description: Total payments made during the reporting period.
            cumulativeToDate:
              type: number
              format: float
              description: Cumulative payments made since January 1.

    CandidatesAndMeasures:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the candidate (if applicable).
        lastName:
          type: string
          description: Last name of the candidate (if applicable).
        name:
          type: string
          description: Name of the ballot measure (if applicable).
        electionOfficeSought:
          $ref: './common-schemas.yaml#/components/schemas/ElectionOffice'
        ballotMeasureJurisdiction:
          type: string
          description: Jurisdiction of the measure
        code:
          type: string
          description: Ballot number of letter.
        position:
          type: string
          enum:
            - Support
            - Oppose
          description: Indicates whether the payment supports or opposes the candidate or measure.

    PayerDetails:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the payer.
        firstName:
          type: string
          description: First name of the payer.
        lastName:
          type: string
          description: Last name of the payer.
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        employerOrBusiness:
          type: string
          description: Employer or business of the payer (if applicable).
        candidateOrMeasureDetails:
          $ref: '#/components/schemas/CandidatesAndMeasures'

    PayeeDetails:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the payee.
        firstName:
          type: string
          description: First name of the payee.
        lastName:
          type: string
          description: Last name of the payee.
        address:
          $ref: './common-schemas.yaml#/components/schemas/Address'

    ScheduleA:
      type: object
      properties:
        summary:
          type: object
          properties:
            itemizedPayments:
              type: number
              format: float
              description: Total itemized payments received (≥ $100).
            nonItemizedPayments:
              type: number
              format: float
              description: Total non-itemized payments received (< $100).
            totalPayments:
              type: number
              format: float
              description: Total payments received (sum of itemized and non-itemized payments).
        details:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: Unique identifier for the payment record.
              dateReceived:
                type: string
                format: date
                description: Date the payment was received.
              amountReceivedThisPeriod:
                type: number
                format: float
                description: Payment amount received during this period.
              cumulativeAmountReceived:
                type: number
                format: float
                description: Cumulative amount received since January 1.
              payerDetails:
                $ref: '#/components/schemas/PayerDetails'

    ScheduleB:
      type: object
      properties:
        summary:
          type: object
          properties:
            itemizedPayments:
              type: number
              format: float
              description: Total payments made to itemized sources (≥ $100).
            nonItemizedPayments:
              type: number
              format: float
              description: Total non-itemized payments made (< $100).
            totalPayments:
              type: number
              format: float
              description: Total payments made (sum of itemized and non-itemized payments).
        details:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: Unique identifier for the payment record.
              payeeDetails:
                $ref: '#/components/schemas/PayeeDetails'
              descriptionOfPayment:
                type: string
                description: Description of the product or service received.
              amount:
                type: number
                format: float
                description: Amount paid during the current period.

    ScheduleB1:
      type: object
      properties:
        agentDetails:
          type: object
          properties:
            id:
              type: string
              description: Unique identifier for the agent or contractor.
            firstName:
              type: string
              description: First name of the agent or contractor.
            lastName:
              type: string
              description: Last name of the agent or contractor.
            address:
              $ref: './common-schemas.yaml#/components/schemas/Address'
        payments:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: Unique identifier for the payment record.
              payeeFirstName:
                type: string
                description: First name of the ultimate payee.
              payeeLastName:
                type: string
                description: Last name of the ultimate payee.
              descriptionOfPayment:
                type: string
                description: Description of the payment made.
              amount:
                type: number
                format: float
                description: Amount paid by the agent or contractor.

    ScheduleC:
      type: array
      items:
        type: object
        properties:
          id:
            type: string
            format: uuid
            description: Unique identifier for the payment record.
          firstName:
            type: string
            description: First name of the individual receiving payments ($1,000 or more).
          lastName:
            type: string
            description: Last name of the individual receiving payments ($1,000 or more).
          amountThisPeriod:
            type: number
            format: float
            description: Payment received by the individual during this period.
          cumulativeAmount:
            type: number
            format: float
            description: Cumulative payments received by the individual since January 1.

    ScheduleD:
      type: array
      items:
        type: object
        properties:
          id:
            type: string
            format: uuid
            description: Unique identifier for the candidate or measure.
          candidateOrMeasures:
            $ref: '#/components/schemas/CandidatesAndMeasures'
