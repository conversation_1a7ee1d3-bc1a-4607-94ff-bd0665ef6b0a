openapi: 3.0.3
info:
  title: Form 603 - Lobbyist Employer/Lobbying Coalition Registration Statement
  description: |
    API for submitting Form 603, the Lobbyist Employer or Lobbying Coalition Registration Statement.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Registration/LobbyingCoalition:
    post:
      summary: Submit Form 603
      description: Submit the Lobbyist Employer/Lobbying Coalition Registration Statement (Form 603).
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingRegistrationStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

  /Lobbying/Registration/LobbyistEmployer:
    post:
      summary: Submit Form 603
      description: Submit the Lobbyist Employer/Lobbying Coalition Registration Statement (Form 603).
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingRegistrationStatement'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingRegistrationStatement:
      type: object
      properties:
        filerDetails:
          $ref: './common-schemas.yaml#/components/schemas/FilerDetails'
        dateQualified:
          type: string
          format: date
          description: Date the filer qualified as a lobbyist employer or lobbying coalition.
        isLobbyingCoalition:
          type: boolean
          description: Describe the Registration type either as LobbyistEmployer or LobbyingCoalition.        
        lobbyists:
          type: array
          items:
            $ref: './604.yaml#/components/schemas/LobbyistCertificationStatement'
          description: List of in-house employee lobbyists (604) and lobbying firms.
        lobbyingFirms:
          type: array
          items:
            $ref: '#/components/schemas/LobbyingFirm'
          description: List of lobbying firms.
        stateAgenciesInfluenced:
          type: array
          items:
            type: string
          description: List of state agencies whose actions will be influenced.
        lobbyingInterest:
          type: string
          description: Lobbying interest name.

        natureAndInterests:
          type: array
          items:
            oneOf:
              - type: object
                title: Individual
                properties:
                  individual:
                    type: array
                    items:
                      $ref: '#/components/schemas/Individual'
                  industryGroupClassification:
                    type: array
                    items:
                      $ref: '#/components/schemas/IndustryGroupClassification'

              - type: object
                title: BusinessEntity
                properties:
                  businessEntity:
                    type: array
                    items:
                      $ref: '#/components/schemas/BusinessEntity'
                  industryGroupClassification:
                    type: array
                    items:
                      $ref: '#/components/schemas/IndustryGroupClassification'

              - type: object
                title: IndustryTradeOrProfessionalAssociation
                properties:
                  industryTradeOrProfessionalAssociation:
                    type: array
                    items:
                      $ref: '#/components/schemas/IndustryTradeOrProfessionalAssociation'
                  industryGroupClassification:
                    type: array
                    items:
                      $ref: '#/components/schemas/IndustryGroupClassification'

              - type: object
                title: Other
                properties:
                  other:
                    type: array
                    items:
                      $ref: '#/components/schemas/Other'
                  industryGroupClassification:
                    type: array
                    items:
                      $ref: '#/components/schemas/IndustryGroupClassification'

        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'

      required:
        - filerDetails
        - lobbyists
        - natureAndInterests
        - stateAgenciesInfluenced
        - LobbyingInterest
        - attestation
        - isLobbyingCoalition

    LobbyingFirm:
      type: object
      properties:
        name:
          type: string
          description: The name of the lobbying firm.
      required:
        - name

    Individual:
      type: object
      properties:
        natureAndInterestEmployerName:
          type: string
        employerAddress:
          $ref: './common-schemas.yaml#/components/schemas/Address'
        businessActivity:
          type: string
          description: Description of business activity in which you or your employer are engaged.

    BusinessEntity:
      type: object
      properties:
        businessDescription:
          type: string
          description: Description of business activity in which engaged.

    IndustryTradeOrProfessionalAssociation:
      type: object
      properties:
        industryDescription:
          type: string
          description: Description of industry, trade or profession represented.
        industryPortion:
          type: string
          description: Specific description of any portion or faction of the industry, trade, or profession which the association exclusively or primarily represents.
        numberOfMembers:
          type: string

    Other:
      type: object
      properties:
        statementOfNatureAndPurposes:
          type: string
          description: Statement of nature and purposes.
        commonInterest:
          type: string
          description: Description of any trade, profession, or other group with a common economic interest which is principally represented or from which membership or financial support is principally derived.

    IndustryGroupClassification:
      type: object
      properties:
        industryGroupClassifications:
          type: string
          description: Industry group sub-categories classification and Industry group classification.
        industryGroupOtherText:
          type: string
          description: The description of the industry group when other is selected.
