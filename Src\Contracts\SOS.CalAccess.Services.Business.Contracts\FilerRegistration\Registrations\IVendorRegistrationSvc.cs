using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// 
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service invoking this layer is developed.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// Allows third party vendors to submit their registration 
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FR-16: Vendor Registration</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this service.
/// </p>
/// | Service                              | Operation  | Description                          |
/// | ------------------------------------ | ---------- | ------------------------------------ |
/// |   VendorRegistrationRepository       | CreateVendorRegistration|  Create the Vendor Registration Record|
/// |   VendorRegistrationRepository       | UpdateVendorRegistration|  Update the Vendor Registration Record|
/// 
#endregion
namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
public interface IVendorRegistrationSvc
{

    /// <summary>
    /// Create the vendor Registration 
    /// </summary>
    /// <param name="vendor">Vendor</param>
    /// <returns>void</returns>
    /// \msc
    /// Actor, IVendorRegistrationSvc [label="IVendor\nRegistrationSvc"], IVendorRegistrationRepository [label="IVendor\nRegistrationRepository"];
    /// Actor => IVendorRegistrationSvc [label="CreateVendorRegistration()"];
    /// IVendorRegistrationRepository >> IVendorRegistrationSvc [label="\nreturn void"];
    /// IVendorRegistrationSvc >> Actor [label="\nreturn void"];
    /// \endmsc
    public Task CreateVendorRegistration(Vendor vendor);


    /// <summary>
    /// Update the vendor Registration - required maybe for marking the vendor certified or if there are any edits requried to the registration 
    /// </summary>
    /// <param name="vendor">Vendor</param>
    /// <returns>void</returns>
    /// \msc
    /// Actor, IVendorRegistrationSvc [label="IVendor\nRegistrationSvc"], IVendorRegistrationRepository [label="IVendor\nRegistrationRepository"];
    /// Actor => IVendorRegistrationSvc [label="UpdateVendorRegistration()"];
    /// IVendorRegistrationRepository >> IVendorRegistrationSvc [label="\nreturn void"];
    /// IVendorRegistrationSvc >> Actor [label="\nreturn void"];
    /// \endmsc
    public Task UpdateVendorRegistration(Vendor vendor);
    //for update currently there dont seem to be any figma diagrams - there needs to be a place where PRD needs to be able to see all vendor registrations
    //and be able to certify a vendor





}
