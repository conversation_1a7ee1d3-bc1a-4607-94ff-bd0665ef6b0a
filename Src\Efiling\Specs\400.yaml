openapi: 3.0.3
info:
  title: Form 400 - Statement of Organization API
  description: API for submitting Form 400, required by slate mailer organizations that receive $500 or more in payments for slate mailers.
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Registration/SlateMailer/{filerId}:
    post:
      summary: Submit Form 400 - Statement of Organization
      description: Submit Form 400, including organization information, officer details, and verification.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CampaignRegistrationSlateMailer'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    CampaignRegistrationSlateMailer:
      type: object
      properties:
        slateMailerOrganization:
          $ref: '#/components/schemas/SlateMailerOrganizationInformation'
        officers:
          type: array
          items:
            type: object
            properties:
              firstName:
                type: string
                description: First name of the officer.
              lastName:
                type: string
                description: Last name of the officer.
              role:
                type: string
                enum:
                  - Treasurer
                  - Principal
                  - Officer
                description: Position of the officer (Treasurer or Principal Officer).
              address:
                $ref: './common-schemas.yaml#/components/schemas/Address'
              phone:
                type: string
                description: Daytime phone number of the officer as digits only.
                pattern: '^\d+$'
                maxLength: 40
              email:
                type: string
                format: email
                description: email address of the officer.
            required:
              - firstName
              - lastName
              - role
              - address
              - phone
              - email

          description: List of Treasurers and Principal Officers, including their roles, contact information, and addresses.

        individualsAuthorizingSlateMailers:
          type: array
          items:
            type: object
            properties:
              firstName:
                type: string
                description: First name of the individual authorizing slate mailers.
              lastName:
                type: string
                description: Last name of the individual authorizing slate mailers.
              address:
                $ref: './common-schemas.yaml#/components/schemas/Address'
              phone:
                type: string
                description: Business telephone number of the individual as digits only.
                pattern: '^\d+$'
                maxLength: 40
              email:
                type: string
                format: email
                description: email address of the individual.

            required:
              - firstName
              - lastName
              - address
              - phone
              - email
          description: List of individuals with final decision-making authority over the slate mailer's contents.
        committeeInfo:
          type: object
          properties:
            isCommittee:
              type: boolean
              description: Indicates if the organization is a committee under Government Code Section 82013.
            committeeDetails:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the recipient committee (if applicable).
                idNumber:
                  type: integer
                  format: int64
                  description: Identification number of the recipient committee.
              description: Details of the committee, if applicable.
          description: Indicates whether the organization qualifies as a committee and provides committee details if applicable.
        amendment:
          $ref: './common-schemas.yaml#/components/schemas/Amendment'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
      required:
        - slateMailerOrganization
        - attestation
        - amendment
        - officers
        - individualsAuthorizingSlateMailers
        - committeeInfo

    SlateMailerOrganizationInformation:
      description: Basic information about the Slate Mailer Organization, including its level of activity and addresses.
      type: object
      properties:
        submittedDate:
          type: string
          format: date
          description: Date submitted
        name:
          type: string
          description: Full name of the Slate Mailer Organization.
        dateQualified:
          type: string
          format: date
          description: Date the organization qualified as a Slate Mailer Organization.
        activityLevel:
          type: string
          enum:
            - City
            - County
            - State
          description: Indicates the organization's primary level of activity (city, county, or state).
        address:
          $ref: './common-schemas.yaml#/components/schemas/OrgAddress'
        mailingAddress:
          $ref: './common-schemas.yaml#/components/schemas/OrgAddress'
        phone:
          type: string
          description: Phone number of the organization as digits only.
          pattern: '^\d+$'
          maxLength: 40
        fax:
          type: string
          description: Optional fax number for the organization as digits only.
          pattern: '^\d+$'
          maxLength: 40
        email:
          type: string
          format: email
          description: email address for the organization.
      required:
        - name
        - activityLevel
        - dateQualified
        - address
        - phone
        - email

