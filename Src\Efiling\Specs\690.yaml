openapi: 3.0.3
info:
  title: Form 690 API for amendment to lobbying disclosure report
  description: API Schema for California Form 690 amendment to lobbying disclosure report
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Disclosure/Amendment/{filerId}:
    post:
      summary: Submit a new Form 690 amendment to lobbying disclosure report
      description: Submit a new Form 690 amendment to lobbying disclosure report
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyingDisclosureAmendment'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyingDisclosureAmendment:
      type: object
      properties:
        originalReportForm:
          type: string
          description: The form number of the original report being amended.
        executionDate:
          type: string
          format: date
          description: Date when the original report was executed.
        reportingPeriod:
          type: object
          properties:
            startDate:
              type: string
              format: date
              description: Start date of the reporting period.
            endDate:
              type: string
              format: date
              description: End date of the reporting period.
        amendments:
          type: object
          properties:
            affectedParts:
              type: array
              items:
                type: string
              description: Parts of the form affected by the amendment.
            affectedSections:
              type: array
              items:
                type: string
              description: Sections of the form affected by the amendment.
            description:
              type: string
              description: Description of the changes being made.
        filingType:
          $ref: './common-schemas.yaml#/components/schemas/FilingType'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
