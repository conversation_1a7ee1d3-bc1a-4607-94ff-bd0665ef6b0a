using System.ComponentModel.DataAnnotations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Model for response body of CandidateRegistrationModel.SearchCandidateByName API.
/// </summary>
public class CandidateSearchResultDto
{
    /// <summary>
    /// Candidate Id
    /// </summary>
    [Required]
    public required long Id { get; set; }

    /// <summary>
    /// Gets or sets the candidate's name.
    /// </summary>
    [Required]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the candidate's last name (used to sort).
    /// </summary>
    [Required]
    public required string LastName { get; set; }

    /// <summary>
    /// Gets or sets the candidate's last election.
    /// </summary>
    [Required]
    public required string LastElection { get; set; }

    /// <summary>
    /// Gets or sets date of the candidate's most recent election (not displayed).
    /// </summary>
    public required DateTime LastElectionDate { get; set; }

    /// <summary>
    /// Gets or sets the candidate's recent registrations.
    /// </summary>
    public List<CandidateSearchRegistrationResultDto> Registrations { get; set; } = new List<CandidateSearchRegistrationResultDto>();
}
