openapi: 3.0.3
info:
  title: Form 615 Lobbyist Report API
  description: API Schema for California Form 615 Lobbyist Report
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Lobbying/Disclosure/Lobbyist/{filerId}:
    post:
      summary: Submit a new Form 615 Lobbyist Report
      description: Submit a new Form 615 Lobbyist Report
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string
            format: uuid

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LobbyistReport'

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

components:
  schemas:
    LobbyistReport:
      type: object
      required:
        - reportingPeriod
        - attestation
        - nothingToReportActivityExpense
        - activityExpenses
        - campaignContributions
        - nothingToReportCampaignContributions
      allOf:
        - if:
          properties:
            nothingToReportActivityExpense:
            const: false
          then:
            required: [activityExpenses]
        - if:
          properties:
            nothingToReportCampaignContributions:
            const: false
          then:
            required: [campaignContributions]
      properties:
        reportingPeriod:
          type: object
          required:
            - startDate
            - endDate
          properties:
            startDate:
              type: string
              format: date
            endDate:
              type: string
              format: date
          description: reporting period start date and end date.       
        amendmentWithExplanation:
          $ref: './common-schemas.yaml#/components/schemas/AmendmentWithExplanation'
        attestation:
          $ref: './common-schemas.yaml#/components/schemas/Attestation'
        contacts:
          type: array
          items:
            $ref: './common-schemas.yaml#/components/schemas/FilerContact'
        nothingToReportActivityExpense:
          type: boolean
          description: Nothing to report for activity expenses.
        activityExpenses:
          type: array
          description: Activity expenses during the reporting period.
          items:
            type: object
            required:
              - date
              - filerContactId
              - isActivityChargedToCreditCard
              - cardName
              - reportablePersons
              - activityExpenseTypeId
              - total
            allOf:
              - if:
                properties:
                  isActivityChargedToCreditCard:
                  const: true
                then:
                  required: [cardName]
            properties:
              date:
                type: string
                format: date
              filerContactId:
                type: string
                description: The id of the contact associated with the activityExpense      
              isActivityChargedToCreditCard:
                type: boolean
                description: If activity is charged to credit card or not.
              cardName:
                type: string
                description: type of card like Bofa, Discover.
              reportablePersons:
                type: array
                items:
                  $ref: '#/components/schemas/ReportablePerson'
              activityExpenseTypeId:
                type: number
                description: Description of services provided, value retrieved from reference api and add correct end point once find
              otherExpenses:
                type: string
                description: Text for other payments or event ticket expenses or travel expenses
              additionalInformation:
                type: string
                description: If more than one payee per activity expense, list the payees in this field.
              total:
                type: number
                description: Total expense amount.
        nothingToReportCampaignContributions:
          type: boolean
          description: Nothing to report for CampaignContributions.
        campaignContributions:
          type: array
          description: Campaign contributions during the period.
          items:
            type: object
            required:
              - date
              - amount
            anyOf:
              - required: [nameOfRecipient]
              - required: [recipientCommitteeID]
            properties:
              date:
                type: string
                format: date
              nonFilerContributorName:
                type: string
              separateAccountName:
                type: string
              nameOfRecipient:
                type: string
              recipientCommitteeID:
                type: string
              amount:
                type: number
    
    ReportablePerson:
      type: object
      required:
        - reportablePersonName
        - officialPositionId
        - agencyId
        - eachItemAmount
      properties:
        reportablePersonName:
          $ref: './common-schemas.yaml#/components/schemas/PersonName'
        officialPositionId:
          type: number
          description: Official position Id
        otherOfficialPosition:
          type: string
          description: Text for other official positions
        agencyId:
          type: number
          description: Agency type id
        otherAgency:
          type: string
          description: Text for other agency
        eachItemAmount:
          type: number
          description: Each item payment

   
