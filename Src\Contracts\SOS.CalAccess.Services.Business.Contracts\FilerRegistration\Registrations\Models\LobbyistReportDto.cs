using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// Represents a single object for complete Candidate Intention Statement
/// (form 501) for submission through the Efile API.
/// </summary>
public class LobbyistReportDto
{
    public LobbyistReport LobbyistReport { get; set; } = new LobbyistReport
    {
        StatusId = FilingStatus.Draft.Id
    };

    public DateTime ReportingStartDate { get; set; }

    public DateTime ReportingEndDate { get; set; }

    public long UserId { get; set; }

    public List<FilerContact> FilerContacts { get; set; } = [];

    public List<ActivityExpense> ActivityExpenses { get; set; } = [];

    public List<LobbyingCampaignContribution> CampaignContributions { get; set; } = [];

    public Attestation Attestation { get; set; } = new();
}
