using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

public class SmoRegistrationController(
    ISmoRegistrationCtlSvc smoRegistrationCtlSvc,
    ISmoRegistrationSvc smoRegistrationSvc,
    IAccuMailValidatorService accuMailValidatorService,
    IStringLocalizer<SharedResources> localizer,
    IToastService toastService,
    IAuthorizationSvc authorizationSvc) : Controller
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    public IActionResult Index()
    {
        return RedirectToAction(nameof(Page01));
    }

    /// <summary>
    /// Handler for opening the form via "View or View and Complete" button
    /// </summary>
    /// <returns></returns>
    public async Task<IActionResult> ViewOrViewAndComplete([Required] long id)
    {
        if (ModelState.IsValid)
        {
            var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_SlateMailerOrganization_View, User, registrationId: id));

            if (isAuthorized)
            {
                return RedirectToAction(nameof(Summary), new { id });
            }
        }

        toastService.Error(CommonResourceConstants.UnauthorizedToAccessPage);

        return RedirectToDashboard();
    }

    /// <summary>
    /// Closes the form without changes and routes the user to the Dashboard.
    /// If FormAction is Cancel, then it will generate the same toast generated when form is cancelled.
    /// This covers the case where a new form is cancelled before saving (no ID to cancel with).
    /// </summary>
    /// <param name="action"></param>
    /// <returns></returns>
    public IActionResult Close(FormAction? action = null)
    {
        if (action == FormAction.Cancel)
        {
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
        }
        return RedirectToDashboard();
    }

    /// <summary>
    /// Cancels the form and routes the user to the Dashboard.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<IActionResult> Cancel(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            await smoRegistrationSvc.CancelSmoRegistration(id);
            toastService.Success(localizer[CommonResourceConstants.ToastCancelled]);
            return RedirectToDashboard();
        }

        return new NotFoundResult();
    }

    public IActionResult CloseOfficer(
    [Required] long id)
    {
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page08), new { id });
        }

        return new NotFoundResult();

    }

    public IActionResult CancelOfficer(
    [Required] long id,
    CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            // Call service to cancel officer

            return RedirectToAction(nameof(Page08), new { id });
        }

        return new NotFoundResult();
    }

    [HttpGet("/SmoRegistration/{registrationId}/EditOfficer/{contactId}")]
    public async Task<IActionResult> EditOfficer(
        [Required] long registrationId,
        [Required] long contactId)
    {
        if (ModelState.IsValid)
        {
            // Redirect to edit treasurer form
            var isTreasurer = await smoRegistrationCtlSvc.IsTreasurer(registrationId, contactId, default);
            if (isTreasurer.GetValueOrDefault())
            {
                return RedirectToAction(nameof(Page07), new { id = registrationId, isEditting = true });
            }

            // Redirect to edit officer form
            return RedirectToAction(nameof(Page10), new { registrationId, contactId });
        }

        return NotFound();
    }

    [HttpDelete("/SmoRegistration/{registrationId}/DeleteOfficer/{contactId}")]
    public async Task<IActionResult> DeleteOfficer(
        [Required] long registrationId,
        [Required] long contactId)
    {
        if (ModelState.IsValid)
        {
            var deletedOfficerResponse = await smoRegistrationSvc.DeleteSmoRegistrationContactsPage06(registrationId, contactId);

            return Ok(deletedOfficerResponse);
        }

        return NotFound();
    }

    [HttpGet("/SmoRegistration/{registrationId:long}/EditIndividualAuthorizer/{id:long}")]
    public async Task<IActionResult> EditIndividualAuthorizer(
        [Required] long registrationId,
        [Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var queryOfficer = await smoRegistrationSvc.GetSmoOfficers(registrationId);

        var contactId = queryOfficer.FirstOrDefault(o => o.Id == id)?.Id ?? 0;

        // Redirect to edit officer form
        return RedirectToAction(nameof(Page13), new { id = registrationId, contactId });
    }

    public IActionResult CloseAuthorizer(
        [Required] long id)
    {
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page12), new { id });
        }

        return new NotFoundResult();
    }

    public IActionResult CancelAuthorizer(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            // Call service to cancel authorizer

            return RedirectToAction(nameof(Page12), new { id });
        }

        return new NotFoundResult();
    }

    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.SmoRegistrationTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new("Filer Portal", "/FilerPortal"),
            new("Slate Mailer", "/SmoRegistration"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.SmoRegistrationOrganization].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.SmoRegistrationOfficers].Value;
        ViewData["ProgressItem3Name"] = "3. " + localizer[ResourceConstants.SmoRegistrationIndividualAuthorizers].Value;
        ViewData["ProgressItem4Name"] = "4. " + localizer[ResourceConstants.SmoRegistrationSubmit].Value;
    }

    /// <summary>
    /// Handler for when user starts to edit a form.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public IActionResult Edit([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        return RedirectToAction(nameof(Page03), new { Id = id });
    }

    #region Page 01 - FR-SMO-Start
    public IActionResult Page01()
    {
        return View();
    }
    #endregion

    #region Page 02 - FR-SMO-FilerInformation-1
    [HttpGet]
    public IActionResult Page02(long? id)
    {
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = id.GetValueOrDefault()
        };

        if (ModelState.IsValid)
        {
            return View(model);
        }
        else
        {
            return RedirectToAction(nameof(Page01));
        }
    }


    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page02(SmoRegistrationDetailsStep01ViewModel? model)
    {
        if (ModelState.IsValid)
        {
            switch (model?.Action)
            {
                case FormAction.Continue:
                    if (model.Id > 0)
                    {
                        return RedirectToAction(nameof(Page03), new { model.Id });
                    }
                    else
                    {
                        return RedirectToAction(nameof(Page03));
                    }
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page01));
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    #endregion

    #region Page 03 - FR-SMO-FilerInformation-2
    /// <summary>
    /// Loader for Page03
    /// </summary>
    /// <returns>Page03 View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Page03(
    long? id,
    CancellationToken cancellationToken = default)
    {
        if (id.HasValue)
        {
            var model = await smoRegistrationCtlSvc.GetPage03ViewModel(id.Value, cancellationToken);

            if (model is not null)
            {
                return View(model);
            }
        }
        return View(new SmoRegistrationStep01ViewModel
        {
            Addresses = new List<AddressViewModel>
            {
                new ()
                {
                    Purpose = CommonConstants.Address.PurposeOrganization,
                    Country = CommonConstants.DefaultCountryUS,
                },
                new ()
                {
                    Purpose = CommonConstants.Address.PurposeMailing,
                    Country = CommonConstants.DefaultCountryUS,
                }
            }
        });
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"</param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Page03(
        SmoRegistrationStep01ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        if (model.IsSameAsOrganizationAddress)
        {
            model.Addresses.Find(x => x.Purpose == CommonConstants.Address.PurposeMailing)?.Clear();
        }

        if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.SaveAndClose:
                return await Page03Save(model);
            case FormAction.Continue:
                return await Page03ContinueAsync(model);
            case FormAction.Previous:
                return await Page03Previous(model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                break;
        }

        return View(model);
    }
    /// <summary>
    /// Handler for Page03 SaveAndClose Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Save(SmoRegistrationStep01ViewModel model)
    {
        await smoRegistrationCtlSvc.Page03Submit(model, ModelState, false);

        if (ModelState.IsValid)
        {
            SetLocalizedToast(CommonResourceConstants.SavedMessage);
            return RedirectToDashboard();
        }
        return View(nameof(Page03), model);
    }
    /// <summary>
    /// Handler for Page03 Previous Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    private async Task<IActionResult> Page03Previous(SmoRegistrationStep01ViewModel model)
    {
        var registrationId = await smoRegistrationCtlSvc.Page03Submit(model, ModelState, false);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page02), new { Id = registrationId });
        }
        return View(nameof(Page03), model);
    }
    /// <summary>
    /// Handler for Page03 Continue Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> Page03ContinueAsync(SmoRegistrationStep01ViewModel model)
    {
        var registrationId = await smoRegistrationCtlSvc.Page03Submit(model, ModelState, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page04), new { Id = registrationId });
        }

        return View(nameof(Page03), model);
    }
    #endregion

    #region Page 04 - FR-SMO-FilerInformation-3

    /// <summary>
    /// Loader for Page04
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Page04 View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Page04([Required] long id, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var newModel = await smoRegistrationCtlSvc.GetPage04ViewModel(id, cancellationToken);

        if (newModel == null)
        {
            return NotFound();
        }

        return View(newModel);
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page04Async(
        SmoRegistrationDetailsStep01ViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page04Save(model);
                case FormAction.Continue:
                    return await Page04Continue(model);
                case FormAction.Previous:
                    return await Page04Previous(model);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }
    /// <summary>
    /// Handler for Page04 Previous Action
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    public async Task<IActionResult> Page04Previous(SmoRegistrationDetailsStep01ViewModel model)
    {
        var registrationId = await smoRegistrationCtlSvc.Page04ContinueSubmit(model, ModelState, false);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page03), new { Id = registrationId });
        }
        return View(nameof(Page04), model);
    }

    private async Task<IActionResult> Page04Save(SmoRegistrationDetailsStep01ViewModel model)
    {
        // Call the service method to process the update
        var registrationId = await smoRegistrationCtlSvc.Page04ContinueSubmit(model, ModelState, false);
        if (ModelState.IsValid && registrationId.HasValue)
        {
            SetLocalizedToast(CommonResourceConstants.SavedMessage);

            return RedirectToDashboard();
        }

        return View(nameof(Page04), model);
    }

    [HttpPost]
    public async Task<IActionResult> Page04Continue(SmoRegistrationDetailsStep01ViewModel model)
    {
        // Call the service method to process the update
        var registrationId = await smoRegistrationCtlSvc.Page04ContinueSubmit(model, ModelState, true);

        // If model state is valid, proceed to Page05 with the registration ID
        if (ModelState.IsValid && registrationId.HasValue)
        {
            return RedirectToAction(nameof(Page05), new { id = registrationId.Value });
        }

        // If there are validation errors, return to the view with the model
        return View(nameof(Page04), model);
    }
    #endregion

    #region Page 05 - FR-SMO-Officers-1

    [HttpGet]
    /// <summary>
    /// Loader for Page05
    /// </summary>
    /// <returns>Page05 View</returns>
    public IActionResult Page05([Required] long? id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            return View(new SmoRegistrationStep02ViewModel { Id = id });
        }
        return NotFound();
    }

    /// <summary>
    /// Handler for all submissions to Page05
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public IActionResult Page05(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page06), new { model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page04), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    #endregion

    #region Page 06 - FR-SMO-Officers-2
    /// <summary>
    /// Loader for Page06
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> Page06(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = await smoRegistrationCtlSvc.GetPage06AndPage07ViewModel(id, cancellationToken);

            if (model is not null)
            {
                return View(model);
            }

            var newModel = new SmoRegistrationStep02ViewModel();
            return View(newModel);
        }
        return NotFound();
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page06(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return await Page06Continue(model, cancellationToken);
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page05), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    private async Task<IActionResult> Page06Continue(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (model.IsUserTreasurer is null)
        {
            ModelState.AddModelError("IsUserTreasurer", string.Format(localizer[ResourceConstants.FieldIsRequired].Value, "This field"));
            return View(nameof(Page06), model);
        }

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page07), new { id = model.Id, isUserTreasurer = model.IsUserTreasurer });
        }
        return View(nameof(Page06), model);
    }
    #endregion

    #region Page 07 - FR-SMO-Officers-3 & FR-SMO-TreasurerInfo
    /// <summary>
    /// Loader for Page07
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page07 View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Page07(
        [Required] long id,
        bool? isUserTreasurer,
        bool isEditting = false,
        CancellationToken cancellationToken = default)
    {
        var viewModel = smoRegistrationCtlSvc.Page07Or10GetEmptyViewModel(id);

        if (!ModelState.IsValid)
        {
            return View(viewModel);
        }

        var model = await smoRegistrationCtlSvc.GetPage06AndPage07ViewModel(id, cancellationToken);

        // If model is null, this is the new registration
        if (model is null)
        {
            // Prefill data if the user select Yes
            if (isUserTreasurer.GetValueOrDefault())
            {
                var prefillData = await smoRegistrationCtlSvc.PagePrefill(id, null, isUserTreasurer, cancellationToken);
                return View(prefillData);
            }

            return View(viewModel);
        }

        // If it's editing, re-populate the data
        if (isEditting)
        {
            return View(model);
        }

        // If user select Yes
        if (isUserTreasurer.GetValueOrDefault())
        {
            // Previous they also is a Treasurer, re-populate data
            if (model.IsUserTreasurer.GetValueOrDefault())
            {
                return View(model);
            }

            // If not, prefill data 
            var prefillData = await smoRegistrationCtlSvc.PagePrefill(id, null, isUserTreasurer, cancellationToken);
            return View(prefillData);
        }

        // If user select No
        // Previous they also is a Treasurer, reset data
        if (model.IsUserTreasurer.GetValueOrDefault())
        {
            return View(viewModel);
        }

        // If not, re-populate data
        return View(model);
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page07(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
            {
                return View(model);
            }

            model.ContactTitle = FilerRole.SlateMailerOrg_Treasurer.Name;
            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page07Save(model, cancellationToken);
                case FormAction.Continue:
                    return await Page07Continue(model, cancellationToken);
                case FormAction.Previous:
                    return await Page07Previous(model, cancellationToken);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> Page07Save(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        await smoRegistrationCtlSvc.Page07Or10Submit(model, ModelState, cancellationToken, false);

        if (ModelState.IsValid)
        {
            SetLocalizedToast(CommonResourceConstants.SavedMessage);

            return RedirectToDashboard();
        }
        return View(nameof(Page07), model);
    }

    private async Task<IActionResult> Page07Continue(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        _ = await smoRegistrationCtlSvc.Page07Or10Submit(model, ModelState, cancellationToken, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page08), new { model.Id });
        }
        return View(nameof(Page07), model);
    }

    private async Task<IActionResult> Page07Previous(
    SmoRegistrationStep02ViewModel model,
    CancellationToken cancellationToken = default)
    {
        _ = await smoRegistrationCtlSvc.Page07Or10Submit(model, ModelState, cancellationToken, false);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page06), new { model.Id });
        }
        return View(nameof(Page07), model);
    }

    #endregion

    #region Page 08 - FR-SMO-Officers-4
    [HttpGet]
    /// <summary>
    /// Loader for Page08
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page08 View with model</returns>
    public async Task<IActionResult> Page08(
        [Required] long id,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = await smoRegistrationCtlSvc.GetPage08ViewModel(id, cancellationToken);

            if (model is not null)
            {
                return View(model);
            }
        }

        return View(new SmoRegistrationStep02ViewModel());
    }

    [HttpPost]
    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Page08 View with model</returns>
    public IActionResult Page08(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page11), new { model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page06), new { model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    #endregion

    #region Page 9 - FR-SMO-Officers-4
    [HttpGet]
    /// <summary>
    /// Loader for Page09
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page09 View with model</returns>
    public IActionResult Page09(long? id)
    {
        if (ModelState.IsValid)
        {
            var model = new SmoRegistrationStep02ViewModel();
            return View(model);
        }
        return View(new SmoRegistrationStep02ViewModel());
    }

    [HttpPost]
    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Page08 View with model</returns>
    public IActionResult Page09(
    SmoRegistrationStep02ViewModel model,
    CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return Page09Continue(model, cancellationToken);
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page08), new { model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private IActionResult Page09Continue(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (model.IsOfficer is null)
        {
            ModelState.AddModelError(
                "IsOfficer",
                string.Format(CultureInfo.InvariantCulture, localizer[ResourceConstants.FieldIsRequired].Value, "This field")
            );
            return View(nameof(Page09), model);
        }

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page10), new { registrationId = model.Id, isOfficer = model.IsOfficer });
        }
        return View(nameof(Page09), model);
    }
    #endregion

    #region Page 10 - FR-SMO-Officers-5
    /// <summary>
    /// Loader for Page10
    /// </summary>
    /// <param name="id"></param>
    /// <param name="isOfficer"></param>
    /// <returns>Page10 View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Page10(
        [Required] long registrationId,
        long? contactId,
        bool? isOfficer,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var prefillData = await smoRegistrationCtlSvc.PagePrefill(registrationId, contactId, isOfficer, cancellationToken);
            if (prefillData != null)
            {
                return View(prefillData);
            }
        }

        return View(smoRegistrationCtlSvc.Page07Or10GetEmptyViewModel(registrationId));
    }

    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Next view to load</returns>
    [HttpPost]
    public async Task<IActionResult> Page10(
        SmoRegistrationStep02ViewModel model,
        CancellationToken cancellationToken)
    {
        if (ModelState.IsValid)
        {
            if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
            {
                return View(model);
            }

            switch (model.Action)
            {
                case FormAction.Submit:
                    return await Page10Save(model, cancellationToken);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<IActionResult> Page10Save(
    SmoRegistrationStep02ViewModel model,
    CancellationToken cancellationToken = default)
    {
        // Add call to save officer
        _ = await smoRegistrationCtlSvc.Page07Or10Submit(model, ModelState, cancellationToken, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page08), new { id = model.Id });
        }
        return View(nameof(Page10), model);
    }
    #endregion

    #region Page 11 - FR-SMO-Authorizers-1
    [HttpGet]
    /// <summary>
    /// Loader for Page09   
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page08 View with model</returns>
    public IActionResult Page11(long? id)
    {
        if (ModelState.IsValid)
        {
            return View(new SmoRegistrationStep03ViewModel());
        }
        var model = new SmoRegistrationStep03ViewModel();
        return View(model);
    }

    [HttpPost]
    public IActionResult Page11(
        SmoRegistrationStep03ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page12), new { model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page08), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }
    #endregion

    #region Page 12 - FR-SMO-Authorizers-2 & FR-SMO-Authorizers-4
    [HttpGet]
    /// <summary>
    /// Loader for Page12
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page08 View with model</returns>
    public async Task<IActionResult> Page12([Required] long id, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var model = await smoRegistrationCtlSvc.GetPage12ViewModel(id, cancellationToken);

            if (model is not null)
            {
                return View(model);
            }
        }

        return View(smoRegistrationCtlSvc.Page13GetEmptyViewModel(id));
    }

    [HttpPost]
    public IActionResult Page12(
        SmoRegistrationStep03ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return RedirectToAction(nameof(Page14), new { model.Id });
                case FormAction.Previous:
                    return RedirectToAction(nameof(Page11), new { id = model.Id });
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }

        return View(model);
    }

    [HttpDelete("/SmoRegistration/{registrationId}/DeleteIndividualAuthorizer/{contactId}")]
    public async Task<JsonResult> DeleteIndividualAuthorizer([FromRoute] long registrationId, [FromRoute] long contactId)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        var model = await smoRegistrationCtlSvc.Page12DeleteIndividualAuthorizer(registrationId, contactId);

        if (model?.IndividualAuthorizersGridModel == null)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        // Make sure the returned result to Ajax always as PascalCase
        var response = new JsonResult(model!.IndividualAuthorizersGridModel, new JsonSerializerOptions());

        return response;
    }

    #endregion

    #region Page 13 - FR-SMO-Authorizers-3
    [HttpGet]
    /// <summary>
    /// Loader for Page13 Edit
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Page08 View with model</returns>
    public async Task<IActionResult> Page13([Required] long id, long? contactId, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            if (contactId is null)
            {
                return View(smoRegistrationCtlSvc.Page13GetEmptyViewModel(id));
            }

            // Prefill data when edit - has contact ID
            var prefillData = await smoRegistrationCtlSvc.GetPage13ViewModel(id, contactId, cancellationToken);
            return View(prefillData);

        }
        return View(smoRegistrationCtlSvc.Page13GetEmptyViewModel(id));
    }

    [HttpPost]
    public async Task<IActionResult> Page13(
           SmoRegistrationStep03ViewModel model,
           CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            if (await accuMailValidatorService.AccuMailValidationHandler(model, ModelState, model.Action == FormAction.Cancel))
            {
                return View(model);
            }

            switch (model.Action)
            {
                case FormAction.SaveAndClose:
                    return await Page13Save(model, cancellationToken);
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    public async Task<IActionResult> Page13Save(
    SmoRegistrationStep03ViewModel model,
    CancellationToken cancellationToken = default)
    {
        await smoRegistrationCtlSvc.Page13Save(model, ModelState, cancellationToken, true);

        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page12), new { model.Id });
        }
        return View(nameof(Page13), model);
    }
    #endregion

    #region Page 14 - FR-SMO-TreasurerAcknowledgement & FR-SMO-TreasuererAcknowledgement-2
    [HttpGet]
    /// <summary>
    /// Loader for Page14
    /// </summary>
    /// <param name="id">ID of registration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Page14 View with model</returns>
    public async Task<IActionResult> Page14(long? id, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var isAuthorized = await authorizationSvc.IsAuthorized(new AuthorizationRequest(Permission.Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment, User, registrationId: id));
            var model = await smoRegistrationCtlSvc.GetPage14ViewModel(id, isAuthorized, cancellationToken);
            return View(model);
        }

        return NotFound();
    }

    [HttpPost]
    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model">Model of step04</param>
    /// <returns>RedirectToAction based on Form Action</returns>
    public async Task<IActionResult> Page14(SmoRegistrationStep04ViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return await Page14Continue(model);
                case FormAction.Previous:
                    return Page14Previous(model);
                case FormAction.Close:
                    return PageClose();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<ActionResult> Page14Continue(SmoRegistrationStep04ViewModel model)
    {
        if (model.IsRequiredOtherAcknowledgements)
        {
            await smoRegistrationCtlSvc.Page14SendForAcknowledgement(model);
            return RedirectToAction(nameof(Page15), new { model.Id });
        }

        // If the current user is Treasurer or Asst. Treasurer
        // Complete the treasurer acknowledgement
        // If there is any other responsible officers has not acknowledged yet, navigate to FR-SMO-TreasurerAcknowledgement-2
        // Otherwise navigate to FR-SMO-Verification-1 or FR-SMO-Verification-2 (Page15)
        if (model.IsTreasurerOrAssistantTreasurer)
        {
            // Complete the treasurer acknowledgement
            var response = await smoRegistrationCtlSvc.Page14Submit(model);

            // If other responsible officers exist, display FR-SMO-TreasurerAcknowledgement-2
            if (response.ResponsibleOfficers!.Count != 0)
            {
                model.IsRequiredOtherAcknowledgements = true;
                model.ResponsibleOfficers = response.ResponsibleOfficers;
                ModelState.Clear();
                return View(nameof(Page14), model);
            }
        }

        return RedirectToAction(nameof(Page15), new { model.Id });
    }

    private ActionResult Page14Previous(SmoRegistrationStep04ViewModel model)
    {
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page12), new { id = model.Id });
        }
        return NotFound();
    }
    #endregion

    #region Page 15 - FR-SMO-Verification-1 & FR-SMO-Verification-2
    [HttpGet]
    /// <summary>
    /// Loader for Page15
    /// </summary>
    /// <param name="id">ID of registration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Page15 View with model</returns>
    public async Task<IActionResult> Page15(long? id, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var isAuthorized = await authorizationSvc.IsAuthorized(new AuthorizationRequest(Permission.Registration_SlateMailerOrganization_Attest, User, registrationId: id));
            var viewModel = await smoRegistrationCtlSvc.GetPage15ViewModel(id, isAuthorized);
            return View(viewModel);
        }

        return NotFound();
    }

    [HttpPost]
    /// <summary>
    /// Handle submissions of this page
    /// </summary>
    /// <param name="model">Model of step04</param>
    /// <returns>RedirectToAction based on Form Action</returns>
    public async Task<IActionResult> Page15(SmoRegistrationStep04ViewModel model)
    {
        if (ModelState.IsValid)
        {
            switch (model.Action)
            {
                case FormAction.Continue:
                    return await Page15Continue(model);
                case FormAction.Previous:
                    return Page15Previous(model);
                case FormAction.Close:
                    return PageClose();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    break;
            }
        }
        return View(model);
    }

    private async Task<ActionResult> Page15Continue(SmoRegistrationStep04ViewModel model)
    {
        // If user has the permission to attest & check the verification box
        // Otherwise, send for attestation
        if (model.IsUserAuthorizedToAttest && model.IsVerificationCertified)
        {
            await smoRegistrationCtlSvc.Page15AttestRegistration(model, ModelState);
        }
        else
        {
            await smoRegistrationCtlSvc.Page15SendForAttestation(model, ModelState);
        }

        if (!ModelState.IsValid)
        {
            return View(nameof(Page15), model);
        }

        return RedirectToAction(nameof(Page16), new { model.Id });
    }

    private ActionResult Page15Previous(
        SmoRegistrationStep04ViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page14), new { id = model.Id });
        }
        return NotFound();
    }

    #endregion

    #region Page 16 - FR-SMO-Confirmation
    [HttpGet]
    /// <summary>
    /// Loader for Page16
    /// </summary>
    /// <param name="id">ID of registration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Page16 View with model</returns>
    public async Task<IActionResult> Page16(long? id, CancellationToken cancellationToken = default)
    {
        if (ModelState.IsValid)
        {
            var viewModel = await smoRegistrationCtlSvc.Page16GetViewModel(id);
            return View(viewModel);
        }

        return NotFound();
    }
    #endregion

    #region FR-01-SMO-View-Only
    /// <summary>
    /// Loader for Summary page
    /// </summary>
    /// <returns>View with model</returns>
    [HttpGet]
    public async Task<IActionResult> Summary(long id)
    {
        var model = await smoRegistrationCtlSvc.SummaryGetViewModel(id);

        if (ModelState.IsValid)
        {
            return View(model);
        }
        return View(model);
    }
    [HttpPost]
    public async Task<IActionResult> Summary([Required] long id, SmoRegistrationSummaryViewModel model)
    {
        if (ModelState.IsValid)
        {
            var data = await smoRegistrationCtlSvc.SummaryGetViewModel(id);

            switch (model.Action)
            {
                case FormAction.Continue:
                    return SummaryContinue(data);
                case FormAction.Close:
                    return RedirectToDashboard();
                default:
                    ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                    return View(model);
            }
        }

        return RedirectToAction(nameof(Summary), new { model.Id });
    }
    private IActionResult SummaryContinue(SmoRegistrationSummaryViewModel model)
    {
        if (model.ShowTreasurerAcknowledgement == true)
        {
            return model.IsAmendment
                ? RedirectToAction(nameof(AmendSmoRegistrationController.Page12), "AmendSmoRegistration", new { model.Id })
                : RedirectToAction(nameof(Page15), new { model.Id });
        }
        else
        {
            return model.IsAmendment
                ? RedirectToAction(nameof(AmendSmoRegistrationController.Page11), "AmendSmoRegistration", new { model.Id })
                : RedirectToAction(nameof(Page14), new { model.Id });
        }
    }

    public async Task<IActionResult> SummaryEdit(long id)
    {
        if (ModelState.IsValid)
        {
            var data = await smoRegistrationCtlSvc.SummaryGetViewModel(id);

            var result = await smoRegistrationCtlSvc.HandleRegistrationEditAsync(id);

            if (result.IsOk())
            {
                return data.IsAmendment
                    ? RedirectToAction(nameof(AmendSmoRegistrationController.Edit), "AmendSmoRegistration", new { id })
                    : RedirectToAction(nameof(Edit), new { id });
            }
        }

        return RedirectToAction(nameof(Summary), new { id });
    }
    #endregion

    #region Shared
    /// <summary>
    /// Set Saved message toast and redirect to dashboard for when screen has "Save and Close" but no posting logic.
    /// </summary>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Json result of committee information</returns>
    public IActionResult PageClose()
    {
        SetLocalizedToast(CommonResourceConstants.SavedMessage);

        return RedirectToDashboard();
    }

    /// <summary>
    /// Search Committee By ID or Committee Name
    /// </summary>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>Json result of committee information</returns>
    [HttpGet]
    public async Task<JsonResult> SearchRecipientCommitteeByIdOrName([FromQuery] string search, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid || string.IsNullOrWhiteSpace(search))
        {
            return new JsonResult(new List<CommitteeSearchResultDto>());
        }

        var result = await smoRegistrationSvc.SearchCommitteeByIdOrName(search);

        return new JsonResult(result, new JsonSerializerOptions { PropertyNamingPolicy = null });
    }

    public void SetLocalizedToast(string localizerKey)
    {
        var savedMessage = localizer[localizerKey]?.Value ?? "";
        TempData["ToastMessage"] = savedMessage;
        TempData["ToastType"] = "e-toast-success";
        TempData["ToastShowCloseButton"] = "true";
        TempData["ToastX"] = "Right";
        TempData["ToastY"] = "Bottom";
    }
    #endregion

}
