﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<RootNamespace>SOS.CalAccess.Services.Common</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="PaymentProcessing\" />
    <Folder Include="PdfGeneration\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Refit" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\SOS.CalAccess.Foundation\SOS.CalAccess.Foundation.csproj" />
    <ProjectReference Include="..\..\SOS.CalAccess.Models\SOS.CalAccess.Models.csproj" />
  </ItemGroup>

</Project>
