// <copyright file="SmoContactRequest.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

/// <summary>
/// SmoContactRequest.
/// </summary>
public sealed record SmoContactRequest
{
    /// <summary>
    /// Gets or sets the SMO Original Id.
    /// </summary>
    public long? OriginalId { get; set; }

    /// <summary>
    /// Gets or sets the Name.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the Email.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the PhoneNumber.
    /// </summary>
    public PhoneNumberDto? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the FaxNumber.
    /// </summary>
    public PhoneNumberDto? FaxNumber { get; set; }

    /// <summary>
    /// Gets or sets the County.
    /// </summary>
    public string? County { get; set; }

    /// <summary>
    /// Gets or sets the OrganizationAddress.
    /// </summary>
    public AddressDto? OrganizationAddress { get; set; }

    /// <summary>
    /// Gets or sets the MailingAddress.
    /// </summary>
    public AddressDto? MailingAddress { get; set; }

    /// <summary>
    /// Gets or sets IsSameAsCandidateAddress.
    /// </summary>
    public bool? IsSameAsOrganizationAddress { get; set; }

    /// <summary>
    /// Gets or sets the CheckRequiredFieldsFlag
    /// </summary>
    public bool CheckRequiredFieldsFlag { get; set; }

}
