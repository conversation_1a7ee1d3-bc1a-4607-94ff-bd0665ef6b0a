using Refit;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;


namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Service interface for managing Slate Mailer Organization (SMO) registration process.
/// Provides methods for creating, retrieving, updating, submitting the registration records.
/// </summary>
public interface ISmoRegistrationSvc
{
    /// <summary>
    /// Creates a new smo registration.
    /// Update Slate Mailer Organization.
    /// </summary>
    /// <param name="request">The request object containing smo contact details.</param>
    /// <returns>Returns a registration response containing status and details of the created statement.</returns>
    [Post("/" + CreateSmoRegistrationPage03Path)]
    Task<RegistrationResponseDto> CreateSmoRegistrationPage03(SmoContactRequest request);
    const string CreateSmoRegistrationPage03Path = "api/Registration/Smo/Page03";

    /// <summary>
    /// Updates an existing SMO registration.
    /// Update Slate Mailer Organization.
    /// </summary>
    /// <param name="request">The request object containing smo contact details.</param>
    /// <returns>Returns a registration response containing status and details of the created statement.</returns>
    [Put("/" + UpdateSmoRegistrationPage03Path)]
    Task<RegistrationResponseDto> UpdateSmoRegistrationPage03(long id, SmoContactRequest request);
    const string UpdateSmoRegistrationPage03Path = "api/Registration/Smo/{id}/Page03";

    /// <param name="id">The unique identifier of the Slate Mailer Organization.</param>
    /// <param name="request">The request object containing updated details.</param>
    /// <returns>Returns a registration response containing the status of the update operation.</returns>
    [Post("/" + UpdateSmoRegistrationPath)]
    Task<RegistrationResponseDto> UpdateSmoRegistration(long id, SlateMailerOrganizationRequest request);
    const string UpdateSmoRegistrationPath = "api/Registration/Smo/{id}";

    /// <summary>
    /// Retrieves an existing RegistrationFiling by ID.
    /// </summary>
    /// <param name="id">The unique identifier of the RegistrationFiling.</param>
    /// <returns>Returns the RegistrationFiling object.</returns>
    [Get("/" + GetRegistrationFilingByIdPath)]
    Task<SmoRegistrationResponseDto?> GetRegistrationFilingById(long id);
    const string GetRegistrationFilingByIdPath = "api/RegistrationFiling/Smo/{id}";

    /// <summary>
    /// Cancel a draft SMO registration
    /// </summary>
    /// <param name="id">Unique identifier for SMO registration record</param>
    /// <returns></returns>
    [Post("/" + CancelSmoRegistrationPath)]
    Task CancelSmoRegistration(long id);
    const string CancelSmoRegistrationPath = "api/Registration/Smo/Cancel/{id}";

    /// <summary>
    /// Searches for committees by Id or Name.
    /// </summary>
    /// <param name="q">The search query string containing the committees name.</param>
    /// <returns>Returns a list of committees matching the search criteria.</returns>
    [Get("/" + SearchCommitteeByIdOrNamePath)]
    Task<IEnumerable<CommitteeSearchResultDto>> SearchCommitteeByIdOrName(string q);
    const string SearchCommitteeByIdOrNamePath = "api/Committee/Search";

    /// <summary>
    /// Gets SMO Authorized Users
    /// </summary>
    /// <param name="id">Smo Registration Filing Id</param>
    /// <returns></returns>
    [Get("/" + GetSmoTreasurerPage02Path)]
    Task<SmoRegistrationContactDto?> GetSmoTreasurerPage02(long id);
    const string GetSmoTreasurerPage02Path = "api/Registration/Smo/{id}/TreasurerPage02";

    /// <summary>
    /// Gets the smo registration contacts
    /// </summary>
    /// <param name="id">Unique identifier for SMO registration record</param>
    /// <returns>Returns a list of registration contacts.</returns>
    [Get("/" + GetSmoRegistrationContactsPage04Path)]
    Task<IEnumerable<SmoRegistrationContactDto>> GetSmoRegistrationContactsPage04(long id);
    const string GetSmoRegistrationContactsPage04Path = "api/Registration/Smo/{id}/ContactsPage04";

    /// <summary>
    /// Saves the SMO Officer as a registration contact
    /// </summary>
    /// <param name="id">Registration Id</param>
    /// <param name="request">SMO Officer Contact Information</param>
    /// <param name="isCreateOrUpdateAuthorizer">Flag to indicate the flow is create/update authorizer</param>
    /// <returns>SMO registration response object</returns>
    /// <exception cref="NotImplementedException"></exception>
    [Post("/" + PostSmoRegistrationContactsPage05Path)]
    Task<RegistrationResponseDto> PostSmoRegistrationContactsPage05(long id, SmoRegistrationContactDto request, bool isCreateOrUpdateAuthorizer = false);
    const string PostSmoRegistrationContactsPage05Path = "api/Registration/Smo/{id}/ContactsPage05";

    /// <summary>
    /// Transfer an existing officer to treasurer
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [Post("/" + PostSmoRegistrationTransferTreasurerPath)]
    Task<RegistrationResponseDto> PostSmoRegistrationTransferTreasurer(long id, SmoTreasurerTransferDto request);
    const string PostSmoRegistrationTransferTreasurerPath = "api/Registration/Smo/{id}/TreasurerTransferPage06";

    /// <summary>
    /// Removes a registration contact.  Only allow officers to be removed.
    /// </summary>
    /// <param name="registrationId">Registration Id</param>
    /// <param name="contactId">RegistrationRegistrationContact Id</param>
    /// <returns></returns>
    [Delete("/" + DeleteSmoRegistrationContactsPage06Path)]
    Task<RegistrationResponseDto> DeleteSmoRegistrationContactsPage06(long registrationId, long contactId);
    const string DeleteSmoRegistrationContactsPage06Path = "api/Registration/Smo/{registrationId}/ContactsPage06/{contactId}";

    /// <summary>
    /// Removes SMO Authorization from a registration contact.  If user is an authorized user, remove the record.
    /// </summary>
    /// <param name="registrationId">Registration Id</param>
    /// <param name="contactId">RegistrationRegistrationContact Id</param>
    /// <returns></returns>
    [Delete("/" + DeleteSmoAuthorizerPage04Path)]
    Task<RegistrationResponseDto> DeleteSmoAuthorizerPage04(long registrationId, long contactId);
    const string DeleteSmoAuthorizerPage04Path = "api/Registration/Smo/{registrationId}/AuthorizerPage04/{contactId}";

    /// <summary>
    /// Gets SMO Authorized Users
    /// </summary>
    /// <param name="id">Smo Registration Filing Id</param>
    /// <returns></returns>
    [Get("/" + GetSmoAuthorizerPage04Path)]
    Task<IEnumerable<SmoRegistrationContactDto>> GetSmoAuthorizerPage04(long id);
    const string GetSmoAuthorizerPage04Path = "api/Registration/Smo/{id}/AuthorizerPage04";


    /// <summary>
    /// Get Linked Recipient Committee of by filer ID
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <returns>Linked Recipient Committee object</returns>
    [Get("/" + GetLinkedRecipientCommitteePath)]
    Task<CommitteeSearchResultDto?> GetLinkedRecipientCommitteeAsync(long filerId);
    const string GetLinkedRecipientCommitteePath = "api/Registration/Smo/Filer/{filerId}/RecipientCommittee";

    /// <summary>
    /// Update role of a filer user in a SMO registration
    /// </summary>
    /// <param name="id">ID of registration</param>
    /// <param name="request">The request containing filer information</param>
    /// <returns></returns>
    [Put("/" + UpdateFilerRolePath)]
    Task UpdatePrimaryFilerUserRoleAsync(long id, UpdateFilerUserRoleRequest request);
    const string UpdateFilerRolePath = "api/Registration/Smo/{id}/Filer/FilerUser";

    /// <summary>
    /// Complete the treasurer acknowledgement statement
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <param name="executedOn">The date that the contact signed the statement</param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException">When the SMO registration is not found</exception>
    /// <exception cref="InvalidOperationException">Any other issue occurred during the process</exception>
    /// <exception cref="UnauthorizedAccessException">When the user does not have enough permission to complete the acknowledgement statement</exception>
    [Patch("/" + CompleteTreasurerAcknowledgementPath)]
    Task CompleteTreasurerAcknowledgementAsync(long id);
    const string CompleteTreasurerAcknowledgementPath = "api/Registration/Smo/{id}/TreasurerAcknowledgement";

    /// <summary>
    /// Get a collection of SMO registration contact that has not signed/completed the acknowledgement statement
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns>A collection of SMO registration contact</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Get("/" + GetTreasurerAcknowledgementContactsAsyncPath)]
    Task<TreasurerAcknowledgementContactResponseDto> GetTreasurerAcknowledgementContactsAsync(long id);
    const string GetTreasurerAcknowledgementContactsAsyncPath = "api/Registration/Smo/{id}/TreasurerAcknowledgementContacts";

    /// <summary>
    ///  Submits an SMO registration for Efile and completes the registration
    /// </summary>
    /// <param name="submission">Registration class</param>
    [Post(SubmitSmoRegistrationForEfilePath)]
    Task<RegistrationResponseDto> SubmitSmoRegistrationForEfile(EfileSlateMailerOrganizationDto submission);
    const string SubmitSmoRegistrationForEfilePath = "/api/Registration/Smo/Submit";

    /// <summary>
    ///  Submits an SMO registration for Efile and completes the registration
    /// </summary>
    /// <param name="submission">Registration class</param>
    [Post(SmoTerminationForEfilePath)]
    Task<RegistrationResponseDto> SmoTerminationForEfile(EfileSlateMailerOrganizationDto submission);
    const string SmoTerminationForEfilePath = "/api/Registration/Smo/Terminate";

    /// <summary>
    /// Send notifications to who has not completed the acknowledgement statement
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns></returns>
    [Post("/" + SendAcknowledgementNotificationsPath)]
    Task SendAcknowledgementNotificationsAsync(long id);
    const string SendAcknowledgementNotificationsPath = "api/Registration/Smo/{id}/AcknowledgementNotifications";

    /// <summary>
    /// Get SMO Officers by Registration Id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Get("/" + GetSmoOfficersPath)]
    Task<IEnumerable<SmoOfficerGridDto>> GetSmoOfficers(long id);
    const string GetSmoOfficersPath = "api/Registration/Smo/{id}/Officers";

    /// <summary>
    /// Get SMO Officer details by Registration Id and Contact Id
    /// </summary>
    /// <param name="registrationId"></param>
    /// <param name="contactId"></param>
    /// <returns></returns>
    [Get("/" + GetSmoOfficerPath)]
    Task<SmoRegistrationContactDto> GetSmoOfficer(long registrationId, long contactId);
    const string GetSmoOfficerPath = "api/Registration/Smo/{registrationId}/Officers/{contactId}";

    /// <summary>
    /// Deletes an individual authorizer by ID.
    /// </summary>
    /// <param name="registrationId">The ID of the SMO registration.</param>
    /// <param name="contactId">The ID of the individual authorizer.</param>
    /// <returns></returns>
    [Delete("/" + DeleteIndividualAuthorizerPath)]
    Task DeleteIndividualAuthorizer(long registrationId, long contactId);
    const string DeleteIndividualAuthorizerPath = "api/Registration/Smo/{registrationId}/IndividualAuthorizer/{contactId}";

    /// <summary>
    /// Attest a SMO registration
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException">When the SMO registration is not found</exception>
    /// <exception cref="InvalidOperationException">Any other issue occurred during the process</exception>
    /// <exception cref="UnauthorizedAccessException">When the user does not have enough permission to attest the registration</exception>
    [Post("/" + AttestRegistrationAsyncPath)]
    Task<RegistrationResponseDto> AttestRegistrationAsync(long id);
    const string AttestRegistrationAsyncPath = "api/Registration/Smo/{id}/Attest";

    /// <summary>
    /// Get a collection of SMO registration contact that is responsible for Attestation
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns>A collection of SMO registration contact</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Get("/" + GetResponsibleOfficerContactsAsyncPath)]
    Task<IEnumerable<SmoRegistrationContactDto>> GetResponsibleOfficerContactsAsync(long id);
    const string GetResponsibleOfficerContactsAsyncPath = "api/Registration/Smo/{id}/ResponsibleOfficerContacts";

    /// <summary>
    /// Get the attestation of a SMO registration
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns>The attestation information of a SMO registration</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Get("/" + GetRegistrationAttestationAsyncPath)]
    Task<SmoRegistrationAttestationResponseDto> GetRegistrationAttestationAsync(long id);
    const string GetRegistrationAttestationAsyncPath = "api/Registration/Smo/{id}/Attestation";

    /// <summary>
    /// Send attestation notifications to who selected from responsible officers
    /// </summary>
    /// <param name="request">Request for sending attestation</param>
    /// <returns>Registration response object</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Post("/" + SendRegistrationForAttestationAsyncPath)]
    Task<RegistrationResponseDto> SendRegistrationForAttestationAsync(long id, SmoRegistrationSendForAttestationRequest request);
    const string SendRegistrationForAttestationAsyncPath = "api/Registration/Smo/{id}/AttestationNotifications";

    /// <summary>
    /// Get pending items of a registration
    /// </summary>
    /// <param name="id">Registration ID</param>
    /// <returns>A collection of pending items</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Get("/" + GetPendingItemsAsyncPath)]
    Task<List<PendingItemDto>> GetPendingItemsAsync(long id);
    const string GetPendingItemsAsyncPath = "api/Registration/Smo/{id}/PendingItems";

    /// <summary>
    /// Create a amendment/new registration
    /// </summary>
    /// <param name="id">Previous/Parent registration ID</param>
    /// <returns>SMO ammendment registration object</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Post("/" + CreateSmoAmendmentRegistrationAsyncPath)]
    Task<SmoRegistrationResponseDto> CreateSmoAmendmentRegistrationAsync(long id);
    const string CreateSmoAmendmentRegistrationAsyncPath = "api/Registration/Smo/{id}/Amendment";

    /// <summary>
    /// Get General Information about the SMO registration
    /// </summary>
    /// <param name="id">ID of a SMO registration</param>
    /// <returns>Details for the SMO registration</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the SMO registration is not found</exception>
    [Get("/" + GetSmoGeneralInformationAsyncPath)]
    Task<SmoGeneralInformationResponseDto> GetSmoGeneralInformationAsync(long id);
    const string GetSmoGeneralInformationAsyncPath = "api/Registration/Smo/{id}/GeneralInformation";

    /// <summary>
    /// Search SMO registration by ID or name and status
    /// </summary>
    /// <param name="query">Text to compare</param>
    /// <param name="statusId">Status of registration</param>
    /// <returns>A collection of SMO registration dto</returns>
    [Get("/" + SearchSmoRegistrationByIdOrNameAsyncPath)]
    Task<List<SmoRegistrationBasicResponseDto>> SearchSmoRegistrationByIdOrNameAsync(string query, long? statusId);
    const string SearchSmoRegistrationByIdOrNameAsyncPath = "api/Registration/Smo/Search";

    /// <summary>
    /// Update notice of termination of a SMO registration
    /// </summary>
    /// <param name="id">SMO registration ID</param>
    /// <param name="isTerminating">Flag to indicate this SMO registration is terminating</param>
    /// <returns></returns>
    [Patch("/" + UpdateNoticeOfTerminationSmoRegistrationAsyncPath)]
    Task<RegistrationResponseDto> UpdateNoticeOfTerminationSmoRegistrationAsync(long id, NoticeOfTerminationRequest request);
    const string UpdateNoticeOfTerminationSmoRegistrationAsyncPath = "api/Registration/Smo/{id}/NoticeOfTermination";

    /// <summary>
    /// Checks if the SMO registration is terminating.
    /// </summary>
    /// <param name="id">The ID of the SMO registration to check.</param>
    /// <returns>A boolean indicating the current termination status of the SMO registration.</returns>
    [Get("/" + IsRegistrationTerminatingAsyncPath)]
    Task<bool> IsRegistrationTerminatingAsync(long id);
    const string IsRegistrationTerminatingAsyncPath = "api/Registration/Smo/{id}/IsTerminating";

    /// <summary>
    /// Search officers of the latest accepted SMO registration by Name
    /// </summary>
    /// <param name="filerId">Filer ID of SMO registration</param>
    /// <param name="query">Text to compare</param>
    /// <returns>A collection of officer information (max 5 records)</returns>
    [Get("/" + SearchLatestAcceptedSmoRegistrationOfficerByNameAsyncPath)]
    Task<List<SmoRegistrationContactDto>> SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(long filerId, string query);
    const string SearchLatestAcceptedSmoRegistrationOfficerByNameAsyncPath = "api/Registration/Smo/Filers/{filerId}/Officers/Search";


    /// <summary>
    /// Gets the current user role for a registration
    /// </summary>
    /// <param name="id">ID of SMO registration</param>
    /// <returns>The current user role id for this registration</returns>
    [Get("/" + GetCurrentUserRoleByIdAsyncPath)]
    Task<long> GetCurrentUserRoleByIdAsync(long id);
    const string GetCurrentUserRoleByIdAsyncPath = "api/Registration/Smo/{id}/CurrentUserRole";

    /// <summary>
    /// Gets the summary data for a SMO registration filing
    /// </summary>
    /// <param name="id">ID of the SMO registration</param>
    /// <returns>Filing summary details for this SMO registration</returns>
    [Get("/" + GetSmoRegistrationFilingSummaryPath)]
    Task<SmoRegistrationFilingSummaryDto> GetSmoRegistrationFilingSummary(long id);
    const string GetSmoRegistrationFilingSummaryPath = "api/Registration/Smo/{id}/FilingSummary";

    /// <summary>
    /// Handles editing logic for a SMO registration filing (e.g. moves it to draft, sends notification).
    /// </summary>
    /// <param name="id">ID of the SMO registration</param>
    /// <returns>A task representing the asynchronous operation</returns>
    [Post("/" + HandleRegistrationEditAsyncPath)]
    Task HandleRegistrationEditAsync(long id);
    const string HandleRegistrationEditAsyncPath = "api/Registration/Smo/{id}/HandleEdit";
}
