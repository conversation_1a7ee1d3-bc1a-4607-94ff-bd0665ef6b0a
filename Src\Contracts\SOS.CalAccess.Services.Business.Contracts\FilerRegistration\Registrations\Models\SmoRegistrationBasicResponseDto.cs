using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

public class SmoRegistrationBasicResponseDto
{
    /// <summary>
    /// Gets or sets ID of SMO registration
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets name of SMO
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// Gets or sets email of SMO
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// Gets or sets ID of SMO registration status
    /// </summary>
    public long StatusId { get; set; }

    /// <summary>
    /// Gets or sets ID of SMO registration filer
    /// </summary>
    public long? FilerId { get; set; }

    public static SlateMailerOrganization MapToEntity(SmoRegistrationBasicResponseDto dto)
    {
        return new SlateMailerOrganization
        {
            Id = dto.Id,
            Name = dto.Name,
            Email = dto.Email,
            StatusId = dto.StatusId,
            FilerId = dto.FilerId,
        };
    }

    public static SmoRegistrationBasicResponseDto MapToDto(SlateMailerOrganization entity)
    {
        return new SmoRegistrationBasicResponseDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Email = entity.Email,
            StatusId = entity.StatusId,
            FilerId = entity.FilerId,
        };
    }
}
