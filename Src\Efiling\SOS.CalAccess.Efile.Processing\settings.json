{"AccuMail": {"Host": "http://accumail01.sos.ca.gov", "Path": "/accumailgold_addressvalidation.asmx/ValidateAddress?"}, "Authorization": {"Enabled": true}, "EmailMessaging": {"FromEmailAddress": "<EMAIL>"}, "IsEncrypted": false, "SmsMessaging": {"FromPhoneNumber": "+18333705276"}, "Values": {}, "AzureValues": {"BlobStorageEndpoint": "https://azdevblobstorage.blob.core.usgovcloudapi.net", "ServiceBusNamespace": "sb://sb-cal-dev.servicebus.usgovcloudapi.net", "QueueName": "efileapi-request-small"}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=CARS;Trusted_Connection=True;TrustServerCertificate=true"}, "Decisions": {"Host": "http://devcaldcs01:8080/Primary/restapi/Flow/", "SessionId": "NS-01JFJZH87PSBE9D2H8GZK4VH9X"}, "logging": {"logLevel": {"Default": "Information", "Microsoft": "Warning", "Host": "Error", "Function": "Error", "Host.Aggregator": "Information"}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 20, "excludedTypes": "Request;Exception"}, "enableLiveMetricsFilters": true}}}