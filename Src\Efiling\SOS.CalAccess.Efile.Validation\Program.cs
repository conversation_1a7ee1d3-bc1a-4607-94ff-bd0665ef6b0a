using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SOS.CalAccess.Efile.Validation;

IHost host = new HostBuilder()
    .ConfigureAppConfiguration((context, config) =>
    {
        string environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Development";
        bool isDevelopment = environment.Equals("Development", StringComparison.OrdinalIgnoreCase);
        if (isDevelopment)
        {
            config.SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("local.settings.json", optional: false, reloadOnChange: true);
        }

        config.AddEnvironmentVariables();
    })
    .ConfigureFunctionsWebApplication()
    .ConfigureServices((context, services) =>
    {
        services.AddApplicationInsightsTelemetryWorkerService(options =>
        {
            options.ConnectionString = Environment.GetEnvironmentVariable("AppInsights_ConnectionString");
        });
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        DependencyInjectionConfiguration setupDI = new(context, services);
        setupDI.SetupAuthentication();
        setupDI.AddRepositories();
        setupDI.AddCommonServices();
        setupDI.AddBusinessServices();
        setupDI.AddMaplightAcceleratorClasses();
        services.ConfigureFunctionsApplicationInsights();
        services.AddHttpClient();
    })
    .Build();

host.Run();
