using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Services.Common.DataValidation.Models;

namespace SOS.CalAccess.Services.Common.DataValidation;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The IJsonSchemaValidationSvc provides methods to support validating that a provided json message meets a defined schema.
/// </p>
/// <p>
/// Since CARS may need to process large JSON documents, the service provides methods for performing the validation on a stream in order to avoid needing to load
/// the entire document into memory while performing the validation.
/// </p>
/// <p>
/// Schemas are stored into the database and referenced by a unique identifier
/// </p>
/// <p>
/// In terms of Architecture Design this translates to a backend common service invoked by the business services layer
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this service is to validate if an incoming api request is properly formatted and to provide feedback on what errors exist and where they are in the request
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>BCS2 Data Validation</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IJsonSchemaRepository          | GetById                        | Get json schema by id        |
#endregion
public interface IJsonSchemaValidationSvc
{
    /// <summary>
    /// Evaluate the json and validate that it meets the specifications defined by the JSON schema as the data is read from the input stream
    /// </summary>
    /// <param name="schemaId"></param>
    /// <param name="inputStream"></param>
    /// <returns>List of validation results</returns>
    ///
    /// \msc
    /// Actor, IJsonSchemaValidationSvc, IJsonSchemaRepository;
    /// Actor => IJsonSchemaValidationSvc [label="ValidateJsonStream()"];
    /// IJsonSchemaValidationSvc => IJsonSchemaRepository [label="GetById()"];
    /// IJsonSchemaRepository >> IJsonSchemaValidationSvc [label="\nreturn "];
    /// IJsonSchemaValidationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    Task<List<JsonSchemaValidationResult>> ValidateJsonStream(string schemaId, Stream inputStream);

    /// <summary>
    /// Evaluate the json and validate that it meets the specifications defined by the JSON schema as the data is copied from the input stream to the output stream
    /// </summary>
    /// <param name="schemaId"></param>
    /// <param name="inputStream"></param>
    /// <param name="outputStream"></param>
    /// <returns>List of validation results</returns>
    /// <exception cref="ArgumentNullException"></exception>
    /// <exception cref="ArgumentException"></exception>
    ///
    /// \msc
    /// Actor, IJsonSchemaValidationSvc, IJsonSchemaRepository;
    /// Actor => IJsonSchemaValidationSvc [label="ValidateJsonStream()"];
    /// IJsonSchemaValidationSvc => IJsonSchemaRepository [label="GetById()"];
    /// IJsonSchemaRepository >> IJsonSchemaValidationSvc [label="\nreturn "];
    /// IJsonSchemaValidationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    Task<List<JsonSchemaValidationResult>> ValidateJsonStream(string schemaId, Stream inputStream, Stream outputStream);

    /// <summary>
    /// Evaluate the json and validate that it meets the specifications defined by the JSON schema.
    /// </summary>
    /// <param name="json">The JSON string we're validating</param>
    /// <param name="formName">The form Name (ex. "Campaign-Statement-CandidateIntention")</param>
    /// <returns>List of validation errors. An empty list indicates that validation was successful.</returns>
    ///
    /// \msc
    /// Actor, IJsonSchemaValidationSvc, IJsonSchemaRepository;
    /// Actor => IJsonSchemaValidationSvc [label="ValidateJson()"];
    /// IJsonSchemaValidationSvc => IJsonSchemaRepository [label="GetById()"];
    /// IJsonSchemaRepository >> IJsonSchemaValidationSvc [label="\nreturn "];
    /// IJsonSchemaValidationSvc >> Actor [label="\nreturn "];
    /// \endmsc
    Task<List<ApiError>> ValidateJson(string json, string formName);
}
