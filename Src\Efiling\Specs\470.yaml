openapi: 3.0.3
info:
  title: Form 470 - Officeholder and Candidate Campaign Statement API
  description: API for submitting and managing Form 470 campaign statements (Short Form and Supplement).
  version: 0.1.0
externalDocs:
  description: OpenAPI Specification
  url: https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.3.md
servers:
  - url: https://tbd/api/v1

paths:
  /Campaign/Disclosure/Candidate/CampaignStatement/Short/{filerId}:
    post:
      summary: Submit Form 470 - Campaign Statement (Short Form)
      description: Submit Form 470, including officeholder and candidate information, office sought or held, election details, and declaration.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                statementFilingPeriodId:
                  type: number
                  format: int64
                  description: Filing Period identification number for the statement.
                committeeDetails:
                  type: array
                  items:
                    type: object
                    properties:
                      committeeId:
                        type: number
                        format: int64
                        description: Committee identification number.
                attestation:
                  $ref: './common-schemas.yaml#/components/schemas/Attestation'
                amendment:
                  $ref: './common-schemas.yaml#/components/schemas/Amendment'
              required:
                - statementFilingPeriodId
                - attestation

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'

  /Campaign/Disclosure/Candidate/CampaignStatement/Supplement/{filerId}:
    post:
      summary: Submit Form 470 - Campaign Statement (Supplement)
      description: Submit Form 470 Supplement when contributions or expenditures total $2,000 or more.
      parameters:
        - name: filerId
          in: path
          description: ID of filer
          required: true
          schema:
            type: string

      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dateThresholdReached:
                  type: string
                  format: date
                  description: Date contributions totaling $2,000 or more were received or expenditures of $2,000 or more were made.
                committeeDetails:
                  type: array
                  items:
                    type: object
                    properties:
                      committeeId:
                        type: number
                        format: integer
                        description: Committee identification number.
                attestation:
                  $ref: './common-schemas.yaml#/components/schemas/Attestation'
                amendment:
                  $ref: './common-schemas.yaml#/components/schemas/Amendment'
              required:
                - dateThresholdReached
                - attestation

      responses:
        '200':
          $ref: './common-schemas.yaml#/components/responses/OK'
        '400':
          $ref: './common-schemas.yaml#/components/responses/BadRequest'
        '401':
          $ref: './common-schemas.yaml#/components/responses/Unauthorized'
        '500':
          $ref: './common-schemas.yaml#/components/responses/InternalServerError'
